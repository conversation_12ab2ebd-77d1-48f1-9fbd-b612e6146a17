"use strict";var D=Object.create;var T=Object.defineProperty;var K=Object.getOwnPropertyDescriptor;var M=Object.getOwnPropertyNames;var q=Object.getPrototypeOf,z=Object.prototype.hasOwnProperty;var G=(e,r)=>{for(var t in r)T(e,t,{get:r[t],enumerable:!0})},E=(e,r,t,a)=>{if(r&&typeof r=="object"||typeof r=="function")for(let n of M(r))!z.call(e,n)&&n!==t&&T(e,n,{get:()=>r[n],enumerable:!(a=K(r,n))||a.enumerable});return e};var J=(e,r,t)=>(t=e!=null?D(q(e)):{},E(r||!e||!e.__esModule?T(t,"default",{value:e,enumerable:!0}):t,e)),Q=e=>E(T({},"__esModule",{value:!0}),e);var re={};G(re,{Animated:()=>h,AnimatedArray:()=>g,AnimatedObject:()=>p,AnimatedString:()=>m,AnimatedValue:()=>d,createHost:()=>te,getAnimated:()=>v,getAnimatedType:()=>Y,getPayload:()=>k,isAnimated:()=>S,setAnimated:()=>N});module.exports=Q(re);var O=require("@react-spring/shared"),b=Symbol.for("Animated:node"),S=e=>!!e&&e[b]===e,v=e=>e&&e[b],N=(e,r)=>(0,O.defineHidden)(e,b,r),k=e=>e&&e[b]&&e[b].getPayload(),h=class{constructor(){N(this,this)}getPayload(){return this.payload||[]}};var P=require("@react-spring/shared");var d=class extends h{constructor(t){super();this._value=t;this.done=!0;this.durationProgress=0;P.is.num(this._value)&&(this.lastPosition=this._value)}static create(t){return new d(t)}getPayload(){return[this]}getValue(){return this._value}setValue(t,a){return P.is.num(t)&&(this.lastPosition=t,a&&(t=Math.round(t/a)*a,this.done&&(this.lastPosition=t))),this._value===t?!1:(this._value=t,!0)}reset(){let{done:t}=this;this.done=!1,P.is.num(this._value)&&(this.elapsedTime=0,this.durationProgress=0,this.lastPosition=this._value,t&&(this.lastVelocity=null),this.v0=null)}};var x=require("@react-spring/shared"),m=class extends d{constructor(t){super(0);this._string=null;this._toString=(0,x.createInterpolator)({output:[t,t]})}static create(t){return new m(t)}getValue(){let t=this._string;return t??(this._string=this._toString(this._value))}setValue(t){if(x.is.str(t)){if(t==this._string)return!1;this._string=t,this._value=1}else if(super.setValue(t))this._string=null;else return!1;return!0}reset(t){t&&(this._toString=(0,x.createInterpolator)({output:[this.getValue(),t]})),this._value=0,super.reset()}};var U=require("@react-spring/shared");var u=require("@react-spring/shared");var V={dependencies:null};var p=class extends h{constructor(t){super();this.source=t;this.setValue(t)}getValue(t){let a={};return(0,u.eachProp)(this.source,(n,s)=>{S(n)?a[s]=n.getValue(t):(0,u.hasFluidValue)(n)?a[s]=(0,u.getFluidValue)(n):t||(a[s]=n)}),a}setValue(t){this.source=t,this.payload=this._makePayload(t)}reset(){this.payload&&(0,u.each)(this.payload,t=>t.reset())}_makePayload(t){if(t){let a=new Set;return(0,u.eachProp)(t,this._addToPayload,a),Array.from(a)}}_addToPayload(t){V.dependencies&&(0,u.hasFluidValue)(t)&&V.dependencies.add(t);let a=k(t);a&&(0,u.each)(a,n=>this.add(n))}};var g=class extends p{constructor(r){super(r)}static create(r){return new g(r)}getValue(){return this.source.map(r=>r.getValue())}setValue(r){let t=this.getPayload();return r.length==t.length?t.map((a,n)=>a.setValue(r[n])).some(Boolean):(super.setValue(r.map(X)),!0)}};function X(e){return((0,U.isAnimatedString)(e)?m:d).create(e)}var _=require("@react-spring/shared");function Y(e){let r=v(e);return r?r.constructor:_.is.arr(e)?g:(0,_.isAnimatedString)(e)?m:d}var f=require("@react-spring/shared");var j=J(require("react")),c=require("react"),o=require("@react-spring/shared");var R=(e,r)=>{let t=!o.is.fun(e)||e.prototype&&e.prototype.isReactComponent;return(0,c.forwardRef)((a,n)=>{let s=(0,c.useRef)(null),i=t&&(0,c.useCallback)(l=>{s.current=ee(n,l)},[n]),[y,L]=Z(a,r),$=(0,o.useForceUpdate)(),w=()=>{let l=s.current;if(t&&!l)return;(l?r.applyAnimatedValues(l,y.getValue(!0)):!1)===!1&&$()},C=new F(w,L),A=(0,c.useRef)();(0,o.useIsomorphicLayoutEffect)(()=>(A.current=C,(0,o.each)(L,l=>(0,o.addFluidObserver)(l,C)),()=>{A.current&&((0,o.each)(A.current.deps,l=>(0,o.removeFluidObserver)(l,A.current)),o.raf.cancel(A.current.update))})),(0,c.useEffect)(w,[]),(0,o.useOnce)(()=>()=>{let l=A.current;(0,o.each)(l.deps,H=>(0,o.removeFluidObserver)(H,l))});let B=r.getComponentProps(y.getValue());return j.createElement(e,{...B,ref:i})})},F=class{constructor(r,t){this.update=r;this.deps=t}eventObserved(r){r.type=="change"&&o.raf.write(this.update)}};function Z(e,r){let t=new Set;return V.dependencies=t,e.style&&(e={...e,style:r.createAnimatedStyle(e.style)}),e=new p(e),V.dependencies=null,[e,t]}function ee(e,r){return e&&(o.is.fun(e)?e(r):e.current=r),r}var I=Symbol.for("AnimatedComponent"),te=(e,{applyAnimatedValues:r=()=>!1,createAnimatedStyle:t=n=>new p(n),getComponentProps:a=n=>n}={})=>{let n={applyAnimatedValues:r,createAnimatedStyle:t,getComponentProps:a},s=i=>{let y=W(i)||"Anonymous";return f.is.str(i)?i=s[i]||(s[i]=R(i,n)):i=i[I]||(i[I]=R(i,n)),i.displayName=`Animated(${y})`,i};return(0,f.eachProp)(e,(i,y)=>{f.is.arr(e)&&(y=W(i)),s[y]=s(i)}),{animated:s}},W=e=>f.is.str(e)?e:e&&f.is.str(e.displayName)?e.displayName:f.is.fun(e)&&e.name||null;
//# sourceMappingURL=react-spring_animated.production.min.cjs.map