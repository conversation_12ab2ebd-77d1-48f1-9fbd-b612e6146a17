import { ThreeErrorBoundary } from './components/ErrorBoundary';
import { Canvas3D } from './components/Canvas3D';
import { SimpleHero3D } from './components/SimpleHero3D';
import { HeroSection } from './components/HeroSection';

function App() {
  return (
    <div className="min-h-screen bg-brand-background">
      {/* Hero Section with 3D Background */}
      <ThreeErrorBoundary>
        <div className="relative h-screen">
          <Canvas3D className="absolute inset-0">
            <SimpleHero3D />
          </Canvas3D>

          {/* Hero Content Overlay */}
          <HeroSection />
        </div>
      </ThreeErrorBoundary>

      {/* Foundation status section */}
      <section className="section-spacing container-padding max-container">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-semantic-text mb-4">
            Foundation Setup Complete
          </h2>
          <p className="text-semantic-textSecondary max-w-2xl mx-auto">
            The 3D portfolio foundation is now ready with performance monitoring,
            WebGL detection, error boundaries, and responsive design system.
          </p>

          <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="glass-effect rounded-lg p-6">
              <h3 className="font-semibold text-semantic-text mb-2">Performance Optimized</h3>
              <p className="text-sm text-semantic-textMuted">
                60fps target with automatic quality degradation
              </p>
            </div>

            <div className="glass-effect rounded-lg p-6">
              <h3 className="font-semibold text-semantic-text mb-2">WebGL Detection</h3>
              <p className="text-sm text-semantic-textMuted">
                Graceful fallback for unsupported devices
              </p>
            </div>

            <div className="glass-effect rounded-lg p-6">
              <h3 className="font-semibold text-semantic-text mb-2">Error Handling</h3>
              <p className="text-sm text-semantic-textMuted">
                Robust error boundaries with user-friendly messages
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

export default App;
