{"version": 3, "sources": ["../../src/index.ts", "../../src/Animated.ts", "../../src/AnimatedValue.ts", "../../src/AnimatedString.ts", "../../src/AnimatedArray.ts", "../../src/AnimatedObject.ts", "../../src/context.ts", "../../src/getAnimatedType.ts", "../../src/createHost.ts", "../../src/withAnimated.tsx"], "sourcesContent": ["export * from './Animated'\nexport * from './AnimatedValue'\nexport * from './AnimatedString'\nexport * from './AnimatedArray'\nexport * from './AnimatedObject'\nexport * from './getAnimatedType'\nexport * from './createHost'\nexport * from './types'\n", "import { defineHidden } from '@react-spring/shared'\nimport { AnimatedValue } from './AnimatedValue'\n\nconst $node: any = Symbol.for('Animated:node')\n\nexport const isAnimated = <T = any>(value: any): value is Animated<T> =>\n  !!value && value[$node] === value\n\n/** Get the owner's `Animated` node. */\nexport const getAnimated = <T = any>(owner: any): Animated<T> | undefined =>\n  owner && owner[$node]\n\n/** Set the owner's `Animated` node. */\nexport const setAnimated = (owner: any, node: Animated) =>\n  defineHidden(owner, $node, node)\n\n/** Get every `AnimatedValue` in the owner's `Animated` node. */\nexport const getPayload = (owner: any): AnimatedValue[] | undefined =>\n  owner && owner[$node] && owner[$node].getPayload()\n\nexport abstract class Animated<T = any> {\n  /** The cache of animated values */\n  protected payload?: Payload\n\n  constructor() {\n    // This makes \"isAnimated\" return true.\n    setAnimated(this, this)\n  }\n\n  /** Get the current value. Pass `true` for only animated values. */\n  abstract getValue(animated?: boolean): T\n\n  /** Set the current value. Returns `true` if the value changed. */\n  abstract setValue(value: T): boolean | void\n\n  /** Reset any animation state. */\n  abstract reset(goal?: T): void\n\n  /** Get every `AnimatedValue` used by this node. */\n  getPayload(): Payload {\n    return this.payload || []\n  }\n}\n\nexport type Payload = readonly AnimatedValue[]\n", "import { is } from '@react-spring/shared'\nimport { Animated, Payload } from './Animated'\n\n/** An animated number or a native attribute value */\nexport class AnimatedValue<T = any> extends Animated {\n  done = true\n  elapsedTime!: number\n  lastPosition!: number\n  lastVelocity?: number | null\n  v0?: number | null\n  durationProgress = 0\n\n  constructor(protected _value: T) {\n    super()\n    if (is.num(this._value)) {\n      this.lastPosition = this._value\n    }\n  }\n\n  /** @internal */\n  static create(value: any) {\n    return new AnimatedValue(value)\n  }\n\n  getPayload(): Payload {\n    return [this]\n  }\n\n  getValue() {\n    return this._value\n  }\n\n  setValue(value: T, step?: number) {\n    if (is.num(value)) {\n      this.lastPosition = value\n      if (step) {\n        value = (Math.round(value / step) * step) as any\n        if (this.done) {\n          this.lastPosition = value as any\n        }\n      }\n    }\n    if (this._value === value) {\n      return false\n    }\n    this._value = value\n    return true\n  }\n\n  reset() {\n    const { done } = this\n    this.done = false\n    if (is.num(this._value)) {\n      this.elapsedTime = 0\n      this.durationProgress = 0\n      this.lastPosition = this._value\n      if (done) this.lastVelocity = null\n      this.v0 = null\n    }\n  }\n}\n", "import { AnimatedValue } from './AnimatedValue'\nimport { is, createInterpolator } from '@react-spring/shared'\n\ntype Value = string | number\n\nexport class AnimatedString extends AnimatedValue<Value> {\n  protected declare _value: number\n  protected _string: string | null = null\n  protected _toString: (input: number) => string\n\n  constructor(value: string) {\n    super(0)\n    this._toString = createInterpolator({\n      output: [value, value],\n    })\n  }\n\n  /** @internal */\n  static create(value: string) {\n    return new AnimatedString(value)\n  }\n\n  getValue() {\n    const value = this._string\n    return value == null ? (this._string = this._toString(this._value)) : value\n  }\n\n  setValue(value: Value) {\n    if (is.str(value)) {\n      if (value == this._string) {\n        return false\n      }\n      this._string = value\n      this._value = 1\n    } else if (super.setValue(value)) {\n      this._string = null\n    } else {\n      return false\n    }\n    return true\n  }\n\n  reset(goal?: string) {\n    if (goal) {\n      this._toString = createInterpolator({\n        output: [this.getValue(), goal],\n      })\n    }\n    this._value = 0\n    super.reset()\n  }\n}\n", "import { isAnimatedString } from '@react-spring/shared'\nimport { AnimatedObject } from './AnimatedObject'\nimport { AnimatedString } from './AnimatedString'\nimport { AnimatedValue } from './AnimatedValue'\n\ntype Value = number | string\ntype Source = AnimatedValue<Value>[]\n\n/** An array of animated nodes */\nexport class AnimatedArray<\n  T extends ReadonlyArray<Value> = Value[],\n> extends AnimatedObject {\n  protected declare source: Source\n  constructor(source: T) {\n    super(source)\n  }\n\n  /** @internal */\n  static create<T extends ReadonlyArray<Value>>(source: T) {\n    return new AnimatedArray(source)\n  }\n\n  getValue(): T {\n    return this.source.map(node => node.getValue()) as any\n  }\n\n  setValue(source: T) {\n    const payload = this.getPayload()\n    // Reuse the payload when lengths are equal.\n    if (source.length == payload.length) {\n      return payload.map((node, i) => node.setValue(source[i])).some(Boolean)\n    }\n    // Remake the payload when length changes.\n    super.setValue(source.map(makeAnimated))\n    return true\n  }\n}\n\nfunction makeAnimated(value: any) {\n  const nodeType = isAnimatedString(value) ? AnimatedString : AnimatedValue\n  return nodeType.create(value)\n}\n", "import { Lookup } from '@react-spring/types'\nimport {\n  each,\n  eachProp,\n  getFluidValue,\n  hasFluidValue,\n} from '@react-spring/shared'\nimport { Animated, isAnimated, getPayload } from './Animated'\nimport { AnimatedValue } from './AnimatedValue'\nimport { TreeContext } from './context'\n\n/** An object containing `Animated` nodes */\nexport class AnimatedObject extends Animated {\n  constructor(protected source: Lookup) {\n    super()\n    this.setValue(source)\n  }\n\n  getValue(animated?: boolean) {\n    const values: Lookup = {}\n    eachProp(this.source, (source, key) => {\n      if (isAnimated(source)) {\n        values[key] = source.getValue(animated)\n      } else if (hasFluidValue(source)) {\n        values[key] = getFluidValue(source)\n      } else if (!animated) {\n        values[key] = source\n      }\n    })\n    return values\n  }\n\n  /** Replace the raw object data */\n  setValue(source: Lookup) {\n    this.source = source\n    this.payload = this._makePayload(source)\n  }\n\n  reset() {\n    if (this.payload) {\n      each(this.payload, node => node.reset())\n    }\n  }\n\n  /** Create a payload set. */\n  protected _makePayload(source: Lookup) {\n    if (source) {\n      const payload = new Set<AnimatedValue>()\n      eachProp(source, this._addToPayload, payload)\n      return Array.from(payload)\n    }\n  }\n\n  /** Add to a payload set. */\n  protected _addToPayload(this: Set<AnimatedValue>, source: any) {\n    if (TreeContext.dependencies && hasFluidValue(source)) {\n      TreeContext.dependencies.add(source)\n    }\n    const payload = getPayload(source)\n    if (payload) {\n      each(payload, node => this.add(node))\n    }\n  }\n}\n", "import { FluidValue } from '@react-spring/shared'\n\nexport type TreeContext = {\n  /**\n   * Any animated values found when updating the payload of an `AnimatedObject`\n   * are also added to this `Set` to be observed by an animated component.\n   */\n  dependencies: Set<FluidValue> | null\n}\n\nexport const TreeContext: TreeContext = { dependencies: null }\n", "import { is, isAnimatedString } from '@react-spring/shared'\nimport { AnimatedType } from './types'\nimport { AnimatedArray } from './AnimatedArray'\nimport { AnimatedString } from './AnimatedString'\nimport { AnimatedValue } from './AnimatedValue'\nimport { getAnimated } from './Animated'\n\n/** Return the `Animated` node constructor for a given value */\nexport function getAnimatedType(value: any): AnimatedType {\n  const parentNode = getAnimated(value)\n  return parentNode\n    ? (parentNode.constructor as any)\n    : is.arr(value)\n      ? AnimatedArray\n      : isAnimatedString(value)\n        ? AnimatedString\n        : AnimatedValue\n}\n", "import { Lookup } from '@react-spring/types'\nimport { is, eachProp } from '@react-spring/shared'\nimport { AnimatableComponent, withAnimated } from './withAnimated'\nimport { Animated } from './Animated'\nimport { AnimatedObject } from './AnimatedObject'\n\nexport interface HostConfig {\n  /** Provide custom logic for native updates */\n  applyAnimatedValues: (node: any, props: Lookup) => boolean | void\n  /** Wrap the `style` prop with an animated node */\n  createAnimatedStyle: (style: Lookup) => Animated\n  /** Intercept props before they're passed to an animated component */\n  getComponentProps: (props: Lookup) => typeof props\n}\n\n// A stub type that gets replaced by @react-spring/web and others.\ntype WithAnimated = {\n  (Component: AnimatableComponent): any\n  [key: string]: any\n}\n\n// For storing the animated version on the original component\nconst cacheKey = Symbol.for('AnimatedComponent')\n\nexport const createHost = (\n  components: AnimatableComponent[] | { [key: string]: AnimatableComponent },\n  {\n    applyAnimatedValues = () => false,\n    createAnimatedStyle = style => new AnimatedObject(style),\n    getComponentProps = props => props,\n  }: Partial<HostConfig> = {}\n) => {\n  const hostConfig: HostConfig = {\n    applyAnimatedValues,\n    createAnimatedStyle,\n    getComponentProps,\n  }\n\n  const animated: WithAnimated = (Component: any) => {\n    const displayName = getDisplayName(Component) || 'Anonymous'\n\n    if (is.str(Component)) {\n      Component =\n        animated[Component] ||\n        (animated[Component] = withAnimated(Component, hostConfig))\n    } else {\n      Component =\n        Component[cacheKey] ||\n        (Component[cacheKey] = withAnimated(Component, hostConfig))\n    }\n\n    Component.displayName = `Animated(${displayName})`\n    return Component\n  }\n\n  eachProp(components, (Component, key) => {\n    if (is.arr(components)) {\n      key = getDisplayName(Component)!\n    }\n    animated[key] = animated(Component)\n  })\n\n  return {\n    animated,\n  }\n}\n\nconst getDisplayName = (arg: AnimatableComponent) =>\n  is.str(arg)\n    ? arg\n    : arg && is.str(arg.displayName)\n      ? arg.displayName\n      : (is.fun(arg) && arg.name) || null\n", "import * as React from 'react'\nimport { forwardRef, useRef, Ref, useCallback, useEffect } from 'react'\nimport {\n  is,\n  each,\n  raf,\n  useForceUpdate,\n  useOnce,\n  FluidEvent,\n  FluidValue,\n  addFluidObserver,\n  removeFluidObserver,\n  useIsomorphicLayoutEffect,\n} from '@react-spring/shared'\nimport { ElementType } from '@react-spring/types'\n\nimport { AnimatedObject } from './AnimatedObject'\nimport { TreeContext } from './context'\nimport { HostConfig } from './createHost'\n\nexport type AnimatableComponent = string | Exclude<ElementType, string>\n\nexport const withAnimated = (Component: any, host: HostConfig) => {\n  const hasInstance: boolean =\n    // Function components must use \"forwardRef\" to avoid being\n    // re-rendered on every animation frame.\n    !is.fun(Component) ||\n    (Component.prototype && Component.prototype.isReactComponent)\n\n  return forwardRef((givenProps: any, givenRef: Ref<any>) => {\n    const instanceRef = useRef<any>(null)\n\n    // The `hasInstance` value is constant, so we can safely avoid\n    // the `useCallback` invocation when `hasInstance` is false.\n    const ref =\n      hasInstance &&\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      useCallback(\n        (value: any) => {\n          instanceRef.current = updateRef(givenRef, value)\n        },\n        [givenRef]\n      )\n\n    const [props, deps] = getAnimatedState(givenProps, host)\n\n    const forceUpdate = useForceUpdate()\n\n    const callback = () => {\n      const instance = instanceRef.current\n      if (hasInstance && !instance) {\n        // Either this component was unmounted before changes could be\n        // applied, or the wrapped component forgot to forward its ref.\n        return\n      }\n\n      const didUpdate = instance\n        ? host.applyAnimatedValues(instance, props.getValue(true))\n        : false\n\n      // Re-render the component when native updates fail.\n      if (didUpdate === false) {\n        forceUpdate()\n      }\n    }\n\n    const observer = new PropsObserver(callback, deps)\n\n    const observerRef = useRef<PropsObserver>()\n    useIsomorphicLayoutEffect(() => {\n      observerRef.current = observer\n\n      // Observe the latest dependencies.\n      each(deps, dep => addFluidObserver(dep, observer))\n\n      return () => {\n        // Stop observing previous dependencies.\n        if (observerRef.current) {\n          each(observerRef.current.deps, dep =>\n            removeFluidObserver(dep, observerRef.current!)\n          )\n          raf.cancel(observerRef.current.update)\n        }\n      }\n    })\n\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    useEffect(callback, [])\n    // Stop observing on unmount.\n    useOnce(() => () => {\n      const observer = observerRef.current!\n      each(observer.deps, dep => removeFluidObserver(dep, observer))\n    })\n\n    const usedProps = host.getComponentProps(props.getValue())\n    return <Component {...usedProps} ref={ref} />\n  })\n}\n\nclass PropsObserver {\n  constructor(\n    readonly update: () => void,\n    readonly deps: Set<FluidValue>\n  ) {}\n  eventObserved(event: FluidEvent) {\n    if (event.type == 'change') {\n      raf.write(this.update)\n    }\n  }\n}\n\ntype AnimatedState = [props: AnimatedObject, dependencies: Set<FluidValue>]\n\nfunction getAnimatedState(props: any, host: HostConfig): AnimatedState {\n  const dependencies = new Set<FluidValue>()\n  TreeContext.dependencies = dependencies\n\n  // Search the style for dependencies.\n  if (props.style)\n    props = {\n      ...props,\n      style: host.createAnimatedStyle(props.style),\n    }\n\n  // Search the props for dependencies.\n  props = new AnimatedObject(props)\n\n  TreeContext.dependencies = null\n  return [props, dependencies]\n}\n\nfunction updateRef<T>(ref: Ref<T>, value: T) {\n  if (ref) {\n    if (is.fun(ref)) ref(value)\n    else (ref as any).current = value\n  }\n  return value\n}\n"], "mappings": "0jBAAA,IAAAA,GAAA,GAAAC,EAAAD,GAAA,cAAAE,EAAA,kBAAAC,EAAA,mBAAAC,EAAA,mBAAAC,EAAA,kBAAAC,EAAA,eAAAC,GAAA,gBAAAC,EAAA,oBAAAC,EAAA,eAAAC,EAAA,eAAAC,EAAA,gBAAAC,IAAA,eAAAC,EAAAb,ICAA,IAAAc,EAA6B,gCAGvBC,EAAa,OAAO,IAAI,eAAe,EAEhCC,EAAuBC,GAClC,CAAC,CAACA,GAASA,EAAMF,CAAK,IAAME,EAGjBC,EAAwBC,GACnCA,GAASA,EAAMJ,CAAK,EAGTK,EAAc,CAACD,EAAYE,OACtC,gBAAaF,EAAOJ,EAAOM,CAAI,EAGpBC,EAAcH,GACzBA,GAASA,EAAMJ,CAAK,GAAKI,EAAMJ,CAAK,EAAE,WAAW,EAE7BQ,EAAf,KAAiC,CAItC,aAAc,CAEZH,EAAY,KAAM,IAAI,CACxB,CAYA,YAAsB,CACpB,OAAO,KAAK,SAAW,CAAC,CAC1B,CACF,EC1CA,IAAAI,EAAmB,gCAIZ,IAAMC,EAAN,cAAqCC,CAAS,CAQnD,YAAsBC,EAAW,CAC/B,MAAM,EADc,YAAAA,EAPtB,UAAO,GAKP,sBAAmB,EAIb,KAAG,IAAI,KAAK,MAAM,IACpB,KAAK,aAAe,KAAK,OAE7B,CAGA,OAAO,OAAOC,EAAY,CACxB,OAAO,IAAIH,EAAcG,CAAK,CAChC,CAEA,YAAsB,CACpB,MAAO,CAAC,IAAI,CACd,CAEA,UAAW,CACT,OAAO,KAAK,MACd,CAEA,SAASA,EAAUC,EAAe,CAUhC,OATI,KAAG,IAAID,CAAK,IACd,KAAK,aAAeA,EAChBC,IACFD,EAAS,KAAK,MAAMA,EAAQC,CAAI,EAAIA,EAChC,KAAK,OACP,KAAK,aAAeD,KAItB,KAAK,SAAWA,EACX,IAET,KAAK,OAASA,EACP,GACT,CAEA,OAAQ,CACN,GAAM,CAAE,KAAAE,CAAK,EAAI,KACjB,KAAK,KAAO,GACR,KAAG,IAAI,KAAK,MAAM,IACpB,KAAK,YAAc,EACnB,KAAK,iBAAmB,EACxB,KAAK,aAAe,KAAK,OACrBA,IAAM,KAAK,aAAe,MAC9B,KAAK,GAAK,KAEd,CACF,EC3DA,IAAAC,EAAuC,gCAI1BC,EAAN,cAA6BC,CAAqB,CAKvD,YAAYC,EAAe,CACzB,MAAM,CAAC,EAJT,KAAU,QAAyB,KAKjC,KAAK,aAAY,sBAAmB,CAClC,OAAQ,CAACA,EAAOA,CAAK,CACvB,CAAC,CACH,CAGA,OAAO,OAAOA,EAAe,CAC3B,OAAO,IAAIF,EAAeE,CAAK,CACjC,CAEA,UAAW,CACT,IAAMA,EAAQ,KAAK,QACnB,OAAOA,IAAiB,KAAK,QAAU,KAAK,UAAU,KAAK,MAAM,EACnE,CAEA,SAASA,EAAc,CACrB,GAAI,KAAG,IAAIA,CAAK,EAAG,CACjB,GAAIA,GAAS,KAAK,QAChB,MAAO,GAET,KAAK,QAAUA,EACf,KAAK,OAAS,UACL,MAAM,SAASA,CAAK,EAC7B,KAAK,QAAU,SAEf,OAAO,GAET,MAAO,EACT,CAEA,MAAMC,EAAe,CACfA,IACF,KAAK,aAAY,sBAAmB,CAClC,OAAQ,CAAC,KAAK,SAAS,EAAGA,CAAI,CAChC,CAAC,GAEH,KAAK,OAAS,EACd,MAAM,MAAM,CACd,CACF,ECnDA,IAAAC,EAAiC,gCCCjC,IAAAC,EAKO,gCCIA,IAAMC,EAA2B,CAAE,aAAc,IAAK,EDEtD,IAAMC,EAAN,cAA6BC,CAAS,CAC3C,YAAsBC,EAAgB,CACpC,MAAM,EADc,YAAAA,EAEpB,KAAK,SAASA,CAAM,CACtB,CAEA,SAASC,EAAoB,CAC3B,IAAMC,EAAiB,CAAC,EACxB,qBAAS,KAAK,OAAQ,CAACF,EAAQG,IAAQ,CACjCC,EAAWJ,CAAM,EACnBE,EAAOC,CAAG,EAAIH,EAAO,SAASC,CAAQ,KAC7B,iBAAcD,CAAM,EAC7BE,EAAOC,CAAG,KAAI,iBAAcH,CAAM,EACxBC,IACVC,EAAOC,CAAG,EAAIH,EAElB,CAAC,EACME,CACT,CAGA,SAASF,EAAgB,CACvB,KAAK,OAASA,EACd,KAAK,QAAU,KAAK,aAAaA,CAAM,CACzC,CAEA,OAAQ,CACF,KAAK,YACP,QAAK,KAAK,QAASK,GAAQA,EAAK,MAAM,CAAC,CAE3C,CAGU,aAAaL,EAAgB,CACrC,GAAIA,EAAQ,CACV,IAAMM,EAAU,IAAI,IACpB,qBAASN,EAAQ,KAAK,cAAeM,CAAO,EACrC,MAAM,KAAKA,CAAO,EAE7B,CAGU,cAAwCN,EAAa,CACzDO,EAAY,iBAAgB,iBAAcP,CAAM,GAClDO,EAAY,aAAa,IAAIP,CAAM,EAErC,IAAMM,EAAUE,EAAWR,CAAM,EAC7BM,MACF,QAAKA,EAASD,GAAQ,KAAK,IAAIA,CAAI,CAAC,CAExC,CACF,EDtDO,IAAMI,EAAN,cAEGC,CAAe,CAEvB,YAAYC,EAAW,CACrB,MAAMA,CAAM,CACd,CAGA,OAAO,OAAuCA,EAAW,CACvD,OAAO,IAAIF,EAAcE,CAAM,CACjC,CAEA,UAAc,CACZ,OAAO,KAAK,OAAO,IAAIC,GAAQA,EAAK,SAAS,CAAC,CAChD,CAEA,SAASD,EAAW,CAClB,IAAME,EAAU,KAAK,WAAW,EAEhC,OAAIF,EAAO,QAAUE,EAAQ,OACpBA,EAAQ,IAAI,CAACD,EAAME,IAAMF,EAAK,SAASD,EAAOG,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,GAGxE,MAAM,SAASH,EAAO,IAAII,CAAY,CAAC,EAChC,GACT,CACF,EAEA,SAASA,EAAaC,EAAY,CAEhC,UADiB,oBAAiBA,CAAK,EAAIC,EAAiBC,GAC5C,OAAOF,CAAK,CAC9B,CGzCA,IAAAG,EAAqC,gCAQ9B,SAASC,EAAgBC,EAA0B,CACxD,IAAMC,EAAaC,EAAYF,CAAK,EACpC,OAAOC,EACFA,EAAW,YACZ,KAAG,IAAID,CAAK,EACVG,KACA,oBAAiBH,CAAK,EACpBI,EACAC,CACV,CChBA,IAAAC,EAA6B,gCCD7B,IAAAC,EAAuB,oBACvBC,EAAgE,iBAChEC,EAWO,gCASA,IAAMC,EAAe,CAACC,EAAgBC,IAAqB,CAChE,IAAMC,EAGJ,CAAC,KAAG,IAAIF,CAAS,GAChBA,EAAU,WAAaA,EAAU,UAAU,iBAE9C,SAAO,cAAW,CAACG,EAAiBC,IAAuB,CACzD,IAAMC,KAAc,UAAY,IAAI,EAI9BC,EACJJ,MAEA,eACGK,GAAe,CACdF,EAAY,QAAUG,GAAUJ,EAAUG,CAAK,CACjD,EACA,CAACH,CAAQ,CACX,EAEI,CAACK,EAAOC,CAAI,EAAIC,EAAiBR,EAAYF,CAAI,EAEjDW,KAAc,kBAAe,EAE7BC,EAAW,IAAM,CACrB,IAAMC,EAAWT,EAAY,QAC7B,GAAIH,GAAe,CAACY,EAGlB,QAGgBA,EACdb,EAAK,oBAAoBa,EAAUL,EAAM,SAAS,EAAI,CAAC,EACvD,MAGc,IAChBG,EAAY,CAEhB,EAEMG,EAAW,IAAIC,EAAcH,EAAUH,CAAI,EAE3CO,KAAc,UAAsB,KAC1C,6BAA0B,KACxBA,EAAY,QAAUF,KAGtB,QAAKL,EAAMQ,MAAO,oBAAiBA,EAAKH,CAAQ,CAAC,EAE1C,IAAM,CAEPE,EAAY,aACd,QAAKA,EAAY,QAAQ,KAAMC,MAC7B,uBAAoBA,EAAKD,EAAY,OAAQ,CAC/C,EACA,MAAI,OAAOA,EAAY,QAAQ,MAAM,EAEzC,EACD,KAGD,aAAUJ,EAAU,CAAC,CAAC,KAEtB,WAAQ,IAAM,IAAM,CAClB,IAAME,EAAWE,EAAY,WAC7B,QAAKF,EAAS,KAAMG,MAAO,uBAAoBA,EAAKH,CAAQ,CAAC,CAC/D,CAAC,EAED,IAAMI,EAAYlB,EAAK,kBAAkBQ,EAAM,SAAS,CAAC,EACzD,OAAO,gBAACT,EAAA,CAAW,GAAGmB,EAAW,IAAKb,EAAK,CAC7C,CAAC,CACH,EAEMU,EAAN,KAAoB,CAClB,YACWI,EACAV,EACT,CAFS,YAAAU,EACA,UAAAV,CACR,CACH,cAAcW,EAAmB,CAC3BA,EAAM,MAAQ,UAChB,MAAI,MAAM,KAAK,MAAM,CAEzB,CACF,EAIA,SAASV,EAAiBF,EAAYR,EAAiC,CACrE,IAAMqB,EAAe,IAAI,IACzB,OAAAC,EAAY,aAAeD,EAGvBb,EAAM,QACRA,EAAQ,CACN,GAAGA,EACH,MAAOR,EAAK,oBAAoBQ,EAAM,KAAK,CAC7C,GAGFA,EAAQ,IAAIe,EAAef,CAAK,EAEhCc,EAAY,aAAe,KACpB,CAACd,EAAOa,CAAY,CAC7B,CAEA,SAASd,GAAaF,EAAaC,EAAU,CAC3C,OAAID,IACE,KAAG,IAAIA,CAAG,EAAGA,EAAIC,CAAK,EACpBD,EAAY,QAAUC,GAEvBA,CACT,CDnHA,IAAMkB,EAAW,OAAO,IAAI,mBAAmB,EAElCC,GAAa,CACxBC,EACA,CACE,oBAAAC,EAAsB,IAAM,GAC5B,oBAAAC,EAAsBC,GAAS,IAAIC,EAAeD,CAAK,EACvD,kBAAAE,EAAoBC,GAASA,CAC/B,EAAyB,CAAC,IACvB,CACH,IAAMC,EAAyB,CAC7B,oBAAAN,EACA,oBAAAC,EACA,kBAAAG,CACF,EAEMG,EAA0BC,GAAmB,CACjD,IAAMC,EAAcC,EAAeF,CAAS,GAAK,YAEjD,OAAI,KAAG,IAAIA,CAAS,EAClBA,EACED,EAASC,CAAS,IACjBD,EAASC,CAAS,EAAIG,EAAaH,EAAWF,CAAU,GAE3DE,EACEA,EAAUX,CAAQ,IACjBW,EAAUX,CAAQ,EAAIc,EAAaH,EAAWF,CAAU,GAG7DE,EAAU,YAAc,YAAYC,KAC7BD,CACT,EAEA,qBAAST,EAAY,CAACS,EAAWI,IAAQ,CACnC,KAAG,IAAIb,CAAU,IACnBa,EAAMF,EAAeF,CAAS,GAEhCD,EAASK,CAAG,EAAIL,EAASC,CAAS,CACpC,CAAC,EAEM,CACL,SAAAD,CACF,CACF,EAEMG,EAAkBG,GACtB,KAAG,IAAIA,CAAG,EACNA,EACAA,GAAO,KAAG,IAAIA,EAAI,WAAW,EAC3BA,EAAI,YACH,KAAG,IAAIA,CAAG,GAAKA,EAAI,MAAS", "names": ["src_exports", "__export", "Animated", "AnimatedArray", "AnimatedObject", "AnimatedString", "AnimatedValue", "createHost", "getAnimated", "getAnimatedType", "getPayload", "isAnimated", "setAnimated", "__toCommonJS", "import_shared", "$node", "isAnimated", "value", "getAnimated", "owner", "setAnimated", "node", "getPayload", "Animated", "import_shared", "AnimatedValue", "Animated", "_value", "value", "step", "done", "import_shared", "AnimatedString", "AnimatedValue", "value", "goal", "import_shared", "import_shared", "TreeContext", "AnimatedObject", "Animated", "source", "animated", "values", "key", "isAnimated", "node", "payload", "TreeContext", "getPayload", "AnimatedArray", "AnimatedObject", "source", "node", "payload", "i", "makeAnimated", "value", "AnimatedString", "AnimatedValue", "import_shared", "getAnimatedType", "value", "parentNode", "getAnimated", "AnimatedArray", "AnimatedString", "AnimatedValue", "import_shared", "React", "import_react", "import_shared", "withAnimated", "Component", "host", "hasInstance", "givenProps", "givenRef", "instanceRef", "ref", "value", "updateRef", "props", "deps", "getAnimatedState", "forceUpdate", "callback", "instance", "observer", "PropsObserver", "observerRef", "dep", "usedProps", "update", "event", "dependencies", "TreeContext", "AnimatedObject", "cache<PERSON>ey", "createHost", "components", "applyAnimatedValues", "createAnimatedStyle", "style", "AnimatedObject", "getComponentProps", "props", "hostConfig", "animated", "Component", "displayName", "getDisplayName", "withAnimated", "key", "arg"]}