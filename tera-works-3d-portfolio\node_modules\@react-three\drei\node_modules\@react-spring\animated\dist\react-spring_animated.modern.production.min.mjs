import{defineH<PERSON>den as B}from"@react-spring/shared";var h=Symbol.for("Animated:node"),v=t=>!!t&&t[h]===t,k=t=>t&&t[h],D=(t,r)=>B(t,h,r),F=t=>t&&t[h]&&t[h].getPayload(),c=class{constructor(){D(this,this)}getPayload(){return this.payload||[]}};import{is as A}from"@react-spring/shared";var l=class extends c{constructor(e){super();this._value=e;this.done=!0;this.durationProgress=0;A.num(this._value)&&(this.lastPosition=this._value)}static create(e){return new l(e)}getPayload(){return[this]}getValue(){return this._value}setValue(e,n){return A.num(e)&&(this.lastPosition=e,n&&(e=Math.round(e/n)*n,this.done&&(this.lastPosition=e))),this._value===e?!1:(this._value=e,!0)}reset(){let{done:e}=this;this.done=!1,A.num(this._value)&&(this.elapsedTime=0,this.durationProgress=0,this.lastPosition=this._value,e&&(this.lastVelocity=null),this.v0=null)}};import{is as K,createInterpolator as R}from"@react-spring/shared";var d=class extends l{constructor(e){super(0);this._string=null;this._toString=R({output:[e,e]})}static create(e){return new d(e)}getValue(){let e=this._string;return e??(this._string=this._toString(this._value))}setValue(e){if(K.str(e)){if(e==this._string)return!1;this._string=e,this._value=1}else if(super.setValue(e))this._string=null;else return!1;return!0}reset(e){e&&(this._toString=R({output:[this.getValue(),e]})),this._value=0,super.reset()}};import{isAnimatedString as q}from"@react-spring/shared";import{each as L,eachProp as w,getFluidValue as M,hasFluidValue as C}from"@react-spring/shared";var f={dependencies:null};var u=class extends c{constructor(e){super();this.source=e;this.setValue(e)}getValue(e){let n={};return w(this.source,(a,i)=>{v(a)?n[i]=a.getValue(e):C(a)?n[i]=M(a):e||(n[i]=a)}),n}setValue(e){this.source=e,this.payload=this._makePayload(e)}reset(){this.payload&&L(this.payload,e=>e.reset())}_makePayload(e){if(e){let n=new Set;return w(e,this._addToPayload,n),Array.from(n)}}_addToPayload(e){f.dependencies&&C(e)&&f.dependencies.add(e);let n=F(e);n&&L(n,a=>this.add(a))}};var y=class extends u{constructor(r){super(r)}static create(r){return new y(r)}getValue(){return this.source.map(r=>r.getValue())}setValue(r){let e=this.getPayload();return r.length==e.length?e.map((n,a)=>n.setValue(r[a])).some(Boolean):(super.setValue(r.map(z)),!0)}};function z(t){return(q(t)?d:l).create(t)}import{is as G,isAnimatedString as J}from"@react-spring/shared";function Le(t){let r=k(t);return r?r.constructor:G.arr(t)?y:J(t)?d:l}import{is as g,eachProp as oe}from"@react-spring/shared";import*as O from"react";import{forwardRef as Q,useRef as H,useCallback as X,useEffect as Y}from"react";import{is as N,each as V,raf as U,useForceUpdate as Z,useOnce as ee,addFluidObserver as te,removeFluidObserver as E,useIsomorphicLayoutEffect as re}from"@react-spring/shared";var x=(t,r)=>{let e=!N.fun(t)||t.prototype&&t.prototype.isReactComponent;return Q((n,a)=>{let i=H(null),o=e&&X(s=>{i.current=ae(a,s)},[a]),[m,T]=ne(n,r),W=Z(),P=()=>{let s=i.current;if(e&&!s)return;(s?r.applyAnimatedValues(s,m.getValue(!0)):!1)===!1&&W()},_=new b(P,T),p=H();re(()=>(p.current=_,V(T,s=>te(s,_)),()=>{p.current&&(V(p.current.deps,s=>E(s,p.current)),U.cancel(p.current.update))})),Y(P,[]),ee(()=>()=>{let s=p.current;V(s.deps,S=>E(S,s))});let $=r.getComponentProps(m.getValue());return O.createElement(t,{...$,ref:o})})},b=class{constructor(r,e){this.update=r;this.deps=e}eventObserved(r){r.type=="change"&&U.write(this.update)}};function ne(t,r){let e=new Set;return f.dependencies=e,t.style&&(t={...t,style:r.createAnimatedStyle(t.style)}),t=new u(t),f.dependencies=null,[t,e]}function ae(t,r){return t&&(N.fun(t)?t(r):t.current=r),r}var j=Symbol.for("AnimatedComponent"),Ke=(t,{applyAnimatedValues:r=()=>!1,createAnimatedStyle:e=a=>new u(a),getComponentProps:n=a=>a}={})=>{let a={applyAnimatedValues:r,createAnimatedStyle:e,getComponentProps:n},i=o=>{let m=I(o)||"Anonymous";return g.str(o)?o=i[o]||(i[o]=x(o,a)):o=o[j]||(o[j]=x(o,a)),o.displayName=`Animated(${m})`,o};return oe(t,(o,m)=>{g.arr(t)&&(m=I(o)),i[m]=i(o)}),{animated:i}},I=t=>g.str(t)?t:t&&g.str(t.displayName)?t.displayName:g.fun(t)&&t.name||null;export{c as Animated,y as AnimatedArray,u as AnimatedObject,d as AnimatedString,l as AnimatedValue,Ke as createHost,k as getAnimated,Le as getAnimatedType,F as getPayload,v as isAnimated,D as setAnimated};
//# sourceMappingURL=react-spring_animated.modern.production.min.mjs.map