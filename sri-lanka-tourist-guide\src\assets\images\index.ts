// Image Index - Organized by Category
// This file provides centralized access to all images in the project

// Destinations
import Sigiriya from './destinations/Sigiriya.jpg'
import Sigiriya2 from './destinations/Sigiriya2.jpg'
import Sigiriya3 from './destinations/Sigiriya 3.jpg'
import KandyHighway from './destinations/Kandy-Mahiyangane-Padiyatalawa Hwy.jpg'
import NuwaraEliya from './destinations/Nuwara Eliya.jpg'
import EllaView from './destinations/View from the top of Little Adam\'s Peak in Ella, Sri Lanka..jpg'
import Galle from './destinations/Galle.jpg'
import Galle2 from './destinations/Galle 2.jpg'
import GalleFort from './destinations/Galle Fort.jpg'
import GalleFort2 from './destinations/Galle Fort 2.jpg'
import GalleFort3 from './destinations/Galle Fort 3.jpg'

// Beaches
import Beach from './beaches/Beach.jpg'
import ArugamBay from './beaches/Arugam Bay.jpg'
import ArugamBay2 from './beaches/Arugam Bay 2.jpg'
import ArugamBay3 from './beaches/Arugam Bay 3.jpg'
import MirissaBeach from './beaches/Mirissa Beach, Mirissa.jpg'
import UnuwatunaBreach from './beaches/Unuwatuna Beach.jpg'
import MidigamaBeach from './beaches/Midigama Beach.jpg'
import TangalleRoad from './beaches/Tangalle Rd, Tangalle.jpg'
import Trincomalee from './beaches/Trincomalee.jpg'
import Trincomalee2 from './beaches/Trincomalee 2.jpg'
import CoconutTreeHill from './beaches/Coconut Tree Hill.jpg'
import CoconutTreeHillRoad from './beaches/Coconut Tree Hill Rd, Mirissa.jpg'
import WeligamaPool from './beaches/Iconic Smiling Moon Pool Weligama.jpg'

// Temples & Religious Sites
import DambullaCave from './temples/Dambulla Cave Temple, Dambulla.jpg'
import AbhayagiriMonastery from './temples/Abhayagiri Monastery.jpg'
import IsurumunuViharaya from './temples/Isurumuni Rajamaha Viharaya, Anuradhapura.jpg'
import IsurumunuViharaya2 from './temples/Isurumuni Rajamaha Viharaya, Anuradhapura 2.jpg'
import Jetavanaramaya from './temples/Jetavanaramaya.jpg'
import RuwanweliSeya from './temples/Ruwanweli Maha Seya, Anuradhapura.jpg'
import Ruwanweliseya from './temples/Ruwanweliseya, Anuradhapura.jpg'
import BodhiTree from './temples/Sri Maha Bodhi Tree.jpg'
import Thuparamaya from './temples/Thuparamaya.jpg'
import RedMosque from './temples/Red Mosque.jpg'
import MoonstoneGuardStones from './temples/Moonstone and guard stones.jpg'

// Wildlife
import ElephantOrphanage from './wildlife/Elephant orphanage, Rambukkana.jpg'
import ElephantSabaragamuwa from './wildlife/Elephant, Sabaragamuwa Province.jpg'
import PinnawalaZoo from './wildlife/Pinnawala Zoo, B199, Rambukkana.jpg'
import YalaNationalPark from './wildlife/Yala National Park, Hambantota.jpg'
import LahugalaAmpara from './wildlife/Lahugala, Ampara.jpg'

// Landscapes
import Mountain from './landscapes/Mountain.jpg'
import AmbuluwawaMountain from './landscapes/Ambuluwawa Mountain Hill.jpg'
import GerandiEllaFalls from './landscapes/Gerandi Ella Falls.jpg'
import LiptonSeatRoad from './landscapes/Lipton Seat Road.jpg'
import LiptonsSeat from './landscapes/Liptons Seat, Haputale.jpg'
import NelligalaRoad from './landscapes/Nelligala Rd, Kandy.jpg'

// Transportation
import TrainNanuoya from './transportation/Train, Nanuoya.jpg'
import TrainWelimada from './transportation/Train, Welimada.jpg'
import NineArchBridge from './transportation/Nine Arch Bridge.jpg'
import NineArchBridge2 from './transportation/Nine Arch Bridge 2.jpg'

// Culture
import EsalaPerahera from './culture/Esala Perahera, Kandy.jpg'
import LotusTower from './culture/Lotus Tower, Colombo.jpg'
import Tissamaharama from './culture/Tissamaharama.jpg'

// Export organized image collections
export const destinationImages = {
  sigiriya: {
    main: Sigiriya,
    alternate: Sigiriya2,
    aerial: Sigiriya3,
  },
  kandy: {
    highway: KandyHighway,
  },
  ella: {
    littleAdamsPeak: EllaView,
  },
  nuwaraEliya: {
    main: NuwaraEliya,
  },
  galle: {
    main: Galle,
    alternate: Galle2,
    fort: GalleFort,
    fortAlternate: GalleFort2,
    fortAerial: GalleFort3,
  },
}

export const beachImages = {
  general: Beach,
  arugamBay: {
    main: ArugamBay,
    view2: ArugamBay2,
    view3: ArugamBay3,
  },
  mirissa: {
    beach: MirissaBeach,
    coconutHill: CoconutTreeHill,
    coconutHillRoad: CoconutTreeHillRoad,
  },
  unuwatuna: UnuwatunaBreach,
  midigama: MidigamaBeach,
  tangalle: TangalleRoad,
  trincomalee: {
    main: Trincomalee,
    alternate: Trincomalee2,
  },
  weligama: WeligamaPool,
}

export const templeImages = {
  dambulla: DambullaCave,
  anuradhapura: {
    abhayagiri: AbhayagiriMonastery,
    isurumunu: IsurumunuViharaya,
    isurumumuAlternate: IsurumunuViharaya2,
    jetavanaramaya: Jetavanaramaya,
    ruwanweliSeya: RuwanweliSeya,
    ruwanweliseya: Ruwanweliseya,
    bodhiTree: BodhiTree,
    thuparamaya: Thuparamaya,
  },
  redMosque: RedMosque,
  moonstone: MoonstoneGuardStones,
}

export const wildlifeImages = {
  elephants: {
    orphanage: ElephantOrphanage,
    sabaragamuwa: ElephantSabaragamuwa,
    pinnawala: PinnawalaZoo,
  },
  nationalParks: {
    yala: YalaNationalPark,
    lahugala: LahugalaAmpara,
  },
}

export const landscapeImages = {
  mountains: {
    general: Mountain,
    ambuluwawa: AmbuluwawaMountain,
  },
  waterfalls: {
    gerandiElla: GerandiEllaFalls,
  },
  teaCountry: {
    liptonSeatRoad: LiptonSeatRoad,
    liptonsSeat: LiptonsSeat,
  },
  kandy: {
    nelligalaRoad: NelligalaRoad,
  },
}

export const transportationImages = {
  trains: {
    nanuoya: TrainNanuoya,
    welimada: TrainWelimada,
  },
  bridges: {
    nineArch: NineArchBridge,
    nineArchAlternate: NineArchBridge2,
  },
}

export const cultureImages = {
  festivals: {
    esalaPerahera: EsalaPerahera,
  },
  landmarks: {
    lotusTower: LotusTower,
  },
  cities: {
    tissamaharama: Tissamaharama,
  },
}

// Export all images for easy access
export const allImages = {
  destinations: destinationImages,
  beaches: beachImages,
  temples: templeImages,
  wildlife: wildlifeImages,
  landscapes: landscapeImages,
  transportation: transportationImages,
  culture: cultureImages,
}

// Helper function to get image by category and name
export const getImage = (category: keyof typeof allImages, subcategory: string, imageName?: string) => {
  const categoryImages = allImages[category] as any
  if (!categoryImages) return null
  
  const subcategoryImages = categoryImages[subcategory]
  if (!subcategoryImages) return null
  
  if (imageName && typeof subcategoryImages === 'object') {
    return subcategoryImages[imageName] || null
  }
  
  return subcategoryImages
}

// Missing images that need to be sourced
export const missingImages = {
  destinations: [
    'Colombo skyline and cityscape',
    'Polonnaruwa ancient ruins',
    'Anuradhapura ancient city overview',
    'Dambulla Golden Temple exterior',
    'Pinnawala Elephant Orphanage entrance',
    'Horton Plains National Park',
    'Adams Peak pilgrimage trail',
  ],
  beaches: [
    'Bentota beach activities',
    'Hikkaduwa coral reef snorkeling',
    'Negombo fishing boats',
    'Kalpitiya kite surfing',
    'Pasikudah bay aerial view',
  ],
  activities: [
    'White water rafting in Kitulgala',
    'Tea plantation workers picking tea',
    'Spice garden tour in Matale',
    'Whale watching boat tour',
    'Safari jeep in national park',
    'Traditional Sri Lankan cooking class',
    'Ayurvedic spa treatment',
  ],
  culture: [
    'Traditional Kandyan dancers',
    'Sri Lankan traditional wedding',
    'Local market with spices and fruits',
    'Traditional craftsmen at work',
    'Buddhist monks in meditation',
    'Hindu temple ceremony',
  ],
  food: [
    'Traditional Sri Lankan rice and curry',
    'String hoppers (idiyappam)',
    'Kottu roti preparation',
    'Fresh tropical fruits display',
    'Ceylon tea plantation and tasting',
    'Street food vendors',
  ],
}
