// Image Index - Organized by Category
// This file provides centralized access to all images in the project

// Destinations
import Sigiriya from './destinations/Sigiriya.jpg?url'
import Sigiriya2 from './destinations/Sigiriya2.jpg?url'
import Sigiriya3 from './destinations/Sigiriya 3.jpg?url'
import KandyHighway from './destinations/Kandy-Mahiyangane-Padiyatalawa Hwy.jpg?url'
import NuwaraEliya from './destinations/Nuwara Eliya.jpg?url'
import EllaView from './destinations/View from the top of Little Adam\'s Peak in Ella, Sri Lanka..jpg?url'
import Galle from './destinations/Galle.jpg?url'
import Galle2 from './destinations/Galle 2.jpg?url'
import GalleFort from './destinations/Galle Fort.jpg?url'
import GalleFort2 from './destinations/Galle Fort 2.jpg?url'
import GalleFort3 from './destinations/Galle Fort 3.jpg?url'

// Beaches
import Beach from './beaches/Beach.jpg?url'
import ArugamBay from './beaches/Arugam Bay.jpg?url'
import ArugamBay2 from './beaches/Arugam Bay 2.jpg?url'
import ArugamBay3 from './beaches/Arugam Bay 3.jpg?url'
import MirissaBeach from './beaches/Mirissa Beach, Mirissa.jpg?url'
import UnuwatunaBreach from './beaches/Unuwatuna Beach.jpg?url'
import MidigamaBeach from './beaches/Midigama Beach.jpg?url'
import TangalleRoad from './beaches/Tangalle Rd, Tangalle.jpg?url'
import Trincomalee from './beaches/Trincomalee.jpg?url'
import Trincomalee2 from './beaches/Trincomalee 2.jpg?url'
import CoconutTreeHill from './beaches/Coconut Tree Hill.jpg?url'
import CoconutTreeHillRoad from './beaches/Coconut Tree Hill Rd, Mirissa.jpg?url'
import WeligamaPool from './beaches/Iconic Smiling Moon Pool Weligama.jpg?url'

// Temples & Religious Sites
import DambullaCave from './temples/Dambulla Cave Temple, Dambulla.jpg?url'
import AbhayagiriMonastery from './temples/Abhayagiri Monastery.jpg?url'
import IsurumunuViharaya from './temples/Isurumuni Rajamaha Viharaya, Anuradhapura.jpg?url'
import IsurumunuViharaya2 from './temples/Isurumuni Rajamaha Viharaya, Anuradhapura 2.jpg?url'
import Jetavanaramaya from './temples/Jetavanaramaya.jpg?url'
import RuwanweliSeya from './temples/Ruwanweli Maha Seya, Anuradhapura.jpg?url'
import Ruwanweliseya from './temples/Ruwanweliseya, Anuradhapura.jpg?url'
import BodhiTree from './temples/Sri Maha Bodhi Tree.jpg?url'
import Thuparamaya from './temples/Thuparamaya.jpg?url'
import RedMosque from './temples/Red Mosque.jpg?url'
import MoonstoneGuardStones from './temples/Moonstone and guard stones.jpg?url'

// Wildlife
import ElephantOrphanage from './wildlife/Elephant orphanage, Rambukkana.jpg?url'
import ElephantSabaragamuwa from './wildlife/Elephant, Sabaragamuwa Province.jpg?url'
import PinnawalaZoo from './wildlife/Pinnawala Zoo, B199, Rambukkana.jpg?url'
import YalaNationalPark from './wildlife/Yala National Park, Hambantota.jpg?url'
import LahugalaAmpara from './wildlife/Lahugala, Ampara.jpg?url'

// Landscapes
import Mountain from './landscapes/Mountain.jpg?url'
import AmbuluwawaMountain from './landscapes/Ambuluwawa Mountain Hill.jpg?url'
import GerandiEllaFalls from './landscapes/Gerandi Ella Falls.jpg?url'
import LiptonSeatRoad from './landscapes/Lipton Seat Road.jpg?url'
import LiptonsSeat from './landscapes/Liptons Seat, Haputale.jpg?url'
import NelligalaRoad from './landscapes/Nelligala Rd, Kandy.jpg?url'

// Transportation
import TrainNanuoya from './transportation/Train, Nanuoya.jpg?url'
import TrainWelimada from './transportation/Train, Welimada.jpg?url'
import NineArchBridge from './transportation/Nine Arch Bridge.jpg?url'
import NineArchBridge2 from './transportation/Nine Arch Bridge 2.jpg?url'

// Culture
import EsalaPerahera from './culture/Esala Perahera, Kandy.jpg?url'
import LotusTower from './culture/Lotus Tower, Colombo.jpg?url'
import Tissamaharama from './culture/Tissamaharama.jpg?url'

// Export organized image collections
export const destinationImages = {
  sigiriya: {
    main: Sigiriya,
    alternate: Sigiriya2,
    aerial: Sigiriya3,
  },
  kandy: {
    highway: KandyHighway,
  },
  ella: {
    littleAdamsPeak: EllaView,
  },
  nuwaraEliya: {
    main: NuwaraEliya,
  },
  galle: {
    main: Galle,
    alternate: Galle2,
    fort: GalleFort,
    fortAlternate: GalleFort2,
    fortAerial: GalleFort3,
  },
}

export const beachImages = {
  general: Beach,
  arugamBay: {
    main: ArugamBay,
    view2: ArugamBay2,
    view3: ArugamBay3,
  },
  mirissa: {
    beach: MirissaBeach,
    coconutHill: CoconutTreeHill,
    coconutHillRoad: CoconutTreeHillRoad,
  },
  unuwatuna: UnuwatunaBreach,
  midigama: MidigamaBeach,
  tangalle: TangalleRoad,
  trincomalee: {
    main: Trincomalee,
    alternate: Trincomalee2,
  },
  weligama: WeligamaPool,
}

export const templeImages = {
  dambulla: DambullaCave,
  anuradhapura: {
    abhayagiri: AbhayagiriMonastery,
    isurumunu: IsurumunuViharaya,
    isurumumuAlternate: IsurumunuViharaya2,
    jetavanaramaya: Jetavanaramaya,
    ruwanweliSeya: RuwanweliSeya,
    ruwanweliseya: Ruwanweliseya,
    bodhiTree: BodhiTree,
    thuparamaya: Thuparamaya,
  },
  redMosque: RedMosque,
  moonstone: MoonstoneGuardStones,
}

export const wildlifeImages = {
  elephants: {
    orphanage: ElephantOrphanage,
    sabaragamuwa: ElephantSabaragamuwa,
    pinnawala: PinnawalaZoo,
  },
  nationalParks: {
    yala: YalaNationalPark,
    lahugala: LahugalaAmpara,
  },
}

export const landscapeImages = {
  mountains: {
    general: Mountain,
    ambuluwawa: AmbuluwawaMountain,
  },
  waterfalls: {
    gerandiElla: GerandiEllaFalls,
  },
  teaCountry: {
    liptonSeatRoad: LiptonSeatRoad,
    liptonsSeat: LiptonsSeat,
  },
  kandy: {
    nelligalaRoad: NelligalaRoad,
  },
}

export const transportationImages = {
  trains: {
    nanuoya: TrainNanuoya,
    welimada: TrainWelimada,
  },
  bridges: {
    nineArch: NineArchBridge,
    nineArchAlternate: NineArchBridge2,
  },
}

export const cultureImages = {
  festivals: {
    esalaPerahera: EsalaPerahera,
  },
  landmarks: {
    lotusTower: LotusTower,
  },
  cities: {
    tissamaharama: Tissamaharama,
  },
}

// Export all images for easy access
export const allImages = {
  destinations: destinationImages,
  beaches: beachImages,
  temples: templeImages,
  wildlife: wildlifeImages,
  landscapes: landscapeImages,
  transportation: transportationImages,
  culture: cultureImages,
}

// Helper function to get image by category and name
export const getImage = (category: keyof typeof allImages, subcategory: string, imageName?: string) => {
  const categoryImages = allImages[category] as any
  if (!categoryImages) return null
  
  const subcategoryImages = categoryImages[subcategory]
  if (!subcategoryImages) return null
  
  if (imageName && typeof subcategoryImages === 'object') {
    return subcategoryImages[imageName] || null
  }
  
  return subcategoryImages
}

// Missing images that need to be sourced
export const missingImages = {
  destinations: [
    'Colombo skyline and cityscape',
    'Polonnaruwa ancient ruins',
    'Anuradhapura ancient city overview',
    'Dambulla Golden Temple exterior',
    'Pinnawala Elephant Orphanage entrance',
    'Horton Plains National Park',
    'Adams Peak pilgrimage trail',
  ],
  beaches: [
    'Bentota beach activities',
    'Hikkaduwa coral reef snorkeling',
    'Negombo fishing boats',
    'Kalpitiya kite surfing',
    'Pasikudah bay aerial view',
  ],
  activities: [
    'White water rafting in Kitulgala',
    'Tea plantation workers picking tea',
    'Spice garden tour in Matale',
    'Whale watching boat tour',
    'Safari jeep in national park',
    'Traditional Sri Lankan cooking class',
    'Ayurvedic spa treatment',
  ],
  culture: [
    'Traditional Kandyan dancers',
    'Sri Lankan traditional wedding',
    'Local market with spices and fruits',
    'Traditional craftsmen at work',
    'Buddhist monks in meditation',
    'Hindu temple ceremony',
  ],
  food: [
    'Traditional Sri Lankan rice and curry',
    'String hoppers (idiyappam)',
    'Kottu roti preparation',
    'Fresh tropical fruits display',
    'Ceylon tea plantation and tasting',
    'Street food vendors',
  ],
}
