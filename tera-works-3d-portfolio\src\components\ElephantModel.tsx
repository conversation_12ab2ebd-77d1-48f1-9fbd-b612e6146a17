import { useRef, forwardRef } from 'react';
import { useFrame } from '@react-three/fiber';
import { useSpring, animated } from '@react-spring/three';
import * as THREE from 'three';

interface ElephantModelProps {
  position?: [number, number, number];
  scale?: number;
  rotation?: [number, number, number];
  animate?: boolean;
}

export const ElephantModel = forwardRef<THREE.Group, ElephantModelProps>(({
  position = [0, 0, 0],
  scale = 1,
  rotation = [0, 0, 0],
  animate = true
}, ref) => {
  const groupRef = useRef<THREE.Group>(null);
  const bodyRef = useRef<THREE.Mesh>(null);
  const headRef = useRef<THREE.Mesh>(null);
  const trunkRef = useRef<THREE.Mesh>(null);
  const leftEarRef = useRef<THREE.Mesh>(null);
  const rightEarRef = useRef<THREE.Mesh>(null);
  const leftTuskRef = useRef<THREE.Mesh>(null);
  const rightTuskRef = useRef<THREE.Mesh>(null);

  // Entrance animation
  const { animationScale, animationY } = useSpring({
    animationScale: animate ? scale : 0,
    animationY: animate ? position[1] : position[1] - 2,
    config: { mass: 1, tension: 280, friction: 60 },
    delay: 500
  });

  // Idle animation
  useFrame((state) => {
    if (!animate || !groupRef.current) return;

    const time = state.clock.elapsedTime;
    
    // Gentle floating motion
    groupRef.current.position.y = position[1] + Math.sin(time * 0.5) * 0.1;
    
    // Subtle rotation
    groupRef.current.rotation.y = rotation[1] + Math.sin(time * 0.3) * 0.05;
    
    // Trunk swaying
    if (trunkRef.current) {
      trunkRef.current.rotation.z = Math.sin(time * 0.8) * 0.1;
    }
    
    // Ear flapping (very subtle)
    if (leftEarRef.current && rightEarRef.current) {
      const earMovement = Math.sin(time * 1.2) * 0.05;
      leftEarRef.current.rotation.z = earMovement;
      rightEarRef.current.rotation.z = -earMovement;
    }
  });

  return (
    <animated.group
      ref={ref || groupRef}
      position={[position[0], animationY as any, position[2]]}
      scale={animationScale as any}
      rotation={rotation}
    >
      {/* Main Body */}
      <mesh ref={bodyRef} position={[0, -0.5, 0]} castShadow receiveShadow>
        <sphereGeometry args={[1, 32, 32]} />
        <meshStandardMaterial
          color="#6BB6FF"
          metalness={0.3}
          roughness={0.4}
          emissive="#2B7FE6"
          emissiveIntensity={0.1}
        />
      </mesh>

      {/* Head */}
      <mesh ref={headRef} position={[0, 0.3, 0.6]} castShadow receiveShadow>
        <sphereGeometry args={[0.6, 32, 32]} />
        <meshStandardMaterial
          color="#4A9FFF"
          metalness={0.3}
          roughness={0.4}
          emissive="#1A5CC7"
          emissiveIntensity={0.1}
        />
      </mesh>

      {/* Trunk */}
      <mesh ref={trunkRef} position={[0, 0, 1.1]} castShadow receiveShadow>
        <cylinderGeometry args={[0.15, 0.25, 1, 16]} />
        <meshStandardMaterial
          color="#4A9FFF"
          metalness={0.3}
          roughness={0.4}
        />
      </mesh>

      {/* Left Ear */}
      <mesh ref={leftEarRef} position={[-0.5, 0.4, 0.3]} rotation={[0, 0, -0.3]} castShadow receiveShadow>
        <sphereGeometry args={[0.4, 16, 16]} />
        <meshStandardMaterial
          color="#7FC7FF"
          metalness={0.2}
          roughness={0.5}
        />
      </mesh>

      {/* Right Ear */}
      <mesh ref={rightEarRef} position={[0.5, 0.4, 0.3]} rotation={[0, 0, 0.3]} castShadow receiveShadow>
        <sphereGeometry args={[0.4, 16, 16]} />
        <meshStandardMaterial
          color="#7FC7FF"
          metalness={0.2}
          roughness={0.5}
        />
      </mesh>

      {/* Left Tusk */}
      <mesh ref={leftTuskRef} position={[-0.2, 0.1, 1.0]} rotation={[0.3, -0.2, 0]} castShadow receiveShadow>
        <cylinderGeometry args={[0.03, 0.05, 0.6, 8]} />
        <meshStandardMaterial
          color="#C8CDD4"
          metalness={0.8}
          roughness={0.1}
          emissive="#E8EDF4"
          emissiveIntensity={0.05}
        />
      </mesh>

      {/* Right Tusk */}
      <mesh ref={rightTuskRef} position={[0.2, 0.1, 1.0]} rotation={[0.3, 0.2, 0]} castShadow receiveShadow>
        <cylinderGeometry args={[0.03, 0.05, 0.6, 8]} />
        <meshStandardMaterial
          color="#C8CDD4"
          metalness={0.8}
          roughness={0.1}
          emissive="#E8EDF4"
          emissiveIntensity={0.05}
        />
      </mesh>

      {/* Eyes */}
      <mesh position={[-0.25, 0.45, 0.85]} castShadow>
        <sphereGeometry args={[0.08, 16, 16]} />
        <meshStandardMaterial color="#1A1B3A" emissive="#4A9FFF" emissiveIntensity={0.2} />
      </mesh>
      
      <mesh position={[0.25, 0.45, 0.85]} castShadow>
        <sphereGeometry args={[0.08, 16, 16]} />
        <meshStandardMaterial color="#1A1B3A" emissive="#4A9FFF" emissiveIntensity={0.2} />
      </mesh>

      {/* Legs */}
      {[
        [-0.6, -1.2, 0.3],
        [0.6, -1.2, 0.3],
        [-0.6, -1.2, -0.3],
        [0.6, -1.2, -0.3]
      ].map((pos, index) => (
        <mesh key={index} position={pos as [number, number, number]} castShadow receiveShadow>
          <cylinderGeometry args={[0.2, 0.25, 0.8, 16]} />
          <meshStandardMaterial
            color="#4A9FFF"
            metalness={0.3}
            roughness={0.4}
          />
        </mesh>
      ))}
    </animated.group>
  );
});

ElephantModel.displayName = 'ElephantModel';

// Elephant model complete
