{"version": 3, "sources": ["../../src/index.ts", "../../src/Animated.ts", "../../src/AnimatedValue.ts", "../../src/AnimatedString.ts", "../../src/AnimatedArray.ts", "../../src/AnimatedObject.ts", "../../src/context.ts", "../../src/getAnimatedType.ts", "../../src/createHost.ts", "../../src/withAnimated.tsx"], "sourcesContent": ["export * from './Animated'\nexport * from './AnimatedValue'\nexport * from './AnimatedString'\nexport * from './AnimatedArray'\nexport * from './AnimatedObject'\nexport * from './getAnimatedType'\nexport * from './createHost'\nexport * from './types'\n", "import { defineHidden } from '@react-spring/shared'\nimport { AnimatedValue } from './AnimatedValue'\n\nconst $node: any = Symbol.for('Animated:node')\n\nexport const isAnimated = <T = any>(value: any): value is Animated<T> =>\n  !!value && value[$node] === value\n\n/** Get the owner's `Animated` node. */\nexport const getAnimated = <T = any>(owner: any): Animated<T> | undefined =>\n  owner && owner[$node]\n\n/** Set the owner's `Animated` node. */\nexport const setAnimated = (owner: any, node: Animated) =>\n  defineHidden(owner, $node, node)\n\n/** Get every `AnimatedValue` in the owner's `Animated` node. */\nexport const getPayload = (owner: any): AnimatedValue[] | undefined =>\n  owner && owner[$node] && owner[$node].getPayload()\n\nexport abstract class Animated<T = any> {\n  /** The cache of animated values */\n  protected payload?: Payload\n\n  constructor() {\n    // This makes \"isAnimated\" return true.\n    setAnimated(this, this)\n  }\n\n  /** Get the current value. Pass `true` for only animated values. */\n  abstract getValue(animated?: boolean): T\n\n  /** Set the current value. Returns `true` if the value changed. */\n  abstract setValue(value: T): boolean | void\n\n  /** Reset any animation state. */\n  abstract reset(goal?: T): void\n\n  /** Get every `AnimatedValue` used by this node. */\n  getPayload(): Payload {\n    return this.payload || []\n  }\n}\n\nexport type Payload = readonly AnimatedValue[]\n", "import { is } from '@react-spring/shared'\nimport { Animated, Payload } from './Animated'\n\n/** An animated number or a native attribute value */\nexport class AnimatedValue<T = any> extends Animated {\n  done = true\n  elapsedTime!: number\n  lastPosition!: number\n  lastVelocity?: number | null\n  v0?: number | null\n  durationProgress = 0\n\n  constructor(protected _value: T) {\n    super()\n    if (is.num(this._value)) {\n      this.lastPosition = this._value\n    }\n  }\n\n  /** @internal */\n  static create(value: any) {\n    return new AnimatedValue(value)\n  }\n\n  getPayload(): Payload {\n    return [this]\n  }\n\n  getValue() {\n    return this._value\n  }\n\n  setValue(value: T, step?: number) {\n    if (is.num(value)) {\n      this.lastPosition = value\n      if (step) {\n        value = (Math.round(value / step) * step) as any\n        if (this.done) {\n          this.lastPosition = value as any\n        }\n      }\n    }\n    if (this._value === value) {\n      return false\n    }\n    this._value = value\n    return true\n  }\n\n  reset() {\n    const { done } = this\n    this.done = false\n    if (is.num(this._value)) {\n      this.elapsedTime = 0\n      this.durationProgress = 0\n      this.lastPosition = this._value\n      if (done) this.lastVelocity = null\n      this.v0 = null\n    }\n  }\n}\n", "import { AnimatedValue } from './AnimatedValue'\nimport { is, createInterpolator } from '@react-spring/shared'\n\ntype Value = string | number\n\nexport class AnimatedString extends AnimatedValue<Value> {\n  protected declare _value: number\n  protected _string: string | null = null\n  protected _toString: (input: number) => string\n\n  constructor(value: string) {\n    super(0)\n    this._toString = createInterpolator({\n      output: [value, value],\n    })\n  }\n\n  /** @internal */\n  static create(value: string) {\n    return new AnimatedString(value)\n  }\n\n  getValue() {\n    const value = this._string\n    return value == null ? (this._string = this._toString(this._value)) : value\n  }\n\n  setValue(value: Value) {\n    if (is.str(value)) {\n      if (value == this._string) {\n        return false\n      }\n      this._string = value\n      this._value = 1\n    } else if (super.setValue(value)) {\n      this._string = null\n    } else {\n      return false\n    }\n    return true\n  }\n\n  reset(goal?: string) {\n    if (goal) {\n      this._toString = createInterpolator({\n        output: [this.getValue(), goal],\n      })\n    }\n    this._value = 0\n    super.reset()\n  }\n}\n", "import { isAnimatedString } from '@react-spring/shared'\nimport { AnimatedObject } from './AnimatedObject'\nimport { AnimatedString } from './AnimatedString'\nimport { AnimatedValue } from './AnimatedValue'\n\ntype Value = number | string\ntype Source = AnimatedValue<Value>[]\n\n/** An array of animated nodes */\nexport class AnimatedArray<\n  T extends ReadonlyArray<Value> = Value[],\n> extends AnimatedObject {\n  protected declare source: Source\n  constructor(source: T) {\n    super(source)\n  }\n\n  /** @internal */\n  static create<T extends ReadonlyArray<Value>>(source: T) {\n    return new AnimatedArray(source)\n  }\n\n  getValue(): T {\n    return this.source.map(node => node.getValue()) as any\n  }\n\n  setValue(source: T) {\n    const payload = this.getPayload()\n    // Reuse the payload when lengths are equal.\n    if (source.length == payload.length) {\n      return payload.map((node, i) => node.setValue(source[i])).some(Boolean)\n    }\n    // Remake the payload when length changes.\n    super.setValue(source.map(makeAnimated))\n    return true\n  }\n}\n\nfunction makeAnimated(value: any) {\n  const nodeType = isAnimatedString(value) ? AnimatedString : AnimatedValue\n  return nodeType.create(value)\n}\n", "import { Lookup } from '@react-spring/types'\nimport {\n  each,\n  eachProp,\n  getFluidValue,\n  hasFluidValue,\n} from '@react-spring/shared'\nimport { Animated, isAnimated, getPayload } from './Animated'\nimport { AnimatedValue } from './AnimatedValue'\nimport { TreeContext } from './context'\n\n/** An object containing `Animated` nodes */\nexport class AnimatedObject extends Animated {\n  constructor(protected source: Lookup) {\n    super()\n    this.setValue(source)\n  }\n\n  getValue(animated?: boolean) {\n    const values: Lookup = {}\n    eachProp(this.source, (source, key) => {\n      if (isAnimated(source)) {\n        values[key] = source.getValue(animated)\n      } else if (hasFluidValue(source)) {\n        values[key] = getFluidValue(source)\n      } else if (!animated) {\n        values[key] = source\n      }\n    })\n    return values\n  }\n\n  /** Replace the raw object data */\n  setValue(source: Lookup) {\n    this.source = source\n    this.payload = this._makePayload(source)\n  }\n\n  reset() {\n    if (this.payload) {\n      each(this.payload, node => node.reset())\n    }\n  }\n\n  /** Create a payload set. */\n  protected _makePayload(source: Lookup) {\n    if (source) {\n      const payload = new Set<AnimatedValue>()\n      eachProp(source, this._addToPayload, payload)\n      return Array.from(payload)\n    }\n  }\n\n  /** Add to a payload set. */\n  protected _addToPayload(this: Set<AnimatedValue>, source: any) {\n    if (TreeContext.dependencies && hasFluidValue(source)) {\n      TreeContext.dependencies.add(source)\n    }\n    const payload = getPayload(source)\n    if (payload) {\n      each(payload, node => this.add(node))\n    }\n  }\n}\n", "import { FluidValue } from '@react-spring/shared'\n\nexport type TreeContext = {\n  /**\n   * Any animated values found when updating the payload of an `AnimatedObject`\n   * are also added to this `Set` to be observed by an animated component.\n   */\n  dependencies: Set<FluidValue> | null\n}\n\nexport const TreeContext: TreeContext = { dependencies: null }\n", "import { is, isAnimatedString } from '@react-spring/shared'\nimport { AnimatedType } from './types'\nimport { AnimatedArray } from './AnimatedArray'\nimport { AnimatedString } from './AnimatedString'\nimport { AnimatedValue } from './AnimatedValue'\nimport { getAnimated } from './Animated'\n\n/** Return the `Animated` node constructor for a given value */\nexport function getAnimatedType(value: any): AnimatedType {\n  const parentNode = getAnimated(value)\n  return parentNode\n    ? (parentNode.constructor as any)\n    : is.arr(value)\n      ? AnimatedArray\n      : isAnimatedString(value)\n        ? AnimatedString\n        : AnimatedValue\n}\n", "import { Lookup } from '@react-spring/types'\nimport { is, eachProp } from '@react-spring/shared'\nimport { AnimatableComponent, withAnimated } from './withAnimated'\nimport { Animated } from './Animated'\nimport { AnimatedObject } from './AnimatedObject'\n\nexport interface HostConfig {\n  /** Provide custom logic for native updates */\n  applyAnimatedValues: (node: any, props: Lookup) => boolean | void\n  /** Wrap the `style` prop with an animated node */\n  createAnimatedStyle: (style: Lookup) => Animated\n  /** Intercept props before they're passed to an animated component */\n  getComponentProps: (props: Lookup) => typeof props\n}\n\n// A stub type that gets replaced by @react-spring/web and others.\ntype WithAnimated = {\n  (Component: AnimatableComponent): any\n  [key: string]: any\n}\n\n// For storing the animated version on the original component\nconst cacheKey = Symbol.for('AnimatedComponent')\n\nexport const createHost = (\n  components: AnimatableComponent[] | { [key: string]: AnimatableComponent },\n  {\n    applyAnimatedValues = () => false,\n    createAnimatedStyle = style => new AnimatedObject(style),\n    getComponentProps = props => props,\n  }: Partial<HostConfig> = {}\n) => {\n  const hostConfig: HostConfig = {\n    applyAnimatedValues,\n    createAnimatedStyle,\n    getComponentProps,\n  }\n\n  const animated: WithAnimated = (Component: any) => {\n    const displayName = getDisplayName(Component) || 'Anonymous'\n\n    if (is.str(Component)) {\n      Component =\n        animated[Component] ||\n        (animated[Component] = withAnimated(Component, hostConfig))\n    } else {\n      Component =\n        Component[cacheKey] ||\n        (Component[cacheKey] = withAnimated(Component, hostConfig))\n    }\n\n    Component.displayName = `Animated(${displayName})`\n    return Component\n  }\n\n  eachProp(components, (Component, key) => {\n    if (is.arr(components)) {\n      key = getDisplayName(Component)!\n    }\n    animated[key] = animated(Component)\n  })\n\n  return {\n    animated,\n  }\n}\n\nconst getDisplayName = (arg: AnimatableComponent) =>\n  is.str(arg)\n    ? arg\n    : arg && is.str(arg.displayName)\n      ? arg.displayName\n      : (is.fun(arg) && arg.name) || null\n", "import * as React from 'react'\nimport { forwardRef, useRef, Ref, useCallback, useEffect } from 'react'\nimport {\n  is,\n  each,\n  raf,\n  useForceUpdate,\n  useOnce,\n  FluidEvent,\n  FluidValue,\n  addFluidObserver,\n  removeFluidObserver,\n  useIsomorphicLayoutEffect,\n} from '@react-spring/shared'\nimport { ElementType } from '@react-spring/types'\n\nimport { AnimatedObject } from './AnimatedObject'\nimport { TreeContext } from './context'\nimport { HostConfig } from './createHost'\n\nexport type AnimatableComponent = string | Exclude<ElementType, string>\n\nexport const withAnimated = (Component: any, host: HostConfig) => {\n  const hasInstance: boolean =\n    // Function components must use \"forwardRef\" to avoid being\n    // re-rendered on every animation frame.\n    !is.fun(Component) ||\n    (Component.prototype && Component.prototype.isReactComponent)\n\n  return forwardRef((givenProps: any, givenRef: Ref<any>) => {\n    const instanceRef = useRef<any>(null)\n\n    // The `hasInstance` value is constant, so we can safely avoid\n    // the `useCallback` invocation when `hasInstance` is false.\n    const ref =\n      hasInstance &&\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      useCallback(\n        (value: any) => {\n          instanceRef.current = updateRef(givenRef, value)\n        },\n        [givenRef]\n      )\n\n    const [props, deps] = getAnimatedState(givenProps, host)\n\n    const forceUpdate = useForceUpdate()\n\n    const callback = () => {\n      const instance = instanceRef.current\n      if (hasInstance && !instance) {\n        // Either this component was unmounted before changes could be\n        // applied, or the wrapped component forgot to forward its ref.\n        return\n      }\n\n      const didUpdate = instance\n        ? host.applyAnimatedValues(instance, props.getValue(true))\n        : false\n\n      // Re-render the component when native updates fail.\n      if (didUpdate === false) {\n        forceUpdate()\n      }\n    }\n\n    const observer = new PropsObserver(callback, deps)\n\n    const observerRef = useRef<PropsObserver>()\n    useIsomorphicLayoutEffect(() => {\n      observerRef.current = observer\n\n      // Observe the latest dependencies.\n      each(deps, dep => addFluidObserver(dep, observer))\n\n      return () => {\n        // Stop observing previous dependencies.\n        if (observerRef.current) {\n          each(observerRef.current.deps, dep =>\n            removeFluidObserver(dep, observerRef.current!)\n          )\n          raf.cancel(observerRef.current.update)\n        }\n      }\n    })\n\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    useEffect(callback, [])\n    // Stop observing on unmount.\n    useOnce(() => () => {\n      const observer = observerRef.current!\n      each(observer.deps, dep => removeFluidObserver(dep, observer))\n    })\n\n    const usedProps = host.getComponentProps(props.getValue())\n    return <Component {...usedProps} ref={ref} />\n  })\n}\n\nclass PropsObserver {\n  constructor(\n    readonly update: () => void,\n    readonly deps: Set<FluidValue>\n  ) {}\n  eventObserved(event: FluidEvent) {\n    if (event.type == 'change') {\n      raf.write(this.update)\n    }\n  }\n}\n\ntype AnimatedState = [props: AnimatedObject, dependencies: Set<FluidValue>]\n\nfunction getAnimatedState(props: any, host: HostConfig): AnimatedState {\n  const dependencies = new Set<FluidValue>()\n  TreeContext.dependencies = dependencies\n\n  // Search the style for dependencies.\n  if (props.style)\n    props = {\n      ...props,\n      style: host.createAnimatedStyle(props.style),\n    }\n\n  // Search the props for dependencies.\n  props = new AnimatedObject(props)\n\n  TreeContext.dependencies = null\n  return [props, dependencies]\n}\n\nfunction updateRef<T>(ref: Ref<T>, value: T) {\n  if (ref) {\n    if (is.fun(ref)) ref(value)\n    else (ref as any).current = value\n  }\n  return value\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,oBAA6B;AAG7B,IAAM,QAAa,OAAO,IAAI,eAAe;AAEtC,IAAM,aAAa,CAAU,UAClC,CAAC,CAAC,SAAS,MAAM,KAAK,MAAM;AAGvB,IAAM,cAAc,CAAU,UACnC,SAAS,MAAM,KAAK;AAGf,IAAM,cAAc,CAAC,OAAY,aACtC,4BAAa,OAAO,OAAO,IAAI;AAG1B,IAAM,aAAa,CAAC,UACzB,SAAS,MAAM,KAAK,KAAK,MAAM,KAAK,EAAE,WAAW;AAE5C,IAAe,WAAf,MAAiC;AAAA,EAItC,cAAc;AAEZ,gBAAY,MAAM,IAAI;AAAA,EACxB;AAAA;AAAA,EAYA,aAAsB;AACpB,WAAO,KAAK,WAAW,CAAC;AAAA,EAC1B;AACF;;;AC1CA,IAAAA,iBAAmB;AAIZ,IAAM,gBAAN,cAAqC,SAAS;AAAA,EAQnD,YAAsB,QAAW;AAC/B,UAAM;AADc;AAPtB,gBAAO;AAKP,4BAAmB;AAIjB,QAAI,kBAAG,IAAI,KAAK,MAAM,GAAG;AACvB,WAAK,eAAe,KAAK;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA,EAGA,OAAO,OAAO,OAAY;AACxB,WAAO,IAAI,cAAc,KAAK;AAAA,EAChC;AAAA,EAEA,aAAsB;AACpB,WAAO,CAAC,IAAI;AAAA,EACd;AAAA,EAEA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,SAAS,OAAU,MAAe;AAChC,QAAI,kBAAG,IAAI,KAAK,GAAG;AACjB,WAAK,eAAe;AACpB,UAAI,MAAM;AACR,gBAAS,KAAK,MAAM,QAAQ,IAAI,IAAI;AACpC,YAAI,KAAK,MAAM;AACb,eAAK,eAAe;AAAA,QACtB;AAAA,MACF;AAAA,IACF;AACA,QAAI,KAAK,WAAW,OAAO;AACzB,aAAO;AAAA,IACT;AACA,SAAK,SAAS;AACd,WAAO;AAAA,EACT;AAAA,EAEA,QAAQ;AACN,UAAM,EAAE,KAAK,IAAI;AACjB,SAAK,OAAO;AACZ,QAAI,kBAAG,IAAI,KAAK,MAAM,GAAG;AACvB,WAAK,cAAc;AACnB,WAAK,mBAAmB;AACxB,WAAK,eAAe,KAAK;AACzB,UAAI;AAAM,aAAK,eAAe;AAC9B,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AACF;;;AC3DA,IAAAC,iBAAuC;AAIhC,IAAM,iBAAN,cAA6B,cAAqB;AAAA,EAKvD,YAAY,OAAe;AACzB,UAAM,CAAC;AAJT,SAAU,UAAyB;AAKjC,SAAK,gBAAY,mCAAmB;AAAA,MAClC,QAAQ,CAAC,OAAO,KAAK;AAAA,IACvB,CAAC;AAAA,EACH;AAAA;AAAA,EAGA,OAAO,OAAO,OAAe;AAC3B,WAAO,IAAI,eAAe,KAAK;AAAA,EACjC;AAAA,EAEA,WAAW;AACT,UAAM,QAAQ,KAAK;AACnB,WAAO,SAAS,OAAQ,KAAK,UAAU,KAAK,UAAU,KAAK,MAAM,IAAK;AAAA,EACxE;AAAA,EAEA,SAAS,OAAc;AACrB,QAAI,kBAAG,IAAI,KAAK,GAAG;AACjB,UAAI,SAAS,KAAK,SAAS;AACzB,eAAO;AAAA,MACT;AACA,WAAK,UAAU;AACf,WAAK,SAAS;AAAA,IAChB,WAAW,MAAM,SAAS,KAAK,GAAG;AAChC,WAAK,UAAU;AAAA,IACjB,OAAO;AACL,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,MAAe;AACnB,QAAI,MAAM;AACR,WAAK,gBAAY,mCAAmB;AAAA,QAClC,QAAQ,CAAC,KAAK,SAAS,GAAG,IAAI;AAAA,MAChC,CAAC;AAAA,IACH;AACA,SAAK,SAAS;AACd,UAAM,MAAM;AAAA,EACd;AACF;;;ACnDA,IAAAC,iBAAiC;;;ACCjC,IAAAC,iBAKO;;;ACIA,IAAM,cAA2B,EAAE,cAAc,KAAK;;;ADEtD,IAAM,iBAAN,cAA6B,SAAS;AAAA,EAC3C,YAAsB,QAAgB;AACpC,UAAM;AADc;AAEpB,SAAK,SAAS,MAAM;AAAA,EACtB;AAAA,EAEA,SAAS,UAAoB;AAC3B,UAAM,SAAiB,CAAC;AACxB,iCAAS,KAAK,QAAQ,CAAC,QAAQ,QAAQ;AACrC,UAAI,WAAW,MAAM,GAAG;AACtB,eAAO,GAAG,IAAI,OAAO,SAAS,QAAQ;AAAA,MACxC,eAAW,8BAAc,MAAM,GAAG;AAChC,eAAO,GAAG,QAAI,8BAAc,MAAM;AAAA,MACpC,WAAW,CAAC,UAAU;AACpB,eAAO,GAAG,IAAI;AAAA,MAChB;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,SAAS,QAAgB;AACvB,SAAK,SAAS;AACd,SAAK,UAAU,KAAK,aAAa,MAAM;AAAA,EACzC;AAAA,EAEA,QAAQ;AACN,QAAI,KAAK,SAAS;AAChB,+BAAK,KAAK,SAAS,UAAQ,KAAK,MAAM,CAAC;AAAA,IACzC;AAAA,EACF;AAAA;AAAA,EAGU,aAAa,QAAgB;AACrC,QAAI,QAAQ;AACV,YAAM,UAAU,oBAAI,IAAmB;AACvC,mCAAS,QAAQ,KAAK,eAAe,OAAO;AAC5C,aAAO,MAAM,KAAK,OAAO;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA,EAGU,cAAwC,QAAa;AAC7D,QAAI,YAAY,oBAAgB,8BAAc,MAAM,GAAG;AACrD,kBAAY,aAAa,IAAI,MAAM;AAAA,IACrC;AACA,UAAM,UAAU,WAAW,MAAM;AACjC,QAAI,SAAS;AACX,+BAAK,SAAS,UAAQ,KAAK,IAAI,IAAI,CAAC;AAAA,IACtC;AAAA,EACF;AACF;;;ADtDO,IAAM,gBAAN,cAEG,eAAe;AAAA,EAEvB,YAAY,QAAW;AACrB,UAAM,MAAM;AAAA,EACd;AAAA;AAAA,EAGA,OAAO,OAAuC,QAAW;AACvD,WAAO,IAAI,cAAc,MAAM;AAAA,EACjC;AAAA,EAEA,WAAc;AACZ,WAAO,KAAK,OAAO,IAAI,UAAQ,KAAK,SAAS,CAAC;AAAA,EAChD;AAAA,EAEA,SAAS,QAAW;AAClB,UAAM,UAAU,KAAK,WAAW;AAEhC,QAAI,OAAO,UAAU,QAAQ,QAAQ;AACnC,aAAO,QAAQ,IAAI,CAAC,MAAM,MAAM,KAAK,SAAS,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO;AAAA,IACxE;AAEA,UAAM,SAAS,OAAO,IAAI,YAAY,CAAC;AACvC,WAAO;AAAA,EACT;AACF;AAEA,SAAS,aAAa,OAAY;AAChC,QAAM,eAAW,iCAAiB,KAAK,IAAI,iBAAiB;AAC5D,SAAO,SAAS,OAAO,KAAK;AAC9B;;;AGzCA,IAAAC,iBAAqC;AAQ9B,SAAS,gBAAgB,OAA0B;AACxD,QAAM,aAAa,YAAY,KAAK;AACpC,SAAO,aACF,WAAW,cACZ,kBAAG,IAAI,KAAK,IACV,oBACA,iCAAiB,KAAK,IACpB,iBACA;AACV;;;AChBA,IAAAC,iBAA6B;;;ACD7B,YAAuB;AACvB,mBAAgE;AAChE,IAAAC,iBAWO;AASA,IAAM,eAAe,CAAC,WAAgB,SAAqB;AAChE,QAAM;AAAA;AAAA;AAAA,IAGJ,CAAC,kBAAG,IAAI,SAAS,KAChB,UAAU,aAAa,UAAU,UAAU;AAAA;AAE9C,aAAO,yBAAW,CAAC,YAAiB,aAAuB;AACzD,UAAM,kBAAc,qBAAY,IAAI;AAIpC,UAAM,MACJ;AAAA,QAEA;AAAA,MACE,CAAC,UAAe;AACd,oBAAY,UAAU,UAAU,UAAU,KAAK;AAAA,MACjD;AAAA,MACA,CAAC,QAAQ;AAAA,IACX;AAEF,UAAM,CAAC,OAAO,IAAI,IAAI,iBAAiB,YAAY,IAAI;AAEvD,UAAM,kBAAc,+BAAe;AAEnC,UAAM,WAAW,MAAM;AACrB,YAAM,WAAW,YAAY;AAC7B,UAAI,eAAe,CAAC,UAAU;AAG5B;AAAA,MACF;AAEA,YAAM,YAAY,WACd,KAAK,oBAAoB,UAAU,MAAM,SAAS,IAAI,CAAC,IACvD;AAGJ,UAAI,cAAc,OAAO;AACvB,oBAAY;AAAA,MACd;AAAA,IACF;AAEA,UAAM,WAAW,IAAI,cAAc,UAAU,IAAI;AAEjD,UAAM,kBAAc,qBAAsB;AAC1C,kDAA0B,MAAM;AAC9B,kBAAY,UAAU;AAGtB,+BAAK,MAAM,aAAO,iCAAiB,KAAK,QAAQ,CAAC;AAEjD,aAAO,MAAM;AAEX,YAAI,YAAY,SAAS;AACvB;AAAA,YAAK,YAAY,QAAQ;AAAA,YAAM,aAC7B,oCAAoB,KAAK,YAAY,OAAQ;AAAA,UAC/C;AACA,6BAAI,OAAO,YAAY,QAAQ,MAAM;AAAA,QACvC;AAAA,MACF;AAAA,IACF,CAAC;AAGD,gCAAU,UAAU,CAAC,CAAC;AAEtB,gCAAQ,MAAM,MAAM;AAClB,YAAMC,YAAW,YAAY;AAC7B,+BAAKA,UAAS,MAAM,aAAO,oCAAoB,KAAKA,SAAQ,CAAC;AAAA,IAC/D,CAAC;AAED,UAAM,YAAY,KAAK,kBAAkB,MAAM,SAAS,CAAC;AACzD,WAAO,oCAAC,aAAW,GAAG,WAAW,KAAU;AAAA,EAC7C,CAAC;AACH;AAEA,IAAM,gBAAN,MAAoB;AAAA,EAClB,YACW,QACA,MACT;AAFS;AACA;AAAA,EACR;AAAA,EACH,cAAc,OAAmB;AAC/B,QAAI,MAAM,QAAQ,UAAU;AAC1B,yBAAI,MAAM,KAAK,MAAM;AAAA,IACvB;AAAA,EACF;AACF;AAIA,SAAS,iBAAiB,OAAY,MAAiC;AACrE,QAAM,eAAe,oBAAI,IAAgB;AACzC,cAAY,eAAe;AAG3B,MAAI,MAAM;AACR,YAAQ;AAAA,MACN,GAAG;AAAA,MACH,OAAO,KAAK,oBAAoB,MAAM,KAAK;AAAA,IAC7C;AAGF,UAAQ,IAAI,eAAe,KAAK;AAEhC,cAAY,eAAe;AAC3B,SAAO,CAAC,OAAO,YAAY;AAC7B;AAEA,SAAS,UAAa,KAAa,OAAU;AAC3C,MAAI,KAAK;AACP,QAAI,kBAAG,IAAI,GAAG;AAAG,UAAI,KAAK;AAAA;AACrB,MAAC,IAAY,UAAU;AAAA,EAC9B;AACA,SAAO;AACT;;;ADnHA,IAAM,WAAW,OAAO,IAAI,mBAAmB;AAExC,IAAM,aAAa,CACxB,YACA;AAAA,EACE,sBAAsB,MAAM;AAAA,EAC5B,sBAAsB,WAAS,IAAI,eAAe,KAAK;AAAA,EACvD,oBAAoB,WAAS;AAC/B,IAAyB,CAAC,MACvB;AACH,QAAM,aAAyB;AAAA,IAC7B;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,QAAM,WAAyB,CAAC,cAAmB;AACjD,UAAM,cAAc,eAAe,SAAS,KAAK;AAEjD,QAAI,kBAAG,IAAI,SAAS,GAAG;AACrB,kBACE,SAAS,SAAS,MACjB,SAAS,SAAS,IAAI,aAAa,WAAW,UAAU;AAAA,IAC7D,OAAO;AACL,kBACE,UAAU,QAAQ,MACjB,UAAU,QAAQ,IAAI,aAAa,WAAW,UAAU;AAAA,IAC7D;AAEA,cAAU,cAAc,YAAY;AACpC,WAAO;AAAA,EACT;AAEA,+BAAS,YAAY,CAAC,WAAW,QAAQ;AACvC,QAAI,kBAAG,IAAI,UAAU,GAAG;AACtB,YAAM,eAAe,SAAS;AAAA,IAChC;AACA,aAAS,GAAG,IAAI,SAAS,SAAS;AAAA,EACpC,CAAC;AAED,SAAO;AAAA,IACL;AAAA,EACF;AACF;AAEA,IAAM,iBAAiB,CAAC,QACtB,kBAAG,IAAI,GAAG,IACN,MACA,OAAO,kBAAG,IAAI,IAAI,WAAW,IAC3B,IAAI,cACH,kBAAG,IAAI,GAAG,KAAK,IAAI,QAAS;", "names": ["import_shared", "import_shared", "import_shared", "import_shared", "import_shared", "import_shared", "import_shared", "observer"]}