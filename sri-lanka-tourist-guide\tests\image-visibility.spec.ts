import { test, expect } from '@playwright/test'

test.describe('Image Visibility Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the home page
    await page.goto('/')
    // Wait for the page to load completely
    await page.waitForLoadState('networkidle')
  })

  test('should display destination images on destinations page', async ({ page }) => {
    // Navigate to destinations page
    await page.goto('/destinations')
    await page.waitForLoadState('networkidle')
    
    // Wait for destination cards to load
    await page.waitForSelector('[data-testid="destination-card"]', { timeout: 10000 })
    
    // Check if destination cards are visible
    const destinationCards = page.locator('[data-testid="destination-card"]')
    await expect(destinationCards).toHaveCount(3, { timeout: 15000 })
    
    // Check if images or placeholders are present in each card
    for (let i = 0; i < 3; i++) {
      const card = destinationCards.nth(i)
      
      // Check for either an actual image or a placeholder
      const hasImage = await card.locator('img').count() > 0
      const hasPlaceholder = await card.locator('[data-testid="image-placeholder"]').count() > 0
      
      expect(hasImage || hasPlaceholder).toBeTruthy()
      
      if (hasImage) {
        const img = card.locator('img').first()
        await expect(img).toBeVisible()
        
        // Check if image has loaded (not broken)
        const naturalWidth = await img.evaluate((img: HTMLImageElement) => img.naturalWidth)
        expect(naturalWidth).toBeGreaterThan(0)
      }
    }
  })

  test('should display destination detail images', async ({ page }) => {
    // Navigate to a specific destination
    await page.goto('/destinations/sigiriya')
    await page.waitForLoadState('networkidle')
    
    // Wait for hero section to load
    await page.waitForSelector('.hero-section, [data-testid="hero-section"]', { timeout: 10000 })
    
    // Check hero image
    const heroSection = page.locator('.hero-section, [data-testid="hero-section"]').first()
    const heroImage = heroSection.locator('img').first()
    const heroPlaceholder = heroSection.locator('[data-testid="image-placeholder"]').first()
    
    const hasHeroImage = await heroImage.count() > 0
    const hasHeroPlaceholder = await heroPlaceholder.count() > 0
    
    expect(hasHeroImage || hasHeroPlaceholder).toBeTruthy()
    
    if (hasHeroImage) {
      await expect(heroImage).toBeVisible()
      const naturalWidth = await heroImage.evaluate((img: HTMLImageElement) => img.naturalWidth)
      expect(naturalWidth).toBeGreaterThan(0)
    }
    
    // Scroll down to gallery section
    await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight / 2))
    await page.waitForTimeout(2000)
    
    // Check gallery images
    const galleryImages = page.locator('.gallery img, [data-testid="gallery"] img')
    const galleryPlaceholders = page.locator('.gallery [data-testid="image-placeholder"], [data-testid="gallery"] [data-testid="image-placeholder"]')
    
    const galleryImageCount = await galleryImages.count()
    const galleryPlaceholderCount = await galleryPlaceholders.count()
    
    expect(galleryImageCount + galleryPlaceholderCount).toBeGreaterThan(0)
  })

  test('should display article images or placeholders', async ({ page }) => {
    // Navigate to articles page
    await page.goto('/articles')
    await page.waitForLoadState('networkidle')
    
    // Wait for article cards to load
    await page.waitForSelector('[data-testid="article-card"], .article-card', { timeout: 10000 })
    
    const articleCards = page.locator('[data-testid="article-card"], .article-card')
    const cardCount = await articleCards.count()
    
    if (cardCount > 0) {
      // Check first few article cards
      const cardsToCheck = Math.min(cardCount, 3)
      
      for (let i = 0; i < cardsToCheck; i++) {
        const card = articleCards.nth(i)
        
        // Check for either an actual image or a placeholder
        const hasImage = await card.locator('img').count() > 0
        const hasPlaceholder = await card.locator('[data-testid="image-placeholder"]').count() > 0
        
        expect(hasImage || hasPlaceholder).toBeTruthy()
        
        if (hasImage) {
          const img = card.locator('img').first()
          await expect(img).toBeVisible()
        }
      }
    }
  })

  test('should handle image loading errors gracefully', async ({ page }) => {
    // Navigate to destinations page
    await page.goto('/destinations')
    await page.waitForLoadState('networkidle')
    
    // Wait for content to load
    await page.waitForTimeout(3000)
    
    // Check for any broken images
    const images = page.locator('img')
    const imageCount = await images.count()
    
    if (imageCount > 0) {
      for (let i = 0; i < Math.min(imageCount, 5); i++) {
        const img = images.nth(i)
        
        // Check if image is visible
        const isVisible = await img.isVisible()
        
        if (isVisible) {
          // Check if image has loaded successfully or shows placeholder
          const naturalWidth = await img.evaluate((img: HTMLImageElement) => img.naturalWidth)
          const hasErrorHandler = await img.evaluate((img: HTMLImageElement) => {
            return img.onerror !== null || img.parentElement?.querySelector('[data-testid="image-placeholder"]') !== null
          })
          
          // Image should either load successfully or have error handling
          expect(naturalWidth > 0 || hasErrorHandler).toBeTruthy()
        }
      }
    }
  })

  test('should display placeholders with proper descriptions', async ({ page }) => {
    // Navigate to destinations page
    await page.goto('/destinations')
    await page.waitForLoadState('networkidle')
    
    // Look for image placeholders
    const placeholders = page.locator('[data-testid="image-placeholder"]')
    const placeholderCount = await placeholders.count()
    
    if (placeholderCount > 0) {
      // Check first placeholder
      const firstPlaceholder = placeholders.first()
      await expect(firstPlaceholder).toBeVisible()
      
      // Check if placeholder has descriptive text
      const placeholderText = await firstPlaceholder.textContent()
      expect(placeholderText).toBeTruthy()
      expect(placeholderText?.length).toBeGreaterThan(5)
    }
  })

  test('should maintain responsive image behavior', async ({ page }) => {
    // Test on different viewport sizes
    const viewports = [
      { width: 375, height: 667 },   // Mobile
      { width: 768, height: 1024 },  // Tablet
      { width: 1920, height: 1080 }  // Desktop
    ]
    
    for (const viewport of viewports) {
      await page.setViewportSize(viewport)
      await page.goto('/destinations')
      await page.waitForLoadState('networkidle')
      
      // Wait for images to load
      await page.waitForTimeout(2000)
      
      // Check if images are properly sized for viewport
      const images = page.locator('img').first()
      if (await images.count() > 0) {
        const boundingBox = await images.boundingBox()
        if (boundingBox) {
          expect(boundingBox.width).toBeLessThanOrEqual(viewport.width)
          expect(boundingBox.width).toBeGreaterThan(0)
        }
      }
    }
  })
})
