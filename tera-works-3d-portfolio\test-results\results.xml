<testsuites id="" name="" tests="5" failures="1" skipped="0" errors="0" time="47.795296">
<testsuite name="foundation.spec.ts" timestamp="2025-06-26T10:39:30.717Z" hostname="chromium" tests="1" failures="0" skipped="0" time="4.866" errors="0">
<testcase name="Foundation Setup Tests › should load the homepage without errors" classname="foundation.spec.ts" time="4.866">
</testcase>
</testsuite>
<testsuite name="foundation.spec.ts" timestamp="2025-06-26T10:39:30.717Z" hostname="firefox" tests="1" failures="1" skipped="0" time="6.399" errors="0">
<testcase name="Foundation Setup Tests › should load the homepage without errors" classname="foundation.spec.ts" time="6.399">
<failure message="foundation.spec.ts:8:3 should load the homepage without errors" type="FAILURE">
<![CDATA[  [firefox] › foundation.spec.ts:8:3 › Foundation Setup Tests › should load the homepage without errors 

    Error: page.goto: NS_ERROR_CONNECTION_REFUSED
    Call log:
      - navigating to "http://localhost:3000/", waiting until "load"


      3 | test.describe('Foundation Setup Tests', () => {
      4 |   test.beforeEach(async ({ page }) => {
    > 5 |     await page.goto('/');
        |                ^
      6 |   });
      7 |
      8 |   test('should load the homepage without errors', async ({ page }) => {
        at E:\Augment Code Testing\tera-works-3d-portfolio\tests\foundation.spec.ts:5:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\foundation-Foundation-Setu-c391a-the-homepage-without-errors-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\foundation-Foundation-Setu-c391a-the-homepage-without-errors-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\foundation-Foundation-Setu-c391a-the-homepage-without-errors-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|foundation-Foundation-Setu-c391a-the-homepage-without-errors-firefox\test-failed-1.png]]

[[ATTACHMENT|foundation-Foundation-Setu-c391a-the-homepage-without-errors-firefox\video.webm]]

[[ATTACHMENT|foundation-Foundation-Setu-c391a-the-homepage-without-errors-firefox\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="foundation.spec.ts" timestamp="2025-06-26T10:39:30.717Z" hostname="webkit" tests="1" failures="0" skipped="0" time="4.513" errors="0">
<testcase name="Foundation Setup Tests › should load the homepage without errors" classname="foundation.spec.ts" time="4.513">
</testcase>
</testsuite>
<testsuite name="foundation.spec.ts" timestamp="2025-06-26T10:39:30.717Z" hostname="Mobile Chrome" tests="1" failures="0" skipped="0" time="4.095" errors="0">
<testcase name="Foundation Setup Tests › should load the homepage without errors" classname="foundation.spec.ts" time="4.095">
</testcase>
</testsuite>
<testsuite name="foundation.spec.ts" timestamp="2025-06-26T10:39:30.717Z" hostname="Mobile Safari" tests="1" failures="0" skipped="0" time="3.691" errors="0">
<testcase name="Foundation Setup Tests › should load the homepage without errors" classname="foundation.spec.ts" time="3.691">
</testcase>
</testsuite>
</testsuites>