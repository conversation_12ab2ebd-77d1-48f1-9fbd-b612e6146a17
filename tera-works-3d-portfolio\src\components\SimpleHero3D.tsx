import { useRef, useEffect } from 'react';
import { useFrame, useThree } from '@react-three/fiber';
import * as THREE from 'three';
import { getOptimalSettings } from '../utils/webgl';

interface SimpleHero3DProps {
  onLoaded?: () => void;
}

export const SimpleHero3D: React.FC<SimpleHero3DProps> = ({ onLoaded }) => {
  const groupRef = useRef<THREE.Group>(null);
  const elephantRef = useRef<THREE.Group>(null);
  const { camera, scene } = useThree();
  const settings = getOptimalSettings();

  useEffect(() => {
    // Set up camera position
    camera.position.set(0, 2, 8);
    camera.lookAt(0, 0, 0);

    // Configure scene
    scene.fog = new THREE.Fog('#1A1B3A', 8, 20);

    // Mark as loaded
    setTimeout(() => {
      onLoaded?.();
    }, 500);
  }, [camera, scene, onLoaded]);

  // Simple animation
  useFrame((state) => {
    if (!groupRef.current) return;

    const time = state.clock.elapsedTime;
    
    // Gentle scene rotation
    groupRef.current.rotation.y = Math.sin(time * 0.1) * 0.05;
    
    // Elephant floating
    if (elephantRef.current) {
      elephantRef.current.position.y = Math.sin(time * 0.5) * 0.1;
      elephantRef.current.rotation.y = Math.sin(time * 0.3) * 0.05;
    }
  });

  return (
    <group ref={groupRef}>
      {/* Lighting Setup */}
      <ambientLight intensity={0.4} color="#4A9FFF" />
      
      <directionalLight
        position={[5, 5, 5]}
        intensity={0.8}
        color="#FFFFFF"
        castShadow={settings.shadowMapSize > 512}
      />
      
      <directionalLight
        position={[-3, 2, -5]}
        intensity={0.4}
        color="#7FC7FF"
      />

      {/* Simple Elephant Model */}
      <group ref={elephantRef} position={[0, 0, 0]} scale={1.5}>
        {/* Main Body */}
        <mesh position={[0, -0.5, 0]} castShadow receiveShadow>
          <sphereGeometry args={[1, 32, 32]} />
          <meshStandardMaterial
            color="#6BB6FF"
            metalness={0.3}
            roughness={0.4}
            emissive="#2B7FE6"
            emissiveIntensity={0.1}
          />
        </mesh>

        {/* Head */}
        <mesh position={[0, 0.3, 0.6]} castShadow receiveShadow>
          <sphereGeometry args={[0.6, 32, 32]} />
          <meshStandardMaterial
            color="#4A9FFF"
            metalness={0.3}
            roughness={0.4}
            emissive="#1A5CC7"
            emissiveIntensity={0.1}
          />
        </mesh>

        {/* Trunk */}
        <mesh position={[0, 0, 1.1]} castShadow receiveShadow>
          <cylinderGeometry args={[0.15, 0.25, 1, 16]} />
          <meshStandardMaterial
            color="#4A9FFF"
            metalness={0.3}
            roughness={0.4}
          />
        </mesh>

        {/* Left Ear */}
        <mesh position={[-0.5, 0.4, 0.3]} rotation={[0, 0, -0.3]} castShadow receiveShadow>
          <sphereGeometry args={[0.4, 16, 16]} />
          <meshStandardMaterial
            color="#7FC7FF"
            metalness={0.2}
            roughness={0.5}
          />
        </mesh>

        {/* Right Ear */}
        <mesh position={[0.5, 0.4, 0.3]} rotation={[0, 0, 0.3]} castShadow receiveShadow>
          <sphereGeometry args={[0.4, 16, 16]} />
          <meshStandardMaterial
            color="#7FC7FF"
            metalness={0.2}
            roughness={0.5}
          />
        </mesh>

        {/* Left Tusk */}
        <mesh position={[-0.2, 0.1, 1.0]} rotation={[0.3, -0.2, 0]} castShadow receiveShadow>
          <cylinderGeometry args={[0.03, 0.05, 0.6, 8]} />
          <meshStandardMaterial
            color="#C8CDD4"
            metalness={0.8}
            roughness={0.1}
            emissive="#E8EDF4"
            emissiveIntensity={0.05}
          />
        </mesh>

        {/* Right Tusk */}
        <mesh position={[0.2, 0.1, 1.0]} rotation={[0.3, 0.2, 0]} castShadow receiveShadow>
          <cylinderGeometry args={[0.03, 0.05, 0.6, 8]} />
          <meshStandardMaterial
            color="#C8CDD4"
            metalness={0.8}
            roughness={0.1}
            emissive="#E8EDF4"
            emissiveIntensity={0.05}
          />
        </mesh>

        {/* Eyes */}
        <mesh position={[-0.25, 0.45, 0.85]} castShadow>
          <sphereGeometry args={[0.08, 16, 16]} />
          <meshStandardMaterial color="#1A1B3A" emissive="#4A9FFF" emissiveIntensity={0.2} />
        </mesh>
        
        <mesh position={[0.25, 0.45, 0.85]} castShadow>
          <sphereGeometry args={[0.08, 16, 16]} />
          <meshStandardMaterial color="#1A1B3A" emissive="#4A9FFF" emissiveIntensity={0.2} />
        </mesh>

        {/* Legs */}
        {[
          [-0.6, -1.2, 0.3],
          [0.6, -1.2, 0.3],
          [-0.6, -1.2, -0.3],
          [0.6, -1.2, -0.3]
        ].map((pos, index) => (
          <mesh key={index} position={pos as [number, number, number]} castShadow receiveShadow>
            <cylinderGeometry args={[0.2, 0.25, 0.8, 16]} />
            <meshStandardMaterial
              color="#4A9FFF"
              metalness={0.3}
              roughness={0.4}
            />
          </mesh>
        ))}
      </group>

      {/* Simple Floating Particles */}
      <SimpleParticles />
    </group>
  );
};

// Simple particles without complex buffer attributes
const SimpleParticles: React.FC = () => {
  const particlesRef = useRef<THREE.Group>(null);

  useFrame((state) => {
    if (!particlesRef.current) return;

    const time = state.clock.elapsedTime;
    
    particlesRef.current.children.forEach((particle, index) => {
      particle.position.y += Math.sin(time + index) * 0.001;
      
      if (particle.position.y > 7) particle.position.y = -7;
      if (particle.position.y < -7) particle.position.y = 7;
    });
  });

  return (
    <group ref={particlesRef}>
      {Array.from({ length: 20 }, (_, i) => (
        <mesh
          key={i}
          position={[
            (Math.random() - 0.5) * 20,
            (Math.random() - 0.5) * 15,
            (Math.random() - 0.5) * 20
          ]}
        >
          <sphereGeometry args={[0.05, 8, 8]} />
          <meshBasicMaterial
            color={new THREE.Color().setHSL(0.6 + Math.random() * 0.1, 0.8, 0.6)}
            transparent
            opacity={0.6}
          />
        </mesh>
      ))}
    </group>
  );
};
