{"name": "tera-works-3d-portfolio", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "playwright test", "test:headed": "playwright test --headed", "test:ui": "playwright test --ui", "test:debug": "playwright test --debug", "test:report": "playwright show-report", "test:foundation": "playwright test foundation.spec.ts", "test:webgl": "playwright test webgl-3d.spec.ts", "test:responsive": "playwright test responsive.spec.ts", "test:performance": "playwright test performance.spec.ts", "test:accessibility": "playwright test accessibility.spec.ts"}, "dependencies": {"@react-spring/core": "^10.0.1", "@react-spring/three": "^10.0.1", "@react-three/drei": "^9.122.0", "@react-three/fiber": "^9.1.2", "@types/three": "^0.177.0", "clsx": "^2.1.1", "gsap": "^3.13.0", "lucide-react": "^0.294.0", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.17", "three": "^0.177.0"}, "devDependencies": {"@eslint/js": "^9.29.0", "@playwright/test": "^1.53.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0"}}