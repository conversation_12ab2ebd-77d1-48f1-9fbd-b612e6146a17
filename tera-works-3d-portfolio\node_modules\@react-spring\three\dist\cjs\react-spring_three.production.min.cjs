"use strict";var v=Object.create;var s=Object.defineProperty;var x=Object.getOwnPropertyDescriptor;var y=Object.getOwnPropertyNames;var A=Object.getPrototypeOf,E=Object.prototype.hasOwnProperty;var h=(t,o)=>{for(var e in o)s(t,e,{get:o[e],enumerable:!0})},p=(t,o,e,f)=>{if(o&&typeof o=="object"||typeof o=="function")for(let a of y(o))!E.call(t,a)&&a!==e&&s(t,a,{get:()=>o[a],enumerable:!(f=x(o,a))||f.enumerable});return t},i=(t,o,e)=>(p(t,o,"default"),e&&p(e,o,"default")),P=(t,o,e)=>(e=t!=null?v(A(t)):{},p(o||!t||!t.__esModule?s(e,"default",{value:t,enumerable:!0}):e,t)),S=t=>p(s({},"__esModule",{value:!0}),t);var r={};h(r,{a:()=>H,animated:()=>H});module.exports=S(r);var n=require("@react-three/fiber"),d=require("@react-spring/core"),m=require("@react-spring/shared"),l=require("@react-spring/animated");var b=P(require("three")),J=require("@react-three/fiber"),c=["primitive"].concat(Object.keys(b).filter(t=>/^[A-Z]/.test(t)).map(t=>t[0].toLowerCase()+t.slice(1)));i(r,require("@react-spring/core"),module.exports);d.Globals.assign({createStringInterpolator:m.createStringInterpolator,colors:m.colors,frameLoop:"demand"});(0,n.addEffect)(()=>{m.raf.advance()});var g=(0,l.createHost)(c,{applyAnimatedValues:n.applyProps}),H=g.animated;0&&(module.exports={a,animated,...require("@react-spring/core")});
