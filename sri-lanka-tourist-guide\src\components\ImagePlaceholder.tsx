import React from 'react'
import { motion } from 'framer-motion'

interface ImagePlaceholderProps {
  description: string
  category?: string
  width?: string
  height?: string
  className?: string
  showIcon?: boolean
}

const ImagePlaceholder: React.FC<ImagePlaceholderProps> = ({
  description,
  category = 'general',
  width = '100%',
  height = '200px',
  className = '',
  showIcon = true
}) => {
  const getCategoryIcon = (cat: string) => {
    switch (cat.toLowerCase()) {
      case 'destination':
      case 'destinations':
        return '🏛️'
      case 'beach':
      case 'beaches':
        return '🏖️'
      case 'temple':
      case 'temples':
        return '🛕'
      case 'wildlife':
        return '🐘'
      case 'landscape':
      case 'landscapes':
        return '🏔️'
      case 'transportation':
        return '🚂'
      case 'culture':
        return '🎭'
      case 'food':
        return '🍛'
      case 'activity':
      case 'activities':
        return '🏃‍♂️'
      default:
        return '📸'
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
      className={`
        relative overflow-hidden rounded-xl border-2 border-dashed border-gray-300 
        bg-gradient-to-br from-gray-50 to-gray-100 
        flex flex-col items-center justify-center
        hover:border-primary-coral hover:bg-gradient-to-br hover:from-primary-coral/5 hover:to-primary-orange/5
        transition-all duration-300
        ${className}
      `}
      style={{ width, height }}
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-gradient-to-br from-primary-teal/20 to-primary-coral/20" />
        <div className="absolute top-4 left-4 w-8 h-8 border border-gray-300 rounded-full" />
        <div className="absolute top-8 right-8 w-6 h-6 border border-gray-300 rounded-full" />
        <div className="absolute bottom-6 left-8 w-4 h-4 border border-gray-300 rounded-full" />
        <div className="absolute bottom-4 right-4 w-10 h-10 border border-gray-300 rounded-full" />
      </div>

      {/* Content */}
      <div className="relative z-10 text-center p-6 max-w-xs">
        {showIcon && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
            className="text-4xl mb-4 filter drop-shadow-sm"
          >
            {getCategoryIcon(category)}
          </motion.div>
        )}
        
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="space-y-2"
        >
          <div className="text-xs font-semibold text-primary-coral uppercase tracking-wider">
            Image Needed
          </div>
          <div className="text-sm font-medium text-gray-700 leading-relaxed">
            {description}
          </div>
          <div className="text-xs text-gray-500 italic">
            Category: {category}
          </div>
        </motion.div>
      </div>

      {/* Corner Accent */}
      <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-bl from-primary-coral/10 to-transparent" />
      <div className="absolute bottom-0 left-0 w-12 h-12 bg-gradient-to-tr from-primary-teal/10 to-transparent" />
    </motion.div>
  )
}

export default ImagePlaceholder

// Helper component for article image placeholders
export const ArticleImagePlaceholder: React.FC<{
  description: string
  position?: 'left' | 'right' | 'center'
  size?: 'small' | 'medium' | 'large'
}> = ({ description, position = 'center', size = 'medium' }) => {
  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return 'w-full max-w-sm h-48'
      case 'large':
        return 'w-full max-w-4xl h-80'
      default:
        return 'w-full max-w-2xl h-64'
    }
  }

  const getPositionClasses = () => {
    switch (position) {
      case 'left':
        return 'mr-auto'
      case 'right':
        return 'ml-auto'
      default:
        return 'mx-auto'
    }
  }

  return (
    <div className={`my-8 ${getPositionClasses()}`}>
      <ImagePlaceholder
        description={description}
        category="article"
        className={getSizeClasses()}
        showIcon={false}
      />
      <p className="text-xs text-gray-500 text-center mt-2 italic">
        Placeholder for: {description}
      </p>
    </div>
  )
}

// Helper component for destination card image placeholders
export const DestinationImagePlaceholder: React.FC<{
  destinationName: string
  imageType?: string
}> = ({ destinationName, imageType = 'main view' }) => {
  return (
    <ImagePlaceholder
      description={`${destinationName} - ${imageType}`}
      category="destinations"
      height="240px"
      className="w-full"
    />
  )
}

// Helper component for activity image placeholders
export const ActivityImagePlaceholder: React.FC<{
  activityName: string
  location?: string
}> = ({ activityName, location }) => {
  const description = location 
    ? `${activityName} in ${location}`
    : activityName

  return (
    <ImagePlaceholder
      description={description}
      category="activities"
      height="200px"
      className="w-full"
    />
  )
}
