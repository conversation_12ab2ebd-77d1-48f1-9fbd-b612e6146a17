{"config": {"configFile": "E:\\Augment Code Testing\\tera-works-3d-portfolio\\playwright.config.ts", "rootDir": "E:/Augment Code Testing/tera-works-3d-portfolio/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "E:/Augment Code Testing/tera-works-3d-portfolio/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "chromium", "name": "chromium", "testDir": "E:/Augment Code Testing/tera-works-3d-portfolio/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Augment Code Testing/tera-works-3d-portfolio/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "firefox", "name": "firefox", "testDir": "E:/Augment Code Testing/tera-works-3d-portfolio/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Augment Code Testing/tera-works-3d-portfolio/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "webkit", "name": "webkit", "testDir": "E:/Augment Code Testing/tera-works-3d-portfolio/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Augment Code Testing/tera-works-3d-portfolio/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "E:/Augment Code Testing/tera-works-3d-portfolio/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Augment Code Testing/tera-works-3d-portfolio/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "E:/Augment Code Testing/tera-works-3d-portfolio/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.1", "workers": 1, "webServer": {"command": "npm run dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "foundation.spec.ts", "file": "foundation.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Foundation Setup Tests", "file": "foundation.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should load the homepage without errors", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 4866, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-26T10:39:32.992Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "6b60d0cacd30be8ae473-f5aeea40d02837a56451", "file": "foundation.spec.ts", "line": 8, "column": 3}, {"title": "should load the homepage without errors", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 1, "parallelIndex": 0, "status": "failed", "duration": 6399, "error": {"message": "Error: page.goto: NS_ERROR_CONNECTION_REFUSED\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: NS_ERROR_CONNECTION_REFUSED\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"load\"\u001b[22m\n\n    at E:\\Augment Code Testing\\tera-works-3d-portfolio\\tests\\foundation.spec.ts:5:16", "location": {"file": "E:\\Augment Code Testing\\tera-works-3d-portfolio\\tests\\foundation.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'Foundation Setup Tests'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 7 |\u001b[39m\n \u001b[90m 8 |\u001b[39m   test(\u001b[32m'should load the homepage without errors'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\tera-works-3d-portfolio\\tests\\foundation.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: NS_ERROR_CONNECTION_REFUSED\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'Foundation Setup Tests'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 7 |\u001b[39m\n \u001b[90m 8 |\u001b[39m   test(\u001b[32m'should load the homepage without errors'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at E:\\Augment Code Testing\\tera-works-3d-portfolio\\tests\\foundation.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-26T10:39:42.234Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\tera-works-3d-portfolio\\test-results\\foundation-Foundation-Setu-c391a-the-homepage-without-errors-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\tera-works-3d-portfolio\\test-results\\foundation-Foundation-Setu-c391a-the-homepage-without-errors-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\tera-works-3d-portfolio\\test-results\\foundation-Foundation-Setu-c391a-the-homepage-without-errors-firefox\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\tera-works-3d-portfolio\\tests\\foundation.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "6b60d0cacd30be8ae473-9589a1746ee646d0b422", "file": "foundation.spec.ts", "line": 8, "column": 3}, {"title": "should load the homepage without errors", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 2, "parallelIndex": 0, "status": "passed", "duration": 4513, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-26T10:39:55.734Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "6b60d0cacd30be8ae473-f8adf76562e949613e49", "file": "foundation.spec.ts", "line": 8, "column": 3}, {"title": "should load the homepage without errors", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 3, "parallelIndex": 0, "status": "passed", "duration": 4095, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-26T10:40:06.099Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "6b60d0cacd30be8ae473-e2ad1664bdb352e42cbf", "file": "foundation.spec.ts", "line": 8, "column": 3}, {"title": "should load the homepage without errors", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 4, "parallelIndex": 0, "status": "passed", "duration": 3691, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-26T10:40:14.219Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "6b60d0cacd30be8ae473-3854d596de8b0b1685a9", "file": "foundation.spec.ts", "line": 8, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-06-26T10:39:30.450Z", "duration": 47795.296, "expected": 4, "skipped": 0, "unexpected": 1, "flaky": 0}}