import { useRef, useEffect, useState } from 'react';
import { useFrame, useThree } from '@react-three/fiber';
import { OrbitControls, Environment, ContactShadows } from '@react-three/drei';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { ElephantModel } from './ElephantModel';
import { getOptimalSettings } from '../utils/webgl';
import * as THREE from 'three';

// Register ScrollTrigger
gsap.registerPlugin(ScrollTrigger);

interface Hero3DProps {
  onLoaded?: () => void;
}

export const Hero3D: React.FC<Hero3DProps> = ({ onLoaded }) => {
  const groupRef = useRef<THREE.Group>(null);
  const elephantRef = useRef<THREE.Group>(null);
  const textRef = useRef<THREE.Group>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const { camera, scene } = useThree();
  const settings = getOptimalSettings();

  useEffect(() => {
    // Set up camera position
    camera.position.set(0, 2, 8);
    camera.lookAt(0, 0, 0);

    // Configure scene
    scene.fog = new THREE.Fog('#1A1B3A', 8, 20);

    // Mark as loaded after a short delay
    const timer = setTimeout(() => {
      setIsLoaded(true);
      onLoaded?.();
    }, 1000);

    return () => clearTimeout(timer);
  }, [camera, scene, onLoaded]);

  // Scroll-based animation
  useFrame((state) => {
    if (!groupRef.current || !isLoaded) return;

    const time = state.clock.elapsedTime;
    
    // Gentle scene rotation
    groupRef.current.rotation.y = Math.sin(time * 0.1) * 0.05;
    
    // Camera breathing effect
    camera.position.z = 8 + Math.sin(time * 0.5) * 0.2;
  });

  return (
    <group ref={groupRef}>
      {/* Lighting Setup */}
      <ambientLight intensity={0.4} color="#4A9FFF" />
      
      <directionalLight
        position={[5, 5, 5]}
        intensity={0.8}
        color="#FFFFFF"
        castShadow={settings.shadowMapSize > 512}
        shadow-mapSize-width={settings.shadowMapSize}
        shadow-mapSize-height={settings.shadowMapSize}
        shadow-camera-far={50}
        shadow-camera-left={-10}
        shadow-camera-right={10}
        shadow-camera-top={10}
        shadow-camera-bottom={-10}
      />
      
      <directionalLight
        position={[-3, 2, -5]}
        intensity={0.4}
        color="#7FC7FF"
      />

      {/* Environment */}
      <Environment preset="night" />

      {/* Main Elephant Model */}
      <ElephantModel
        ref={elephantRef}
        position={[0, 0, 0]}
        scale={1.5}
        animate={isLoaded}
      />

      {/* 3D Text - Company Name (simplified for now) */}
      <group ref={textRef} position={[0, 3, 0]}>
        <mesh>
          <boxGeometry args={[4, 0.5, 0.2]} />
          <meshStandardMaterial
            color="#FFFFFF"
            metalness={0.1}
            roughness={0.3}
            emissive="#4A9FFF"
            emissiveIntensity={0.1}
            transparent
            opacity={0.8}
          />
        </mesh>
      </group>

      {/* Floating Particles */}
      <FloatingParticles />

      {/* Ground Shadows */}
      {settings.shadowMapSize > 512 && (
        <ContactShadows
          position={[0, -2, 0]}
          opacity={0.4}
          scale={15}
          blur={2}
          far={4}
          color="#1A1B3A"
        />
      )}

      {/* Camera Controls */}
      <OrbitControls
        enablePan={false}
        enableZoom={false}
        enableRotate={true}
        autoRotate={false}
        minPolarAngle={Math.PI / 3}
        maxPolarAngle={Math.PI - Math.PI / 3}
        minAzimuthAngle={-Math.PI / 4}
        maxAzimuthAngle={Math.PI / 4}
        dampingFactor={0.05}
        enableDamping
      />
    </group>
  );
};

// Floating particles component
const FloatingParticles: React.FC = () => {
  const particlesRef = useRef<THREE.Points>(null);
  const particleCount = 50;

  const positions = new Float32Array(particleCount * 3);
  const colors = new Float32Array(particleCount * 3);

  // Initialize particle positions and colors
  for (let i = 0; i < particleCount; i++) {
    const i3 = i * 3;
    
    // Random positions in a sphere around the scene
    positions[i3] = (Math.random() - 0.5) * 20;
    positions[i3 + 1] = (Math.random() - 0.5) * 15;
    positions[i3 + 2] = (Math.random() - 0.5) * 20;
    
    // Brand colors
    const color = new THREE.Color();
    color.setHSL(0.6 + Math.random() * 0.1, 0.8, 0.6 + Math.random() * 0.2);
    colors[i3] = color.r;
    colors[i3 + 1] = color.g;
    colors[i3 + 2] = color.b;
  }

  useFrame((state) => {
    if (!particlesRef.current) return;

    const time = state.clock.elapsedTime;
    const positions = particlesRef.current.geometry.attributes.position.array as Float32Array;

    for (let i = 0; i < particleCount; i++) {
      const i3 = i * 3;
      
      // Gentle floating motion
      positions[i3 + 1] += Math.sin(time + i) * 0.001;
      
      // Wrap around
      if (positions[i3 + 1] > 7) positions[i3 + 1] = -7;
      if (positions[i3 + 1] < -7) positions[i3 + 1] = 7;
    }

    particlesRef.current.geometry.attributes.position.needsUpdate = true;
  });

  return (
    <points ref={particlesRef}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={particleCount}
          array={positions}
          itemSize={3}
          args={[positions, 3]}
        />
        <bufferAttribute
          attach="attributes-color"
          count={particleCount}
          array={colors}
          itemSize={3}
          args={[colors, 3]}
        />
      </bufferGeometry>
      <pointsMaterial
        size={0.05}
        vertexColors
        transparent
        opacity={0.6}
        sizeAttenuation
      />
    </points>
  );
};
