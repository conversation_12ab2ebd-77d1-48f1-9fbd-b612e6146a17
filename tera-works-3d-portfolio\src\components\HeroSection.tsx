import React, { useEffect, useRef } from 'react';
import { ChevronDown, MessageCircle } from 'lucide-react';
import { gsap } from 'gsap';

export const HeroSection: React.FC = () => {
  const heroRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const ctaRef = useRef<HTMLDivElement>(null);
  const scrollIndicatorRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // GSAP entrance animation
    const tl = gsap.timeline({ delay: 0.5 });
    
    // Animate title
    tl.fromTo(titleRef.current, 
      { 
        opacity: 0, 
        y: 50,
        scale: 0.9
      },
      { 
        opacity: 1, 
        y: 0,
        scale: 1,
        duration: 1.2,
        ease: "power3.out"
      }
    );

    // Animate subtitle
    tl.fromTo(subtitleRef.current,
      {
        opacity: 0,
        y: 30
      },
      {
        opacity: 1,
        y: 0,
        duration: 0.8,
        ease: "power2.out"
      },
      "-=0.6"
    );

    // Animate CTA buttons
    tl.fromTo(ctaRef.current,
      {
        opacity: 0,
        y: 20
      },
      {
        opacity: 1,
        y: 0,
        duration: 0.6,
        ease: "power2.out"
      },
      "-=0.4"
    );

    // Animate scroll indicator
    tl.fromTo(scrollIndicatorRef.current,
      {
        opacity: 0,
        y: 20
      },
      {
        opacity: 1,
        y: 0,
        duration: 0.5,
        ease: "power2.out"
      },
      "-=0.2"
    );

    // Continuous scroll indicator animation
    gsap.to(scrollIndicatorRef.current, {
      y: 10,
      duration: 1.5,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1
    });

    return () => {
      tl.kill();
    };
  }, []);

  const scrollToNext = () => {
    const nextSection = document.querySelector('section');
    if (nextSection) {
      nextSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div
      ref={heroRef}
      className="absolute inset-0 z-20 flex items-center justify-center h-full pointer-events-none"
    >
      <div className="text-center max-w-4xl mx-auto container-padding pointer-events-auto">
        {/* Main Title */}
        <h1 
          ref={titleRef}
          className="text-hero font-bold text-semantic-text mb-6 leading-tight"
        >
          <span className="text-gradient">Tera Works</span>
          <br />
          <span className="text-4xl md:text-5xl lg:text-6xl">
            Digital Ecosystem
          </span>
        </h1>

        {/* Subtitle */}
        <p 
          ref={subtitleRef}
          className="text-xl md:text-2xl text-semantic-textSecondary mb-8 max-w-2xl mx-auto leading-relaxed"
        >
          Transform your business with custom websites, e-commerce platforms, 
          and strategic digital marketing. 
          <span className="text-brand-accent font-medium block mt-2">
            Let's Grow Together
          </span>
        </p>

        {/* CTA Buttons */}
        <div 
          ref={ctaRef}
          className="flex flex-col sm:flex-row gap-4 justify-center mb-12"
        >
          <a
            href="https://wa.me/***********?text=Hi! I'm interested in growing my business with Tera Works. Let's discuss my project."
            target="_blank"
            rel="noopener noreferrer"
            className="btn-primary inline-flex items-center justify-center gap-3 text-lg px-8 py-4 glow-effect"
          >
            <MessageCircle className="w-5 h-5" />
            Start Growing
          </a>
          
          <button
            onClick={scrollToNext}
            className="btn-secondary text-lg px-8 py-4"
          >
            View Our Work
          </button>
        </div>

        {/* Trust Indicators */}
        <div className="flex flex-wrap justify-center items-center gap-6 text-sm text-semantic-textMuted mb-8">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-semantic-success rounded-full"></div>
            <span>50+ Happy Clients</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-semantic-success rounded-full"></div>
            <span>Available 9 AM - 8 PM</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-semantic-success rounded-full"></div>
            <span>Usually responds within 1 hour</span>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div
        ref={scrollIndicatorRef}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 cursor-pointer pointer-events-auto"
        onClick={scrollToNext}
      >
        <div className="flex flex-col items-center text-semantic-textMuted hover:text-brand-primary transition-colors">
          <span className="text-sm mb-2">Scroll to explore</span>
          <ChevronDown className="w-6 h-6" />
        </div>
      </div>
    </div>
  );
};
