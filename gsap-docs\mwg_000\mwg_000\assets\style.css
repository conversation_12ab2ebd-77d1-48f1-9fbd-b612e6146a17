@import url('https://fonts.googleapis.com/css2?family=Inter:wght@500;800&display=swap');

/* NORMALIZE */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    appearance: none;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
/* NORMALIZE */

body {
    background: #121212;
    color: #F1F1F1;
    font: 500 normal 22px/1.3 'Inter', sans-serif;
}
.mwg_effect000 {
    height: 100vh;
    overflow: hidden;
    position: relative;
    display: grid;
    place-items: center;
}

.mwg_effect000 .header {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    align-items: center;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    border-bottom: 1px solid #323232;
    padding: 20px 25px;
    color: #BAB8B9;
}
.mwg_effect000 .header div:nth-child(2) {
    font-size: 26px;
}
.mwg_effect000 .header div:last-child {
    display: flex;
    justify-content: flex-end;
}
.mwg_effect000 .button {
    font-size: 14px;
    text-transform: uppercase;
    
    border-radius: 24px;
    height: 48px;
    gap: 5px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    width: max-content; 
}
.mwg_effect000 .button1 {
    background-color: #232323;
}
.mwg_effect000 .button2 {
    border: 1px solid #323232;
}


.mwg_effect000 .button img {
    width: 22px;
    height: auto;
    display: block;
}

.mwg_effect000 .medias {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1vw;
}
.mwg_effect000 .medias img {
    width: 11vw;
    height: 11vw;
    object-fit: contain;
    border-radius: 4%;
    display: block;
    pointer-events: none;
    will-change: transform;
}

@media (max-width: 768px) {
    .mwg_effect000 .header {
        padding: 15px;
        display: flex;
        justify-content: space-between;
    }
    .mwg_effect000 .header div:nth-child(2) {
        display: none;
    }
    .mwg_effect000 .medias {
        gap: 2vw;
    }
    .mwg_effect000 .medias img {
        width: 18vw;
        height: 18vw;
    }
}