{"version": 3, "sources": ["../../src/index.ts", "../../src/hooks/useChain.ts", "../../src/helpers.ts", "../../src/hooks/useSpring.ts", "../../src/hooks/useSprings.ts", "../../src/SpringValue.ts", "../../src/AnimationConfig.ts", "../../src/constants.ts", "../../src/Animation.ts", "../../src/scheduleProps.ts", "../../src/runAsync.ts", "../../src/AnimationResult.ts", "../../src/FrameValue.ts", "../../src/SpringPhase.ts", "../../src/Controller.ts", "../../src/SpringContext.tsx", "../../src/SpringRef.ts", "../../src/hooks/useSpringRef.ts", "../../src/hooks/useSpringValue.ts", "../../src/hooks/useTrail.ts", "../../src/hooks/useTransition.tsx", "../../src/hooks/useScroll.ts", "../../src/hooks/useResize.ts", "../../src/hooks/useInView.ts", "../../src/components/Spring.tsx", "../../src/components/Trail.tsx", "../../src/components/Transition.tsx", "../../src/interpolate.ts", "../../src/Interpolation.ts", "../../src/globals.ts"], "sourcesContent": ["export * from './hooks'\nexport * from './components'\nexport * from './interpolate'\nexport * from './constants'\nexport * from './globals'\n\nexport { Controller } from './Controller'\nexport { SpringValue } from './SpringValue'\nexport { SpringContext } from './SpringContext'\nexport { SpringRef } from './SpringRef'\n\nexport { FrameValue } from './FrameValue'\nexport { Interpolation } from './Interpolation'\nexport { BailSignal } from './runAsync'\nexport {\n  createInterpolator,\n  useIsomorphicLayoutEffect,\n  useReducedMotion,\n  easings,\n} from '@react-spring/shared'\nexport { inferTo } from './helpers'\n\nexport * from './types'\nexport * from '@react-spring/types'\n", "import { each, useIsomorphicLayoutEffect } from '@react-spring/shared'\nimport { SpringRef } from '../SpringRef'\nimport { callProp } from '../helpers'\n\n/**\n * Used to orchestrate animation hooks in sequence with one another.\n * This is best used when you specifically want to orchestrate different\n * types of animation hook e.g. `useSpring` & `useTransition` in\n * sequence as opposed to multiple `useSpring` hooks.\n *\n *\n * ```jsx\n * export const MyComponent = () => {\n *  //...\n *  useChain([springRef, transitionRef])\n *  //...\n * }\n * ```\n *\n * @param refs – An array of `SpringRef`s.\n * @param timeSteps – Optional array of numbers that define the\n * delay between each animation from 0-1. The length should correlate\n * to the length of `refs`.\n * @param timeFrame – Optional number that defines the total duration\n *\n * @public\n */\nexport function useChain(\n  refs: ReadonlyArray<SpringRef>,\n  timeSteps?: number[],\n  timeFrame = 1000\n) {\n  useIsomorphicLayoutEffect(() => {\n    if (timeSteps) {\n      let prevDelay = 0\n      each(refs, (ref, i) => {\n        const controllers = ref.current\n        if (controllers.length) {\n          let delay = timeFrame * timeSteps[i]\n\n          // Use the previous delay if none exists.\n          if (isNaN(delay)) delay = prevDelay\n          else prevDelay = delay\n\n          each(controllers, ctrl => {\n            each(ctrl.queue, props => {\n              // memoizing stops recursion https://github.com/pmndrs/react-spring/issues/1367\n              const memoizedDelayProp = props.delay\n              props.delay = key => delay + callProp(memoizedDelayProp || 0, key)\n            })\n          })\n\n          ref.start()\n        }\n      })\n    } else {\n      let p: Promise<any> = Promise.resolve()\n      each(refs, ref => {\n        const controllers = ref.current\n        if (controllers.length) {\n          // Take the queue of each controller\n          const queues = controllers.map(ctrl => {\n            const q = ctrl.queue\n            ctrl.queue = []\n            return q\n          })\n\n          // Apply the queue when the previous ref stops animating\n          p = p.then(() => {\n            each(controllers, (ctrl, i) =>\n              each(queues[i] || [], update => ctrl.queue.push(update))\n            )\n            return Promise.all(ref.start())\n          })\n        }\n      })\n    }\n  })\n}\n", "import {\n  is,\n  toArray,\n  eachProp,\n  getFluidValue,\n  isAnimatedString,\n  FluidValue,\n  Globals as G,\n} from '@react-spring/shared'\nimport { AnyFn, OneOrMore, Lookup } from '@react-spring/types'\nimport { ReservedProps, ForwardProps, InferTo } from './types'\nimport type { Controller } from './Controller'\nimport type { SpringRef } from './SpringRef'\n\nexport function callProp<T>(\n  value: T,\n  ...args: T extends AnyFn ? Parameters<T> : unknown[]\n): T extends AnyFn<any, infer U> ? U : T {\n  return is.fun(value) ? value(...args) : value\n}\n\n/** Try to coerce the given value into a boolean using the given key */\nexport const matchProp = (\n  value: boolean | OneOrMore<string> | ((key: any) => boolean) | undefined,\n  key: string | undefined\n) =>\n  value === true ||\n  !!(\n    key &&\n    value &&\n    (is.fun(value) ? value(key) : toArray(value).includes(key))\n  )\n\nexport const resolveProp = <T>(\n  prop: T | Lookup<T> | undefined,\n  key: string | undefined\n) => (is.obj(prop) ? key && (prop as any)[key] : prop)\n\nexport const concatFn = <T extends AnyFn>(first: T | undefined, last: T) =>\n  first ? (...args: Parameters<T>) => (first(...args), last(...args)) : last\n\n/** Returns `true` if the given prop is having its default value set. */\nexport const hasDefaultProp = <T extends Lookup>(props: T, key: keyof T) =>\n  !is.und(getDefaultProp(props, key))\n\n/** Get the default value being set for the given `key` */\nexport const getDefaultProp = <T extends Lookup, P extends keyof T>(\n  props: T,\n  key: P\n): T[P] =>\n  props.default === true\n    ? props[key]\n    : props.default\n      ? props.default[key]\n      : undefined\n\nconst noopTransform = (value: any) => value\n\n/**\n * Extract the default props from an update.\n *\n * When the `default` prop is falsy, this function still behaves as if\n * `default: true` was used. The `default` prop is always respected when\n * truthy.\n */\nexport const getDefaultProps = <T extends Lookup>(\n  props: Lookup,\n  transform: (value: any, key: string) => any = noopTransform\n): T => {\n  let keys: readonly string[] = DEFAULT_PROPS\n  if (props.default && props.default !== true) {\n    props = props.default\n    keys = Object.keys(props)\n  }\n  const defaults: any = {}\n  for (const key of keys) {\n    const value = transform(props[key], key)\n    if (!is.und(value)) {\n      defaults[key] = value\n    }\n  }\n  return defaults\n}\n\n/**\n * These props are implicitly used as defaults when defined in a\n * declarative update (eg: render-based) or any update with `default: true`.\n *\n * Use `default: {}` or `default: false` to opt-out of these implicit defaults\n * for any given update.\n *\n * Note: These are not the only props with default values. For example, the\n * `pause`, `cancel`, and `immediate` props. But those must be updated with\n * the object syntax (eg: `default: { immediate: true }`).\n */\nexport const DEFAULT_PROPS = [\n  'config',\n  'onProps',\n  'onStart',\n  'onChange',\n  'onPause',\n  'onResume',\n  'onRest',\n] as const\n\nconst RESERVED_PROPS: {\n  [key: string]: 1 | undefined\n} = {\n  config: 1,\n  from: 1,\n  to: 1,\n  ref: 1,\n  loop: 1,\n  reset: 1,\n  pause: 1,\n  cancel: 1,\n  reverse: 1,\n  immediate: 1,\n  default: 1,\n  delay: 1,\n  onProps: 1,\n  onStart: 1,\n  onChange: 1,\n  onPause: 1,\n  onResume: 1,\n  onRest: 1,\n  onResolve: 1,\n\n  // Transition props\n  items: 1,\n  trail: 1,\n  sort: 1,\n  expires: 1,\n  initial: 1,\n  enter: 1,\n  update: 1,\n  leave: 1,\n  children: 1,\n  onDestroyed: 1,\n\n  // Internal props\n  keys: 1,\n  callId: 1,\n  parentId: 1,\n}\n\n/**\n * Extract any properties whose keys are *not* reserved for customizing your\n * animations. All hooks use this function, which means `useTransition` props\n * are reserved for `useSpring` calls, etc.\n */\nfunction getForwardProps<Props extends ReservedProps>(\n  props: Props\n): ForwardProps<Props> | undefined {\n  const forward: any = {}\n\n  let count = 0\n  eachProp(props, (value, prop) => {\n    if (!RESERVED_PROPS[prop]) {\n      forward[prop] = value\n      count++\n    }\n  })\n\n  if (count) {\n    return forward\n  }\n}\n\n/**\n * Clone the given `props` and move all non-reserved props\n * into the `to` prop.\n */\nexport function inferTo<T extends object>(props: T): InferTo<T> {\n  const to = getForwardProps(props)\n  if (to) {\n    const out: any = { to }\n    eachProp(props, (val, key) => key in to || (out[key] = val))\n    return out\n  }\n  return { ...props } as any\n}\n\n// Compute the goal value, converting \"red\" to \"rgba(255, 0, 0, 1)\" in the process\nexport function computeGoal<T>(value: T | FluidValue<T>): T {\n  value = getFluidValue(value)\n  return is.arr(value)\n    ? value.map(computeGoal)\n    : isAnimatedString(value)\n      ? (G.createStringInterpolator({\n          range: [0, 1],\n          output: [value, value] as any,\n        })(1) as any)\n      : value\n}\n\nexport function hasProps(props: object) {\n  for (const _ in props) return true\n  return false\n}\n\nexport function isAsyncTo(to: any) {\n  return is.fun(to) || (is.arr(to) && is.obj(to[0]))\n}\n\n/** Detach `ctrl` from `ctrl.ref` and (optionally) the given `ref` */\nexport function detachRefs(ctrl: Controller, ref?: SpringRef) {\n  ctrl.ref?.delete(ctrl)\n  ref?.delete(ctrl)\n}\n\n/** Replace `ctrl.ref` with the given `ref` (if defined) */\nexport function replaceRef(ctrl: Controller, ref?: SpringRef) {\n  if (ref && ctrl.ref !== ref) {\n    ctrl.ref?.delete(ctrl)\n    ref.add(ctrl)\n    ctrl.ref = ref\n  }\n}\n", "import { Lookup, Remap } from '@react-spring/types'\nimport { is } from '@react-spring/shared'\n\nimport { ControllerUpdate, PickAnimated, SpringValues } from '../types'\nimport { Valid } from '../types/common'\nimport { SpringRef } from '../SpringRef'\nimport { useSprings } from './useSprings'\n\n/**\n * The props that `useSpring` recognizes.\n */\nexport type UseSpringProps<Props extends object = any> = unknown &\n  PickAnimated<Props> extends infer State\n  ? State extends Lookup\n    ? Remap<\n        ControllerUpdate<State> & {\n          /**\n           * Used to access the imperative API.\n           *\n           * When defined, the render animation won't auto-start.\n           */\n          ref?: SpringRef<State>\n        }\n      >\n    : never\n  : never\n\n/**\n * The `props` function is only called on the first render, unless\n * `deps` change (when defined). State is inferred from forward props.\n */\nexport function useSpring<Props extends object>(\n  props:\n    | Function\n    | (() => (Props & Valid<Props, UseSpringProps<Props>>) | UseSpringProps),\n  deps?: readonly any[] | undefined\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup\n    ? [SpringValues<State>, SpringRef<State>]\n    : never\n  : never\n\n/**\n * Updated on every render, with state inferred from forward props.\n */\nexport function useSpring<Props extends object>(\n  props: (Props & Valid<Props, UseSpringProps<Props>>) | UseSpringProps\n): SpringValues<PickAnimated<Props>>\n\n/**\n * Updated only when `deps` change, with state inferred from forward props.\n */\nexport function useSpring<Props extends object>(\n  props: (Props & Valid<Props, UseSpringProps<Props>>) | UseSpringProps,\n  deps: readonly any[] | undefined\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup\n    ? [SpringValues<State>, SpringRef<State>]\n    : never\n  : never\n\n/** @internal */\nexport function useSpring(props: any, deps?: readonly any[]) {\n  const isFn = is.fun(props)\n  const [[values], ref] = useSprings(\n    1,\n    isFn ? props : [props],\n    isFn ? deps || [] : deps\n  )\n  return isFn || arguments.length == 2 ? [values, ref] : values\n}\n", "import { useContext, useMemo, useRef } from 'react'\nimport { Lookup } from '@react-spring/types'\nimport {\n  is,\n  each,\n  usePrev,\n  useOnce,\n  useForceUpdate,\n  useIsomorphicLayoutEffect,\n} from '@react-spring/shared'\n\nimport {\n  ControllerFlushFn,\n  ControllerUpdate,\n  PickAnimated,\n  SpringValues,\n} from '../types'\nimport { UseSpringProps } from './useSpring'\nimport { declareUpdate } from '../SpringValue'\nimport {\n  Controller,\n  getSprings,\n  flushUpdateQueue,\n  setSprings,\n} from '../Controller'\nimport { hasProps, detachRefs, replaceRef } from '../helpers'\nimport { SpringContext } from '../SpringContext'\nimport { SpringRef } from '../SpringRef'\nimport type { SpringRef as SpringRefType } from '../SpringRef'\n\nexport type UseSpringsProps<State extends Lookup = Lookup> = unknown &\n  ControllerUpdate<State> & {\n    ref?: SpringRefType<State>\n  }\n\n/**\n * When the `deps` argument exists, the `props` function is called whenever\n * the `deps` change on re-render.\n *\n * Without the `deps` argument, the `props` function is only called once.\n */\nexport function useSprings<Props extends UseSpringProps>(\n  length: number,\n  props: (i: number, ctrl: Controller) => Props,\n  deps?: readonly any[]\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup<any>\n    ? [SpringValues<State>[], SpringRefType<State>]\n    : never\n  : never\n\n/**\n * Animations are updated on re-render.\n */\nexport function useSprings<Props extends UseSpringsProps>(\n  length: number,\n  props: Props[] & UseSpringsProps<PickAnimated<Props>>[]\n): SpringValues<PickAnimated<Props>>[]\n\n/**\n * When the `deps` argument exists, you get the `update` and `stop` function.\n */\nexport function useSprings<Props extends UseSpringsProps>(\n  length: number,\n  props: Props[] & UseSpringsProps<PickAnimated<Props>>[],\n  deps: readonly any[] | undefined\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup<any>\n    ? [SpringValues<State>[], SpringRefType<State>]\n    : never\n  : never\n\n/** @internal */\nexport function useSprings(\n  length: number,\n  props: any[] | ((i: number, ctrl: Controller) => any),\n  deps?: readonly any[]\n): any {\n  const propsFn = is.fun(props) && props\n  if (propsFn && !deps) deps = []\n\n  // Create a local ref if a props function or deps array is ever passed.\n  const ref = useMemo(\n    () => (propsFn || arguments.length == 3 ? SpringRef() : void 0),\n    []\n  )\n\n  interface State {\n    // The controllers used for applying updates.\n    ctrls: Controller[]\n    // The queue of changes to make on commit.\n    queue: Array<() => void>\n    // The flush function used by controllers.\n    flush: ControllerFlushFn\n  }\n\n  // Set to 0 to prevent sync flush.\n  const layoutId = useRef(0)\n  const forceUpdate = useForceUpdate()\n\n  // State is updated on commit.\n  const state = useMemo(\n    (): State => ({\n      ctrls: [],\n      queue: [],\n      flush(ctrl, updates) {\n        const springs = getSprings(ctrl, updates)\n\n        // Flushing is postponed until the component's commit phase\n        // if a spring was created since the last commit.\n        const canFlushSync =\n          layoutId.current > 0 &&\n          !state.queue.length &&\n          !Object.keys(springs).some(key => !ctrl.springs[key])\n\n        return canFlushSync\n          ? flushUpdateQueue(ctrl, updates)\n          : new Promise<any>(resolve => {\n              setSprings(ctrl, springs)\n              state.queue.push(() => {\n                resolve(flushUpdateQueue(ctrl, updates))\n              })\n              forceUpdate()\n            })\n      },\n    }),\n    []\n  )\n\n  const ctrls = useRef([...state.ctrls])\n  const updates: any[] = []\n\n  // Cache old controllers to dispose in the commit phase.\n  const prevLength = usePrev(length) || 0\n\n  // Create new controllers when \"length\" increases, and destroy\n  // the affected controllers when \"length\" decreases.\n  useMemo(() => {\n    // Clean up any unused controllers\n    each(ctrls.current.slice(length, prevLength), ctrl => {\n      detachRefs(ctrl, ref)\n      ctrl.stop(true)\n    })\n    ctrls.current.length = length\n\n    declareUpdates(prevLength, length)\n  }, [length])\n\n  // Update existing controllers when \"deps\" are changed.\n  useMemo(() => {\n    declareUpdates(0, Math.min(prevLength, length))\n  }, deps)\n\n  /** Fill the `updates` array with declarative updates for the given index range. */\n  function declareUpdates(startIndex: number, endIndex: number) {\n    for (let i = startIndex; i < endIndex; i++) {\n      const ctrl =\n        ctrls.current[i] ||\n        (ctrls.current[i] = new Controller(null, state.flush))\n\n      const update: UseSpringProps<any> = propsFn\n        ? propsFn(i, ctrl)\n        : (props as any)[i]\n\n      if (update) {\n        updates[i] = declareUpdate(update)\n      }\n    }\n  }\n\n  // New springs are created during render so users can pass them to\n  // their animated components, but new springs aren't cached until the\n  // commit phase (see the `useIsomorphicLayoutEffect` callback below).\n  const springs = ctrls.current.map((ctrl, i) => getSprings(ctrl, updates[i]))\n\n  const context = useContext(SpringContext)\n  const prevContext = usePrev(context)\n  const hasContext = context !== prevContext && hasProps(context)\n\n  useIsomorphicLayoutEffect(() => {\n    layoutId.current++\n\n    // Replace the cached controllers.\n    state.ctrls = ctrls.current\n\n    // Flush the commit queue.\n    const { queue } = state\n    if (queue.length) {\n      state.queue = []\n      each(queue, cb => cb())\n    }\n\n    // Update existing controllers.\n    each(ctrls.current, (ctrl, i) => {\n      // Attach the controller to the local ref.\n      ref?.add(ctrl)\n\n      // Update the default props.\n      if (hasContext) {\n        ctrl.start({ default: context })\n      }\n\n      // Apply updates created during render.\n      const update = updates[i]\n      if (update) {\n        // Update the injected ref if needed.\n        replaceRef(ctrl, update.ref)\n\n        // When an injected ref exists, the update is postponed\n        // until the ref has its `start` method called.\n        if (ctrl.ref) {\n          ctrl.queue.push(update)\n        } else {\n          ctrl.start(update)\n        }\n      }\n    })\n  })\n\n  // Cancel the animations of all controllers on unmount.\n  useOnce(() => () => {\n    each(state.ctrls, ctrl => ctrl.stop(true))\n  })\n\n  // Return a deep copy of the `springs` array so the caller can\n  // safely mutate it during render.\n  const values = springs.map(x => ({ ...x }))\n\n  return ref ? [values, ref] : values\n}\n", "import {\n  is,\n  raf,\n  each,\n  isEqual,\n  toArray,\n  eachProp,\n  frameLoop,\n  flushCalls,\n  getFluidValue,\n  isAnimatedString,\n  FluidValue,\n  Globals as G,\n  callFluidObservers,\n  hasFluidValue,\n  addFluidObserver,\n  removeFluidObserver,\n  getFluidObservers,\n} from '@react-spring/shared'\nimport {\n  Animated,\n  AnimatedValue,\n  AnimatedString,\n  getPayload,\n  getAnimated,\n  setAnimated,\n  getAnimatedType,\n} from '@react-spring/animated'\nimport { Lookup } from '@react-spring/types'\n\nimport { Animation } from './Animation'\nimport { mergeConfig } from './AnimationConfig'\nimport { scheduleProps } from './scheduleProps'\nimport { runAsync, RunAsyncState, RunAsyncProps, stopAsync } from './runAsync'\nimport {\n  callProp,\n  computeGoal,\n  matchProp,\n  inferTo,\n  getDefaultProps,\n  getDefaultProp,\n  isAsyncTo,\n  resolveProp,\n} from './helpers'\nimport { FrameValue, isFrameValue } from './FrameValue'\nimport {\n  isAnimating,\n  isPaused,\n  setPausedBit,\n  hasAnimated,\n  setActiveBit,\n} from './SpringPhase'\nimport {\n  AnimationRange,\n  AnimationResolver,\n  EventKey,\n  PickEventFns,\n} from './types/internal'\nimport { AsyncResult, SpringUpdate, VelocityProp, SpringProps } from './types'\nimport {\n  getCombinedResult,\n  getCancelledResult,\n  getFinishedResult,\n  getNoopResult,\n} from './AnimationResult'\n\ndeclare const console: any\n\ninterface DefaultSpringProps<T>\n  extends Pick<SpringProps<T>, 'pause' | 'cancel' | 'immediate' | 'config'>,\n    PickEventFns<SpringProps<T>> {}\n\n/**\n * Only numbers, strings, and arrays of numbers/strings are supported.\n * Non-animatable strings are also supported.\n */\nexport class SpringValue<T = any> extends FrameValue<T> {\n  /** The property name used when `to` or `from` is an object. Useful when debugging too. */\n  key?: string\n\n  /** The animation state */\n  animation = new Animation<T>()\n\n  /** The queue of pending props */\n  queue?: SpringUpdate<T>[]\n\n  /** Some props have customizable default values */\n  defaultProps: DefaultSpringProps<T> = {}\n\n  /** The state for `runAsync` calls */\n  protected _state: RunAsyncState<SpringValue<T>> = {\n    paused: false,\n    delayed: false,\n    pauseQueue: new Set(),\n    resumeQueue: new Set(),\n    timeouts: new Set(),\n  }\n\n  /** The promise resolvers of pending `start` calls */\n  protected _pendingCalls = new Set<AnimationResolver<this>>()\n\n  /** The counter for tracking `scheduleProps` calls */\n  protected _lastCallId = 0\n\n  /** The last `scheduleProps` call that changed the `to` prop */\n  protected _lastToId = 0\n\n  protected _memoizedDuration = 0\n\n  constructor(from: Exclude<T, object>, props?: SpringUpdate<T>)\n  constructor(props?: SpringUpdate<T>)\n  constructor(arg1?: any, arg2?: any) {\n    super()\n    if (!is.und(arg1) || !is.und(arg2)) {\n      const props = is.obj(arg1) ? { ...arg1 } : { ...arg2, from: arg1 }\n      if (is.und(props.default)) {\n        props.default = true\n      }\n      this.start(props)\n    }\n  }\n\n  /** Equals true when not advancing on each frame. */\n  get idle() {\n    return !(isAnimating(this) || this._state.asyncTo) || isPaused(this)\n  }\n\n  get goal() {\n    return getFluidValue(this.animation.to) as T\n  }\n\n  get velocity(): VelocityProp<T> {\n    const node = getAnimated(this)!\n    return (\n      node instanceof AnimatedValue\n        ? node.lastVelocity || 0\n        : node.getPayload().map(node => node.lastVelocity || 0)\n    ) as any\n  }\n\n  /**\n   * When true, this value has been animated at least once.\n   */\n  get hasAnimated() {\n    return hasAnimated(this)\n  }\n\n  /**\n   * When true, this value has an unfinished animation,\n   * which is either active or paused.\n   */\n  get isAnimating() {\n    return isAnimating(this)\n  }\n\n  /**\n   * When true, all current and future animations are paused.\n   */\n  get isPaused() {\n    return isPaused(this)\n  }\n\n  /**\n   *\n   *\n   */\n  get isDelayed() {\n    return this._state.delayed\n  }\n\n  /** Advance the current animation by a number of milliseconds */\n  advance(dt: number) {\n    let idle = true\n    let changed = false\n\n    const anim = this.animation\n    let { toValues } = anim\n    const { config } = anim\n\n    const payload = getPayload(anim.to)\n    if (!payload && hasFluidValue(anim.to)) {\n      toValues = toArray(getFluidValue(anim.to)) as any\n    }\n\n    anim.values.forEach((node, i) => {\n      if (node.done) return\n\n      const to =\n        // Animated strings always go from 0 to 1.\n        node.constructor == AnimatedString\n          ? 1\n          : payload\n            ? payload[i].lastPosition\n            : toValues![i]\n\n      let finished = anim.immediate\n      let position = to\n\n      if (!finished) {\n        position = node.lastPosition\n\n        // Loose springs never move.\n        if (config.tension <= 0) {\n          node.done = true\n          return\n        }\n\n        let elapsed = (node.elapsedTime += dt)\n        const from = anim.fromValues[i]\n\n        const v0 =\n          node.v0 != null\n            ? node.v0\n            : (node.v0 = is.arr(config.velocity)\n                ? config.velocity[i]\n                : config.velocity)\n\n        let velocity: number\n\n        /** The smallest distance from a value before being treated like said value. */\n        /**\n         * TODO: make this value ~0.0001 by default in next breaking change\n         * for more info see – https://github.com/pmndrs/react-spring/issues/1389\n         */\n        const precision =\n          config.precision ||\n          (from == to ? 0.005 : Math.min(1, Math.abs(to - from) * 0.001))\n\n        // Duration easing\n        if (!is.und(config.duration)) {\n          let p = 1\n          if (config.duration > 0) {\n            /**\n             * Here we check if the duration has changed in the config\n             * and if so update the elapsed time to the percentage\n             * of completition so there is no jank in the animation\n             * https://github.com/pmndrs/react-spring/issues/1163\n             */\n            if (this._memoizedDuration !== config.duration) {\n              // update the memoized version to the new duration\n              this._memoizedDuration = config.duration\n\n              // if the value has started animating we need to update it\n              if (node.durationProgress > 0) {\n                // set elapsed time to be the same percentage of progress as the previous duration\n                node.elapsedTime = config.duration * node.durationProgress\n                // add the delta so the below updates work as expected\n                elapsed = node.elapsedTime += dt\n              }\n            }\n\n            // calculate the new progress\n            p = (config.progress || 0) + elapsed / this._memoizedDuration\n            // p is clamped between 0-1\n            p = p > 1 ? 1 : p < 0 ? 0 : p\n            // store our new progress\n            node.durationProgress = p\n          }\n\n          position = from + config.easing(p) * (to - from)\n          velocity = (position - node.lastPosition) / dt\n\n          finished = p == 1\n        }\n\n        // Decay easing\n        else if (config.decay) {\n          const decay = config.decay === true ? 0.998 : config.decay\n          const e = Math.exp(-(1 - decay) * elapsed)\n\n          position = from + (v0 / (1 - decay)) * (1 - e)\n          finished = Math.abs(node.lastPosition - position) <= precision\n\n          // derivative of position\n          velocity = v0 * e\n        }\n\n        // Spring easing\n        else {\n          velocity = node.lastVelocity == null ? v0 : node.lastVelocity\n\n          /** The velocity at which movement is essentially none */\n          const restVelocity = config.restVelocity || precision / 10\n\n          // Bouncing is opt-in (not to be confused with overshooting)\n          const bounceFactor = config.clamp ? 0 : config.bounce!\n          const canBounce = !is.und(bounceFactor)\n\n          /** When `true`, the value is increasing over time */\n          const isGrowing = from == to ? node.v0 > 0 : from < to\n\n          /** When `true`, the velocity is considered moving */\n          let isMoving!: boolean\n\n          /** When `true`, the velocity is being deflected or clamped */\n          let isBouncing = false\n\n          const step = 1 // 1ms\n          const numSteps = Math.ceil(dt / step)\n          for (let n = 0; n < numSteps; ++n) {\n            isMoving = Math.abs(velocity) > restVelocity\n\n            if (!isMoving) {\n              finished = Math.abs(to - position) <= precision\n              if (finished) {\n                break\n              }\n            }\n\n            if (canBounce) {\n              isBouncing = position == to || position > to == isGrowing\n\n              // Invert the velocity with a magnitude, or clamp it.\n              if (isBouncing) {\n                velocity = -velocity * bounceFactor\n                position = to\n              }\n            }\n\n            const springForce = -config.tension * 0.000001 * (position - to)\n            const dampingForce = -config.friction * 0.001 * velocity\n            const acceleration = (springForce + dampingForce) / config.mass // pt/ms^2\n\n            velocity = velocity + acceleration * step // pt/ms\n            position = position + velocity * step\n          }\n        }\n\n        node.lastVelocity = velocity\n\n        if (Number.isNaN(position)) {\n          console.warn(`Got NaN while animating:`, this)\n          finished = true\n        }\n      }\n\n      // Parent springs must finish before their children can.\n      if (payload && !payload[i].done) {\n        finished = false\n      }\n\n      if (finished) {\n        node.done = true\n      } else {\n        idle = false\n      }\n\n      if (node.setValue(position, config.round)) {\n        changed = true\n      }\n    })\n\n    const node = getAnimated(this)!\n    /**\n     * Get the node's current value, this will be different\n     * to anim.to when config.decay is true\n     */\n    const currVal = node.getValue()\n    if (idle) {\n      // get our final fluid val from the anim.to\n      const finalVal = getFluidValue(anim.to)\n      /**\n       * check if they're not equal, or if they're\n       * change and if there's no config.decay set\n       */\n      if ((currVal !== finalVal || changed) && !config.decay) {\n        // set the value to anim.to\n        node.setValue(finalVal)\n        this._onChange(finalVal)\n      } else if (changed && config.decay) {\n        /**\n         * if it's changed but there is a config.decay,\n         * just call _onChange with currrent value\n         */\n        this._onChange(currVal)\n      }\n      // call stop because the spring has stopped.\n      this._stop()\n    } else if (changed) {\n      /**\n       * if the spring has changed, but is not idle,\n       * just call the _onChange handler\n       */\n      this._onChange(currVal)\n    }\n  }\n\n  /** Set the current value, while stopping the current animation */\n  set(value: T | FluidValue<T>) {\n    raf.batchedUpdates(() => {\n      this._stop()\n\n      // These override the current value and goal value that may have\n      // been updated by `onRest` handlers in the `_stop` call above.\n      this._focus(value)\n      this._set(value)\n    })\n    return this\n  }\n\n  /**\n   * Freeze the active animation in time, as well as any updates merged\n   * before `resume` is called.\n   */\n  pause() {\n    this._update({ pause: true })\n  }\n\n  /** Resume the animation if paused. */\n  resume() {\n    this._update({ pause: false })\n  }\n\n  /** Skip to the end of the current animation. */\n  finish() {\n    if (isAnimating(this)) {\n      const { to, config } = this.animation\n      raf.batchedUpdates(() => {\n        // Ensure the \"onStart\" and \"onRest\" props are called.\n        this._onStart()\n\n        // Jump to the goal value, except for decay animations\n        // which have an undefined goal value.\n        if (!config.decay) {\n          this._set(to, false)\n        }\n\n        this._stop()\n      })\n    }\n    return this\n  }\n\n  /** Push props into the pending queue. */\n  update(props: SpringUpdate<T>) {\n    const queue = this.queue || (this.queue = [])\n    queue.push(props)\n    return this\n  }\n\n  /**\n   * Update this value's animation using the queue of pending props,\n   * and unpause the current animation (if one is frozen).\n   *\n   * When arguments are passed, a new animation is created, and the\n   * queued animations are left alone.\n   */\n  start(): AsyncResult<this>\n\n  start(props: SpringUpdate<T>): AsyncResult<this>\n\n  start(to: T, props?: SpringProps<T>): AsyncResult<this>\n\n  start(to?: any, arg2?: any) {\n    let queue: SpringUpdate<T>[]\n    if (!is.und(to)) {\n      queue = [is.obj(to) ? to : { ...arg2, to }]\n    } else {\n      queue = this.queue || []\n      this.queue = []\n    }\n\n    return Promise.all(\n      queue.map(props => {\n        const up = this._update(props)\n        return up\n      })\n    ).then(results => getCombinedResult(this, results))\n  }\n\n  /**\n   * Stop the current animation, and cancel any delayed updates.\n   *\n   * Pass `true` to call `onRest` with `cancelled: true`.\n   */\n  stop(cancel?: boolean) {\n    const { to } = this.animation\n\n    // The current value becomes the goal value.\n    this._focus(this.get())\n\n    stopAsync(this._state, cancel && this._lastCallId)\n    raf.batchedUpdates(() => this._stop(to, cancel))\n\n    return this\n  }\n\n  /** Restart the animation. */\n  reset() {\n    this._update({ reset: true })\n  }\n\n  /** @internal */\n  eventObserved(event: FrameValue.Event) {\n    if (event.type == 'change') {\n      this._start()\n    } else if (event.type == 'priority') {\n      this.priority = event.priority + 1\n    }\n  }\n\n  /**\n   * Parse the `to` and `from` range from the given `props` object.\n   *\n   * This also ensures the initial value is available to animated components\n   * during the render phase.\n   */\n  protected _prepareNode(props: {\n    to?: any\n    from?: any\n    reverse?: boolean\n    default?: any\n  }) {\n    const key = this.key || ''\n\n    let { to, from } = props\n\n    to = is.obj(to) ? to[key] : to\n    if (to == null || isAsyncTo(to)) {\n      to = undefined\n    }\n\n    from = is.obj(from) ? from[key] : from\n    if (from == null) {\n      from = undefined\n    }\n\n    // Create the range now to avoid \"reverse\" logic.\n    const range = { to, from }\n\n    // Before ever animating, this method ensures an `Animated` node\n    // exists and keeps its value in sync with the \"from\" prop.\n    if (!hasAnimated(this)) {\n      if (props.reverse) [to, from] = [from, to]\n\n      from = getFluidValue(from)\n      if (!is.und(from)) {\n        this._set(from)\n      }\n      // Use the \"to\" value if our node is undefined.\n      else if (!getAnimated(this)) {\n        this._set(to)\n      }\n    }\n\n    return range\n  }\n\n  /** Every update is processed by this method before merging. */\n  protected _update(\n    { ...props }: SpringProps<T>,\n    isLoop?: boolean\n  ): AsyncResult<SpringValue<T>> {\n    const { key, defaultProps } = this\n\n    // Update the default props immediately.\n    if (props.default)\n      Object.assign(\n        defaultProps,\n        getDefaultProps(props, (value, prop) =>\n          /^on/.test(prop) ? resolveProp(value, key) : value\n        )\n      )\n\n    mergeActiveFn(this, props, 'onProps')\n    sendEvent(this, 'onProps', props, this)\n\n    // Ensure the initial value can be accessed by animated components.\n    const range = this._prepareNode(props)\n\n    if (Object.isFrozen(this)) {\n      throw Error(\n        'Cannot animate a `SpringValue` object that is frozen. ' +\n          'Did you forget to pass your component to `animated(...)` before animating its props?'\n      )\n    }\n\n    const state = this._state\n\n    return scheduleProps(++this._lastCallId, {\n      key,\n      props,\n      defaultProps,\n      state,\n      actions: {\n        pause: () => {\n          if (!isPaused(this)) {\n            setPausedBit(this, true)\n            flushCalls(state.pauseQueue)\n            sendEvent(\n              this,\n              'onPause',\n              getFinishedResult(this, checkFinished(this, this.animation.to)),\n              this\n            )\n          }\n        },\n        resume: () => {\n          if (isPaused(this)) {\n            setPausedBit(this, false)\n            if (isAnimating(this)) {\n              this._resume()\n            }\n            flushCalls(state.resumeQueue)\n            sendEvent(\n              this,\n              'onResume',\n              getFinishedResult(this, checkFinished(this, this.animation.to)),\n              this\n            )\n          }\n        },\n        start: this._merge.bind(this, range),\n      },\n    }).then(result => {\n      if (props.loop && result.finished && !(isLoop && result.noop)) {\n        const nextProps = createLoopUpdate(props)\n        if (nextProps) {\n          return this._update(nextProps, true)\n        }\n      }\n      return result\n    })\n  }\n\n  /** Merge props into the current animation */\n  protected _merge(\n    range: AnimationRange<T>,\n    props: RunAsyncProps<SpringValue<T>>,\n    resolve: AnimationResolver<SpringValue<T>>\n  ): void {\n    // The \"cancel\" prop cancels all pending delays and it forces the\n    // active animation to stop where it is.\n    if (props.cancel) {\n      this.stop(true)\n      return resolve(getCancelledResult(this))\n    }\n\n    /** The \"to\" prop is defined. */\n    const hasToProp = !is.und(range.to)\n\n    /** The \"from\" prop is defined. */\n    const hasFromProp = !is.und(range.from)\n\n    // Avoid merging other props if implicitly prevented, except\n    // when both the \"to\" and \"from\" props are undefined.\n    if (hasToProp || hasFromProp) {\n      if (props.callId > this._lastToId) {\n        this._lastToId = props.callId\n      } else {\n        return resolve(getCancelledResult(this))\n      }\n    }\n\n    const { key, defaultProps, animation: anim } = this\n    const { to: prevTo, from: prevFrom } = anim\n    let { to = prevTo, from = prevFrom } = range\n\n    // Focus the \"from\" value if changing without a \"to\" value.\n    // For default updates, do this only if no \"to\" value exists.\n    if (hasFromProp && !hasToProp && (!props.default || is.und(to))) {\n      to = from\n    }\n\n    // Flip the current range if \"reverse\" is true.\n    if (props.reverse) [to, from] = [from, to]\n\n    /** The \"from\" value is changing. */\n    const hasFromChanged = !isEqual(from, prevFrom)\n\n    if (hasFromChanged) {\n      anim.from = from\n    }\n\n    // Coerce \"from\" into a static value.\n    from = getFluidValue(from)\n\n    /** The \"to\" value is changing. */\n    const hasToChanged = !isEqual(to, prevTo)\n\n    if (hasToChanged) {\n      this._focus(to)\n    }\n\n    /** The \"to\" prop is async. */\n    const hasAsyncTo = isAsyncTo(props.to)\n\n    const { config } = anim\n    const { decay, velocity } = config\n\n    // Reset to default velocity when goal values are defined.\n    if (hasToProp || hasFromProp) {\n      config.velocity = 0\n    }\n\n    // The \"runAsync\" function treats the \"config\" prop as a default,\n    // so we must avoid merging it when the \"to\" prop is async.\n    if (props.config && !hasAsyncTo) {\n      mergeConfig(\n        config,\n        callProp(props.config, key!),\n        // Avoid calling the same \"config\" prop twice.\n        props.config !== defaultProps.config\n          ? callProp(defaultProps.config, key!)\n          : void 0\n      )\n    }\n\n    // This instance might not have its Animated node yet. For example,\n    // the constructor can be given props without a \"to\" or \"from\" value.\n    let node = getAnimated(this)\n    if (!node || is.und(to)) {\n      return resolve(getFinishedResult(this, true))\n    }\n\n    /** When true, start at the \"from\" value. */\n    const reset =\n      // When `reset` is undefined, the `from` prop implies `reset: true`,\n      // except for declarative updates. When `reset` is defined, there\n      // must exist a value to animate from.\n      is.und(props.reset)\n        ? hasFromProp && !props.default\n        : !is.und(from) && matchProp(props.reset, key)\n\n    // The current value, where the animation starts from.\n    const value = reset ? (from as T) : this.get()\n\n    // The animation ends at this value, unless \"to\" is fluid.\n    const goal = computeGoal<any>(to)\n\n    // Only specific types can be animated to/from.\n    const isAnimatable = is.num(goal) || is.arr(goal) || isAnimatedString(goal)\n\n    // When true, the value changes instantly on the next frame.\n    const immediate =\n      !hasAsyncTo &&\n      (!isAnimatable ||\n        matchProp(defaultProps.immediate || props.immediate, key))\n\n    if (hasToChanged) {\n      const nodeType = getAnimatedType(to)\n      if (nodeType !== node.constructor) {\n        if (immediate) {\n          node = this._set(goal)!\n        } else\n          throw Error(\n            `Cannot animate between ${node.constructor.name} and ${nodeType.name}, as the \"to\" prop suggests`\n          )\n      }\n    }\n\n    // The type of Animated node for the goal value.\n    const goalType = node.constructor\n\n    // When the goal value is fluid, we don't know if its value\n    // will change before the next animation frame, so it always\n    // starts the animation to be safe.\n    let started = hasFluidValue(to)\n    let finished = false\n\n    if (!started) {\n      // When true, the current value has probably changed.\n      const hasValueChanged = reset || (!hasAnimated(this) && hasFromChanged)\n\n      // When the \"to\" value or current value are changed,\n      // start animating if not already finished.\n      if (hasToChanged || hasValueChanged) {\n        finished = isEqual(computeGoal(value), goal)\n        started = !finished\n      }\n\n      // Changing \"decay\" or \"velocity\" starts the animation.\n      if (\n        (!isEqual(anim.immediate, immediate) && !immediate) ||\n        !isEqual(config.decay, decay) ||\n        !isEqual(config.velocity, velocity)\n      ) {\n        started = true\n      }\n    }\n\n    // Was the goal value set to the current value while animating?\n    if (finished && isAnimating(this)) {\n      // If the first frame has passed, allow the animation to\n      // overshoot instead of stopping abruptly.\n      if (anim.changed && !reset) {\n        started = true\n      }\n      // Stop the animation before its first frame.\n      else if (!started) {\n        this._stop(prevTo)\n      }\n    }\n\n    if (!hasAsyncTo) {\n      // Make sure our \"toValues\" are updated even if our previous\n      // \"to\" prop is a fluid value whose current value is also ours.\n      if (started || hasFluidValue(prevTo)) {\n        anim.values = node.getPayload()\n        anim.toValues = hasFluidValue(to)\n          ? null\n          : goalType == AnimatedString\n            ? [1]\n            : toArray(goal)\n      }\n\n      if (anim.immediate != immediate) {\n        anim.immediate = immediate\n\n        // Ensure the immediate goal is used as from value.\n        if (!immediate && !reset) {\n          this._set(prevTo)\n        }\n      }\n\n      if (started) {\n        const { onRest } = anim\n\n        // Set the active handlers when an animation starts.\n        each(ACTIVE_EVENTS, type => mergeActiveFn(this, props, type))\n\n        const result = getFinishedResult(this, checkFinished(this, prevTo))\n        flushCalls(this._pendingCalls, result)\n        this._pendingCalls.add(resolve)\n\n        if (anim.changed)\n          raf.batchedUpdates(() => {\n            // Ensure `onStart` can be called after a reset.\n            anim.changed = !reset\n\n            // Call the active `onRest` handler from the interrupted animation.\n            onRest?.(result, this)\n\n            // Notify the default `onRest` of the reset, but wait for the\n            // first frame to pass before sending an `onStart` event.\n            if (reset) {\n              callProp(defaultProps.onRest, result)\n            }\n            // Call the active `onStart` handler here since the first frame\n            // has already passed, which means this is a goal update and not\n            // an entirely new animation.\n            else {\n              anim.onStart?.(result, this)\n            }\n          })\n      }\n    }\n\n    if (reset) {\n      this._set(value)\n    }\n\n    if (hasAsyncTo) {\n      resolve(runAsync(props.to, props, this._state, this))\n    }\n\n    // Start an animation\n    else if (started) {\n      this._start()\n    }\n\n    // Postpone promise resolution until the animation is finished,\n    // so that no-op updates still resolve at the expected time.\n    else if (isAnimating(this) && !hasToChanged) {\n      this._pendingCalls.add(resolve)\n    }\n\n    // Resolve our promise immediately.\n    else {\n      resolve(getNoopResult(value))\n    }\n  }\n\n  /** Update the `animation.to` value, which might be a `FluidValue` */\n  protected _focus(value: T | FluidValue<T>) {\n    const anim = this.animation\n    if (value !== anim.to) {\n      if (getFluidObservers(this)) {\n        this._detach()\n      }\n      anim.to = value\n      if (getFluidObservers(this)) {\n        this._attach()\n      }\n    }\n  }\n\n  protected _attach() {\n    let priority = 0\n\n    const { to } = this.animation\n    if (hasFluidValue(to)) {\n      addFluidObserver(to, this)\n      if (isFrameValue(to)) {\n        priority = to.priority + 1\n      }\n    }\n\n    this.priority = priority\n  }\n\n  protected _detach() {\n    const { to } = this.animation\n    if (hasFluidValue(to)) {\n      removeFluidObserver(to, this)\n    }\n  }\n\n  /**\n   * Update the current value from outside the frameloop,\n   * and return the `Animated` node.\n   */\n  protected _set(arg: T | FluidValue<T>, idle = true): Animated | undefined {\n    const value = getFluidValue(arg)\n    if (!is.und(value)) {\n      const oldNode = getAnimated(this)\n      if (!oldNode || !isEqual(value, oldNode.getValue())) {\n        // Create a new node or update the existing node.\n        const nodeType = getAnimatedType(value)\n        if (!oldNode || oldNode.constructor != nodeType) {\n          setAnimated(this, nodeType.create(value))\n        } else {\n          oldNode.setValue(value)\n        }\n        // Never emit a \"change\" event for the initial value.\n        if (oldNode) {\n          raf.batchedUpdates(() => {\n            this._onChange(value, idle)\n          })\n        }\n      }\n    }\n    return getAnimated(this)\n  }\n\n  protected _onStart() {\n    const anim = this.animation\n    if (!anim.changed) {\n      anim.changed = true\n      sendEvent(\n        this,\n        'onStart',\n        getFinishedResult(this, checkFinished(this, anim.to)),\n        this\n      )\n    }\n  }\n\n  protected _onChange(value: T, idle?: boolean) {\n    if (!idle) {\n      this._onStart()\n      callProp(this.animation.onChange, value, this)\n    }\n    callProp(this.defaultProps.onChange, value, this)\n    super._onChange(value, idle)\n  }\n\n  // This method resets the animation state (even if already animating) to\n  // ensure the latest from/to range is used, and it also ensures this spring\n  // is added to the frameloop.\n  protected _start() {\n    const anim = this.animation\n\n    // Reset the state of each Animated node.\n    getAnimated(this)!.reset(getFluidValue(anim.to))\n\n    // Use the current values as the from values.\n    if (!anim.immediate) {\n      anim.fromValues = anim.values.map(node => node.lastPosition)\n    }\n\n    if (!isAnimating(this)) {\n      setActiveBit(this, true)\n      if (!isPaused(this)) {\n        this._resume()\n      }\n    }\n  }\n\n  protected _resume() {\n    // The \"skipAnimation\" global avoids the frameloop.\n    if (G.skipAnimation) {\n      this.finish()\n    } else {\n      frameLoop.start(this)\n    }\n  }\n\n  /**\n   * Exit the frameloop and notify `onRest` listeners.\n   *\n   * Always wrap `_stop` calls with `batchedUpdates`.\n   */\n  protected _stop(goal?: any, cancel?: boolean) {\n    if (isAnimating(this)) {\n      setActiveBit(this, false)\n\n      const anim = this.animation\n      each(anim.values, node => {\n        node.done = true\n      })\n\n      // These active handlers must be reset to undefined or else\n      // they could be called while idle. But keep them defined\n      // when the goal value is dynamic.\n      if (anim.toValues) {\n        anim.onChange = anim.onPause = anim.onResume = undefined\n      }\n\n      callFluidObservers(this, {\n        type: 'idle',\n        parent: this,\n      })\n\n      const result = cancel\n        ? getCancelledResult(this.get())\n        : getFinishedResult(this.get(), checkFinished(this, goal ?? anim.to))\n\n      flushCalls(this._pendingCalls, result)\n      if (anim.changed) {\n        anim.changed = false\n        sendEvent(this, 'onRest', result, this)\n      }\n    }\n  }\n}\n\n/** Returns true when the current value and goal value are equal. */\nfunction checkFinished<T>(target: SpringValue<T>, to: T | FluidValue<T>) {\n  const goal = computeGoal(to)\n  const value = computeGoal(target.get())\n  return isEqual(value, goal)\n}\n\nexport function createLoopUpdate<T>(\n  props: T & { loop?: any; to?: any; from?: any; reverse?: any },\n  loop = props.loop,\n  to = props.to\n): T | undefined {\n  const loopRet = callProp(loop)\n  if (loopRet) {\n    const overrides = loopRet !== true && inferTo(loopRet)\n    const reverse = (overrides || props).reverse\n    const reset = !overrides || overrides.reset\n    return createUpdate({\n      ...props,\n      loop,\n\n      // Avoid updating default props when looping.\n      default: false,\n\n      // Never loop the `pause` prop.\n      pause: undefined,\n\n      // For the \"reverse\" prop to loop as expected, the \"to\" prop\n      // must be undefined. The \"reverse\" prop is ignored when the\n      // \"to\" prop is an array or function.\n      to: !reverse || isAsyncTo(to) ? to : undefined,\n\n      // Ignore the \"from\" prop except on reset.\n      from: reset ? props.from : undefined,\n      reset,\n\n      // The \"loop\" prop can return a \"useSpring\" props object to\n      // override any of the original props.\n      ...overrides,\n    })\n  }\n}\n\n/**\n * Return a new object based on the given `props`.\n *\n * - All non-reserved props are moved into the `to` prop object.\n * - The `keys` prop is set to an array of affected keys,\n *   or `null` if all keys are affected.\n */\nexport function createUpdate(props: any) {\n  const { to, from } = (props = inferTo(props))\n\n  // Collect the keys affected by this update.\n  const keys = new Set<string>()\n\n  if (is.obj(to)) findDefined(to, keys)\n  if (is.obj(from)) findDefined(from, keys)\n\n  // The \"keys\" prop helps in applying updates to affected keys only.\n  props.keys = keys.size ? Array.from(keys) : null\n\n  return props\n}\n\n/**\n * A modified version of `createUpdate` meant for declarative APIs.\n */\nexport function declareUpdate(props: any) {\n  const update = createUpdate(props)\n  if (is.und(update.default)) {\n    update.default = getDefaultProps(update)\n  }\n  return update\n}\n\n/** Find keys with defined values */\nfunction findDefined(values: Lookup, keys: Set<string>) {\n  eachProp(values, (value, key) => value != null && keys.add(key as any))\n}\n\n/** Event props with \"active handler\" support */\nconst ACTIVE_EVENTS = [\n  'onStart',\n  'onRest',\n  'onChange',\n  'onPause',\n  'onResume',\n] as const\n\nfunction mergeActiveFn<T, P extends EventKey>(\n  target: SpringValue<T>,\n  props: SpringProps<T>,\n  type: P\n) {\n  target.animation[type] =\n    props[type] !== getDefaultProp(props, type)\n      ? resolveProp<any>(props[type], target.key)\n      : undefined\n}\n\ntype EventArgs<T, P extends EventKey> = Parameters<\n  Extract<SpringProps<T>[P], Function>\n>\n\n/** Call the active handler first, then the default handler. */\nfunction sendEvent<T, P extends EventKey>(\n  target: SpringValue<T>,\n  type: P,\n  ...args: EventArgs<T, P>\n) {\n  target.animation[type]?.(...(args as [any, any]))\n  target.defaultProps[type]?.(...(args as [any, any]))\n}\n", "import { is, easings } from '@react-spring/shared'\nimport { EasingFunction } from '@react-spring/types'\nimport { config as configs } from './constants'\n\nconst defaults: any = {\n  ...configs.default,\n  mass: 1,\n  damping: 1,\n  easing: easings.linear,\n  clamp: false,\n}\n\nexport class AnimationConfig {\n  /**\n   * With higher tension, the spring will resist bouncing and try harder to stop at its end value.\n   *\n   * When tension is zero, no animation occurs.\n   *\n   * @default 170\n   */\n  tension!: number\n\n  /**\n   * The damping ratio coefficient, or just the damping ratio when `speed` is defined.\n   *\n   * When `speed` is defined, this value should be between 0 and 1.\n   *\n   * Higher friction means the spring will slow down faster.\n   *\n   * @default 26\n   */\n  friction!: number\n\n  /**\n   * The natural frequency (in seconds), which dictates the number of bounces\n   * per second when no damping exists.\n   *\n   * When defined, `tension` is derived from this, and `friction` is derived\n   * from `tension` and `damping`.\n   */\n  frequency?: number\n\n  /**\n   * The damping ratio, which dictates how the spring slows down.\n   *\n   * Set to `0` to never slow down. Set to `1` to slow down without bouncing.\n   * Between `0` and `1` is for you to explore.\n   *\n   * Only works when `frequency` is defined.\n   *\n   * @default 1\n   */\n  damping!: number\n\n  /**\n   * Higher mass means more friction is required to slow down.\n   *\n   * Defaults to 1, which works fine most of the time.\n   *\n   * @default 1\n   */\n  mass!: number\n\n  /**\n   * The initial velocity of one or more values.\n   *\n   * @default 0\n   */\n  velocity: number | number[] = 0\n\n  /**\n   * The smallest velocity before the animation is considered \"not moving\".\n   *\n   * When undefined, `precision` is used instead.\n   */\n  restVelocity?: number\n\n  /**\n   * The smallest distance from a value before that distance is essentially zero.\n   *\n   * This helps in deciding when a spring is \"at rest\". The spring must be within\n   * this distance from its final value, and its velocity must be lower than this\n   * value too (unless `restVelocity` is defined).\n   *\n   * @default 0.01\n   */\n  precision?: number\n\n  /**\n   * For `duration` animations only. Note: The `duration` is not affected\n   * by this property.\n   *\n   * Defaults to `0`, which means \"start from the beginning\".\n   *\n   * Setting to `1+` makes an immediate animation.\n   *\n   * Setting to `0.5` means \"start from the middle of the easing function\".\n   *\n   * Any number `>= 0` and `<= 1` makes sense here.\n   */\n  progress?: number\n\n  /**\n   * Animation length in number of milliseconds.\n   */\n  duration?: number\n\n  /**\n   * The animation curve. Only used when `duration` is defined.\n   *\n   * Defaults to quadratic ease-in-out.\n   */\n  easing!: EasingFunction\n\n  /**\n   * Avoid overshooting by ending abruptly at the goal value.\n   *\n   * @default false\n   */\n  clamp!: boolean\n\n  /**\n   * When above zero, the spring will bounce instead of overshooting when\n   * exceeding its goal value. Its velocity is multiplied by `-1 + bounce`\n   * whenever its current value equals or exceeds its goal. For example,\n   * setting `bounce` to `0.5` chops the velocity in half on each bounce,\n   * in addition to any friction.\n   */\n  bounce?: number\n\n  /**\n   * \"Decay animations\" decelerate without an explicit goal value.\n   * Useful for scrolling animations.\n   *\n   * Use `true` for the default exponential decay factor (`0.998`).\n   *\n   * When a `number` between `0` and `1` is given, a lower number makes the\n   * animation slow down faster. And setting to `1` would make an unending\n   * animation.\n   *\n   * @default false\n   */\n  decay?: boolean | number\n\n  /**\n   * While animating, round to the nearest multiple of this number.\n   * The `from` and `to` values are never rounded, as well as any value\n   * passed to the `set` method of an animated value.\n   */\n  round?: number\n\n  constructor() {\n    Object.assign(this, defaults)\n  }\n}\n\nexport function mergeConfig(\n  config: AnimationConfig,\n  newConfig: Partial<AnimationConfig>,\n  defaultConfig?: Partial<AnimationConfig>\n): typeof config\n\nexport function mergeConfig(\n  config: any,\n  newConfig: object,\n  defaultConfig?: object\n) {\n  if (defaultConfig) {\n    defaultConfig = { ...defaultConfig }\n    sanitizeConfig(defaultConfig, newConfig)\n    newConfig = { ...defaultConfig, ...newConfig }\n  }\n\n  sanitizeConfig(config, newConfig)\n  Object.assign(config, newConfig)\n\n  for (const key in defaults) {\n    if (config[key] == null) {\n      config[key] = defaults[key]\n    }\n  }\n\n  let { frequency, damping } = config\n  const { mass } = config\n  if (!is.und(frequency)) {\n    if (frequency < 0.01) frequency = 0.01\n    if (damping < 0) damping = 0\n    config.tension = Math.pow((2 * Math.PI) / frequency, 2) * mass\n    config.friction = (4 * Math.PI * damping * mass) / frequency\n  }\n\n  return config\n}\n\n// Prevent a config from accidentally overriding new props.\n// This depends on which \"config\" props take precedence when defined.\nfunction sanitizeConfig(\n  config: Partial<AnimationConfig>,\n  props: Partial<AnimationConfig>\n) {\n  if (!is.und(props.decay)) {\n    config.duration = undefined\n  } else {\n    const isTensionConfig = !is.und(props.tension) || !is.und(props.friction)\n    if (\n      isTensionConfig ||\n      !is.und(props.frequency) ||\n      !is.und(props.damping) ||\n      !is.und(props.mass)\n    ) {\n      config.duration = undefined\n      config.decay = undefined\n    }\n    if (isTensionConfig) {\n      config.frequency = undefined\n    }\n  }\n}\n", "// The `mass` prop defaults to 1\nexport const config = {\n  default: { tension: 170, friction: 26 },\n  gentle: { tension: 120, friction: 14 },\n  wobbly: { tension: 180, friction: 12 },\n  stiff: { tension: 210, friction: 20 },\n  slow: { tension: 280, friction: 60 },\n  molasses: { tension: 280, friction: 120 },\n} as const\n", "import { AnimatedValue } from '@react-spring/animated'\nimport { FluidValue } from '@react-spring/shared'\nimport { AnimationConfig } from './AnimationConfig'\nimport { PickEventFns } from './types/internal'\nimport { SpringProps } from './types'\n\nconst emptyArray: readonly any[] = []\n\n/** An animation being executed by the frameloop */\n// eslint-disable-next-line @typescript-eslint/no-unsafe-declaration-merging\nexport class Animation<T = any> {\n  changed = false\n  values: readonly AnimatedValue[] = emptyArray\n  toValues: readonly number[] | null = null\n  fromValues: readonly number[] = emptyArray\n\n  to!: T | FluidValue<T>\n  from!: T | FluidValue<T>\n  config = new AnimationConfig()\n  immediate = false\n}\n\nexport interface Animation<T> extends PickEventFns<SpringProps<T>> {}\n", "import { Timeout, is, raf, Globals as G } from '@react-spring/shared'\nimport { matchProp, callProp } from './helpers'\nimport { AsyncResult, MatchProp } from './types'\nimport { RunAsyncState, RunAsyncProps } from './runAsync'\nimport {\n  AnimationResolver,\n  AnimationTarget,\n  InferProps,\n  InferState,\n} from './types/internal'\n\n// The `scheduleProps` function only handles these defaults.\ntype DefaultProps<T> = { cancel?: MatchProp<T>; pause?: MatchProp<T> }\n\ninterface ScheduledProps<T extends AnimationTarget> {\n  key?: string\n  props: InferProps<T>\n  defaultProps?: DefaultProps<InferState<T>>\n  state: RunAsyncState<T>\n  actions: {\n    pause: () => void\n    resume: () => void\n    start: (props: RunAsyncProps<T>, resolve: AnimationResolver<T>) => void\n  }\n}\n\n/**\n * This function sets a timeout if both the `delay` prop exists and\n * the `cancel` prop is not `true`.\n *\n * The `actions.start` function must handle the `cancel` prop itself,\n * but the `pause` prop is taken care of.\n */\nexport function scheduleProps<T extends AnimationTarget>(\n  callId: number,\n  { key, props, defaultProps, state, actions }: ScheduledProps<T>\n): AsyncResult<T> {\n  return new Promise((resolve, reject) => {\n    let delay: number\n    let timeout: Timeout\n\n    let cancel = matchProp(props.cancel ?? defaultProps?.cancel, key)\n    if (cancel) {\n      onStart()\n    } else {\n      // The `pause` prop updates the paused flag.\n      if (!is.und(props.pause)) {\n        state.paused = matchProp(props.pause, key)\n      }\n      // The default `pause` takes precedence when true,\n      // which allows `SpringContext` to work as expected.\n      let pause = defaultProps?.pause\n      if (pause !== true) {\n        pause = state.paused || matchProp(pause, key)\n      }\n\n      delay = callProp(props.delay || 0, key)\n      if (pause) {\n        state.resumeQueue.add(onResume)\n        actions.pause()\n      } else {\n        actions.resume()\n        onResume()\n      }\n    }\n\n    function onPause() {\n      state.resumeQueue.add(onResume)\n      state.timeouts.delete(timeout)\n      timeout.cancel()\n      // Cache the remaining delay.\n      delay = timeout.time - raf.now()\n    }\n\n    function onResume() {\n      if (delay > 0 && !G.skipAnimation) {\n        state.delayed = true\n        timeout = raf.setTimeout(onStart, delay)\n        state.pauseQueue.add(onPause)\n        state.timeouts.add(timeout)\n      } else {\n        onStart()\n      }\n    }\n\n    function onStart() {\n      if (state.delayed) {\n        state.delayed = false\n      }\n\n      state.pauseQueue.delete(onPause)\n      state.timeouts.delete(timeout)\n\n      // Maybe cancelled during its delay.\n      if (callId <= (state.cancelId || 0)) {\n        cancel = true\n      }\n\n      try {\n        actions.start({ ...props, callId, cancel }, resolve)\n      } catch (err) {\n        reject(err)\n      }\n    }\n  })\n}\n", "import {\n  is,\n  raf,\n  flush,\n  eachProp,\n  Timeout,\n  Globals as G,\n} from '@react-spring/shared'\nimport { Falsy } from '@react-spring/types'\n\nimport { getDefaultProps } from './helpers'\nimport { AnimationTarget, InferState, InferProps } from './types/internal'\nimport { AnimationResult, AsyncResult, SpringChain, SpringToFn } from './types'\nimport { getCancelledResult, getFinishedResult } from './AnimationResult'\n\ntype AsyncTo<T> = SpringChain<T> | SpringToFn<T>\n\n/** @internal */\nexport type RunAsyncProps<T extends AnimationTarget = any> = InferProps<T> & {\n  callId: number\n  parentId?: number\n  cancel: boolean\n  to?: any\n}\n\n/** @internal */\nexport interface RunAsyncState<T extends AnimationTarget = any> {\n  paused: boolean\n  pauseQueue: Set<() => void>\n  resumeQueue: Set<() => void>\n  timeouts: Set<Timeout>\n  delayed?: boolean\n  asyncId?: number\n  asyncTo?: AsyncTo<InferState<T>>\n  promise?: AsyncResult<T>\n  cancelId?: number\n}\n\n/**\n * Start an async chain or an async script.\n *\n * Always call `runAsync` in the action callback of a `scheduleProps` call.\n *\n * The `T` parameter can be a set of animated values (as an object type)\n * or a primitive type for a single animated value.\n */\nexport function runAsync<T extends AnimationTarget>(\n  to: AsyncTo<InferState<T>>,\n  props: RunAsyncProps<T>,\n  state: RunAsyncState<T>,\n  target: T\n): AsyncResult<T> {\n  const { callId, parentId, onRest } = props\n  const { asyncTo: prevTo, promise: prevPromise } = state\n\n  if (!parentId && to === prevTo && !props.reset) {\n    return prevPromise!\n  }\n\n  return (state.promise = (async () => {\n    state.asyncId = callId\n    state.asyncTo = to\n\n    // The default props of any `animate` calls.\n    const defaultProps = getDefaultProps<InferProps<T>>(props, (value, key) =>\n      // The `onRest` prop is only called when the `runAsync` promise is resolved.\n      key === 'onRest' ? undefined : value\n    )\n\n    let preventBail!: () => void\n    let bail: (error: any) => void\n\n    // This promise is rejected when the animation is interrupted.\n    const bailPromise = new Promise<void>(\n      (resolve, reject) => ((preventBail = resolve), (bail = reject))\n    )\n\n    const bailIfEnded = (bailSignal: BailSignal) => {\n      const bailResult =\n        // The `cancel` prop or `stop` method was used.\n        (callId <= (state.cancelId || 0) && getCancelledResult(target)) ||\n        // The async `to` prop was replaced.\n        (callId !== state.asyncId && getFinishedResult(target, false))\n\n      if (bailResult) {\n        bailSignal.result = bailResult\n\n        // Reject the `bailPromise` to ensure the `runAsync` promise\n        // is not relying on the caller to rethrow the error for us.\n        bail(bailSignal)\n        throw bailSignal\n      }\n    }\n\n    const animate: any = (arg1: any, arg2?: any) => {\n      // Create the bail signal outside the returned promise,\n      // so the generated stack trace is relevant.\n      const bailSignal = new BailSignal()\n      const skipAnimationSignal = new SkipAnimationSignal()\n\n      return (async () => {\n        if (G.skipAnimation) {\n          /**\n           * We need to stop animations if `skipAnimation`\n           * is set in the Globals\n           *\n           */\n          stopAsync(state)\n\n          // create the rejection error that's handled gracefully\n          skipAnimationSignal.result = getFinishedResult(target, false)\n          bail(skipAnimationSignal)\n          throw skipAnimationSignal\n        }\n\n        bailIfEnded(bailSignal)\n\n        const props: any = is.obj(arg1) ? { ...arg1 } : { ...arg2, to: arg1 }\n        props.parentId = callId\n\n        eachProp(defaultProps, (value, key) => {\n          if (is.und(props[key])) {\n            props[key] = value\n          }\n        })\n\n        const result = await target.start(props)\n        bailIfEnded(bailSignal)\n\n        if (state.paused) {\n          await new Promise<void>(resume => {\n            state.resumeQueue.add(resume)\n          })\n        }\n\n        return result\n      })()\n    }\n\n    let result!: AnimationResult<T>\n\n    if (G.skipAnimation) {\n      /**\n       * We need to stop animations if `skipAnimation`\n       * is set in the Globals\n       */\n      stopAsync(state)\n      return getFinishedResult(target, false)\n    }\n\n    try {\n      let animating!: Promise<void>\n\n      // Async sequence\n      if (is.arr(to)) {\n        animating = (async (queue: any[]) => {\n          for (const props of queue) {\n            await animate(props)\n          }\n        })(to)\n      }\n\n      // Async script\n      else {\n        animating = Promise.resolve(to(animate, target.stop.bind(target)))\n      }\n\n      await Promise.all([animating.then(preventBail), bailPromise])\n      result = getFinishedResult(target.get(), true, false)\n\n      // Bail handling\n    } catch (err) {\n      if (err instanceof BailSignal) {\n        result = err.result\n      } else if (err instanceof SkipAnimationSignal) {\n        result = err.result\n      } else {\n        throw err\n      }\n\n      // Reset the async state.\n    } finally {\n      if (callId == state.asyncId) {\n        state.asyncId = parentId\n        state.asyncTo = parentId ? prevTo : undefined\n        state.promise = parentId ? prevPromise : undefined\n      }\n    }\n\n    if (is.fun(onRest)) {\n      raf.batchedUpdates(() => {\n        onRest(result, target, target.item)\n      })\n    }\n\n    return result\n  })())\n}\n\n/** Stop the current `runAsync` call with `finished: false` (or with `cancelled: true` when `cancelId` is defined) */\nexport function stopAsync(state: RunAsyncState, cancelId?: number | Falsy) {\n  flush(state.timeouts, t => t.cancel())\n  state.pauseQueue.clear()\n  state.resumeQueue.clear()\n  state.asyncId = state.asyncTo = state.promise = undefined\n  if (cancelId) state.cancelId = cancelId\n}\n\n/** This error is thrown to signal an interrupted async animation. */\nexport class BailSignal extends Error {\n  result!: AnimationResult\n  constructor() {\n    super(\n      'An async animation has been interrupted. You see this error because you ' +\n        'forgot to use `await` or `.catch(...)` on its returned promise.'\n    )\n  }\n}\n\nexport class SkipAnimationSignal extends Error {\n  result!: AnimationResult\n\n  constructor() {\n    super('SkipAnimationSignal')\n  }\n}\n", "import { AnimationResult } from './types'\nimport { Readable } from './types/internal'\n\n/** @internal */\nexport const getCombinedResult = <T extends Readable>(\n  target: T,\n  results: AnimationResult<T>[]\n): AnimationResult<T> =>\n  results.length == 1\n    ? results[0]\n    : results.some(result => result.cancelled)\n      ? getCancelledResult(target.get())\n      : results.every(result => result.noop)\n        ? getNoopResult(target.get())\n        : getFinishedResult(\n            target.get(),\n            results.every(result => result.finished)\n          )\n\n/** No-op results are for updates that never start an animation. */\nexport const getNoopResult = (value: any) => ({\n  value,\n  noop: true,\n  finished: true,\n  cancelled: false,\n})\n\nexport const getFinishedResult = (\n  value: any,\n  finished: boolean,\n  cancelled = false\n) => ({\n  value,\n  finished,\n  cancelled,\n})\n\nexport const getCancelledResult = (value: any) => ({\n  value,\n  cancelled: true,\n  finished: false,\n})\n", "import {\n  deprecateInterpolate,\n  frameLoop,\n  FluidValue,\n  Globals as G,\n  callFluidObservers,\n} from '@react-spring/shared'\nimport { InterpolatorArgs } from '@react-spring/types'\nimport { getAnimated } from '@react-spring/animated'\n\nimport { Interpolation } from './Interpolation'\n\nexport const isFrameValue = (value: any): value is FrameValue =>\n  value instanceof FrameValue\n\nlet nextId = 1\n\n/**\n * A kind of `FluidValue` that manages an `AnimatedValue` node.\n *\n * Its underlying value can be accessed and even observed.\n */\nexport abstract class FrameValue<T = any> extends FluidValue<\n  T,\n  FrameValue.Event<T>\n> {\n  readonly id = nextId++\n\n  abstract key?: string\n  abstract get idle(): boolean\n\n  protected _priority = 0\n\n  get priority() {\n    return this._priority\n  }\n  set priority(priority: number) {\n    if (this._priority != priority) {\n      this._priority = priority\n      this._onPriorityChange(priority)\n    }\n  }\n\n  /** Get the current value */\n  get(): T {\n    const node = getAnimated(this)\n    return node && node.getValue()\n  }\n\n  /** Create a spring that maps our value to another value */\n  to<Out>(...args: InterpolatorArgs<T, Out>) {\n    return G.to(this, args) as Interpolation<T, Out>\n  }\n\n  /** @deprecated Use the `to` method instead. */\n  interpolate<Out>(...args: InterpolatorArgs<T, Out>) {\n    deprecateInterpolate()\n    return G.to(this, args) as Interpolation<T, Out>\n  }\n\n  toJSON() {\n    return this.get()\n  }\n\n  protected observerAdded(count: number) {\n    if (count == 1) this._attach()\n  }\n\n  protected observerRemoved(count: number) {\n    if (count == 0) this._detach()\n  }\n\n  /** @internal */\n  abstract advance(dt: number): void\n\n  /** @internal */\n  abstract eventObserved(_event: FrameValue.Event): void\n\n  /** Called when the first child is added. */\n  protected _attach() {}\n\n  /** Called when the last child is removed. */\n  protected _detach() {}\n\n  /** Tell our children about our new value */\n  protected _onChange(value: T, idle = false) {\n    callFluidObservers(this, {\n      type: 'change',\n      parent: this,\n      value,\n      idle,\n    })\n  }\n\n  /** Tell our children about our new priority */\n  protected _onPriorityChange(priority: number) {\n    if (!this.idle) {\n      frameLoop.sort(this)\n    }\n    callFluidObservers(this, {\n      type: 'priority',\n      parent: this,\n      priority,\n    })\n  }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-namespace\nexport declare namespace FrameValue {\n  /** A parent changed its value */\n  interface ChangeEvent<T = any> {\n    parent: FrameValue<T>\n    type: 'change'\n    value: T\n    idle: boolean\n  }\n\n  /** A parent changed its priority */\n  interface PriorityEvent<T = any> {\n    parent: FrameValue<T>\n    type: 'priority'\n    priority: number\n  }\n\n  /** A parent is done animating */\n  interface IdleEvent<T = any> {\n    parent: FrameValue<T>\n    type: 'idle'\n  }\n\n  /** Events sent to children of `FrameValue` objects */\n  export type Event<T = any> = ChangeEvent<T> | PriorityEvent<T> | IdleEvent<T>\n}\n", "/** The property symbol of the current animation phase. */\nconst $P = Symbol.for('SpringPhase')\n\nconst HAS_ANIMATED = 1\nconst IS_ANIMATING = 2\nconst IS_PAUSED = 4\n\n/** Returns true if the `target` has ever animated. */\nexport const hasAnimated = (target: any) => (target[$P] & HAS_ANIMATED) > 0\n\n/** Returns true if the `target` is animating (even if paused). */\nexport const isAnimating = (target: any) => (target[$P] & IS_ANIMATING) > 0\n\n/** Returns true if the `target` is paused (even if idle). */\nexport const isPaused = (target: any) => (target[$P] & IS_PAUSED) > 0\n\n/** Set the active bit of the `target` phase. */\nexport const setActiveBit = (target: any, active: boolean) =>\n  active\n    ? (target[$P] |= IS_ANIMATING | HAS_ANIMATED)\n    : (target[$P] &= ~IS_ANIMATING)\n\nexport const setPausedBit = (target: any, paused: boolean) =>\n  paused ? (target[$P] |= IS_PAUSED) : (target[$P] &= ~IS_PAUSED)\n", "import { <PERSON><PERSON>r<PERSON>ore, UnknownProps, Lookup, Falsy } from '@react-spring/types'\nimport {\n  is,\n  raf,\n  each,\n  noop,\n  flush,\n  toArray,\n  eachProp,\n  flushCalls,\n  addFluidObserver,\n  FluidObserver,\n} from '@react-spring/shared'\n\nimport { getDefaultProp } from './helpers'\nimport { FrameValue } from './FrameValue'\nimport type { SpringRef } from './SpringRef'\nimport { SpringValue, createLoopUpdate, createUpdate } from './SpringValue'\nimport { getCancelledResult, getCombinedResult } from './AnimationResult'\nimport { runAsync, RunAsyncState, stopAsync } from './runAsync'\nimport { scheduleProps } from './scheduleProps'\nimport {\n  AnimationResult,\n  AsyncResult,\n  ControllerFlushFn,\n  ControllerUpdate,\n  OnChange,\n  OnRest,\n  OnStart,\n  SpringChain,\n  SpringToFn,\n  SpringValues,\n} from './types'\n\n/** Events batched by the `Controller` class */\nconst BATCHED_EVENTS = ['onStart', 'onChange', 'onRest'] as const\n\nlet nextId = 1\n\n/** Queue of pending updates for a `Controller` instance. */\nexport interface ControllerQueue<State extends Lookup = Lookup>\n  extends Array<\n    ControllerUpdate<State, any> & {\n      /** The keys affected by this update. When null, all keys are affected. */\n      keys: string[] | null\n    }\n  > {}\n\nexport class Controller<State extends Lookup = Lookup> {\n  readonly id = nextId++\n\n  /** The animated values */\n  springs: SpringValues<State> = {} as any\n\n  /** The queue of props passed to the `update` method. */\n  queue: ControllerQueue<State> = []\n\n  /**\n   * The injected ref. When defined, render-based updates are pushed\n   * onto the `queue` instead of being auto-started.\n   */\n  ref?: SpringRef<State>\n\n  /** Custom handler for flushing update queues */\n  protected _flush?: ControllerFlushFn<this>\n\n  /** These props are used by all future spring values */\n  protected _initialProps?: Lookup\n\n  /** The counter for tracking `scheduleProps` calls */\n  protected _lastAsyncId = 0\n\n  /** The values currently being animated */\n  protected _active = new Set<FrameValue>()\n\n  /** The values that changed recently */\n  protected _changed = new Set<FrameValue>()\n\n  /** Equals false when `onStart` listeners can be called */\n  protected _started = false\n\n  private _item?: any\n\n  /** State used by the `runAsync` function */\n  protected _state: RunAsyncState<this> = {\n    paused: false,\n    pauseQueue: new Set(),\n    resumeQueue: new Set(),\n    timeouts: new Set(),\n  }\n\n  /** The event queues that are flushed once per frame maximum */\n  protected _events = {\n    onStart: new Map<\n      OnStart<SpringValue<State>, Controller<State>, any>,\n      AnimationResult\n    >(),\n    onChange: new Map<\n      OnChange<SpringValue<State>, Controller<State>, any>,\n      AnimationResult\n    >(),\n    onRest: new Map<\n      OnRest<SpringValue<State>, Controller<State>, any>,\n      AnimationResult\n    >(),\n  }\n\n  constructor(\n    props?: ControllerUpdate<State> | null,\n    flush?: ControllerFlushFn<any>\n  ) {\n    this._onFrame = this._onFrame.bind(this)\n    if (flush) {\n      this._flush = flush\n    }\n    if (props) {\n      this.start({ default: true, ...props })\n    }\n  }\n\n  /**\n   * Equals `true` when no spring values are in the frameloop, and\n   * no async animation is currently active.\n   */\n  get idle() {\n    return (\n      !this._state.asyncTo &&\n      Object.values(this.springs as Lookup<SpringValue>).every(spring => {\n        return spring.idle && !spring.isDelayed && !spring.isPaused\n      })\n    )\n  }\n\n  get item() {\n    return this._item\n  }\n\n  set item(item) {\n    this._item = item\n  }\n\n  /** Get the current values of our springs */\n  get(): State & UnknownProps {\n    const values: any = {}\n    this.each((spring, key) => (values[key] = spring.get()))\n    return values\n  }\n\n  /** Set the current values without animating. */\n  set(values: Partial<State>) {\n    for (const key in values) {\n      const value = values[key]\n      if (!is.und(value)) {\n        this.springs[key].set(value)\n      }\n    }\n  }\n\n  /** Push an update onto the queue of each value. */\n  update(props: ControllerUpdate<State> | Falsy) {\n    if (props) {\n      this.queue.push(createUpdate(props))\n    }\n    return this\n  }\n\n  /**\n   * Start the queued animations for every spring, and resolve the returned\n   * promise once all queued animations have finished or been cancelled.\n   *\n   * When you pass a queue (instead of nothing), that queue is used instead of\n   * the queued animations added with the `update` method, which are left alone.\n   */\n  start(props?: OneOrMore<ControllerUpdate<State>> | null): AsyncResult<this> {\n    let { queue } = this as any\n    if (props) {\n      queue = toArray<any>(props).map(createUpdate)\n    } else {\n      this.queue = []\n    }\n\n    if (this._flush) {\n      return this._flush(this, queue)\n    }\n\n    prepareKeys(this, queue)\n    return flushUpdateQueue(this, queue)\n  }\n\n  /** Stop all animations. */\n  stop(): this\n  /** Stop animations for the given keys. */\n  stop(keys: OneOrMore<string>): this\n  /** Cancel all animations. */\n  stop(cancel: boolean): this\n  /** Cancel animations for the given keys. */\n  stop(cancel: boolean, keys: OneOrMore<string>): this\n  /** Stop some or all animations. */\n  stop(keys?: OneOrMore<string>): this\n  /** Cancel some or all animations. */\n  stop(cancel: boolean, keys?: OneOrMore<string>): this\n  /** @internal */\n  stop(arg?: boolean | OneOrMore<string>, keys?: OneOrMore<string>) {\n    if (arg !== !!arg) {\n      keys = arg as OneOrMore<string>\n    }\n    if (keys) {\n      const springs = this.springs as Lookup<SpringValue>\n      each(toArray(keys) as string[], key => springs[key].stop(!!arg))\n    } else {\n      stopAsync(this._state, this._lastAsyncId)\n      this.each(spring => spring.stop(!!arg))\n    }\n    return this\n  }\n\n  /** Freeze the active animation in time */\n  pause(keys?: OneOrMore<string>) {\n    if (is.und(keys)) {\n      this.start({ pause: true })\n    } else {\n      const springs = this.springs as Lookup<SpringValue>\n      each(toArray(keys) as string[], key => springs[key].pause())\n    }\n    return this\n  }\n\n  /** Resume the animation if paused. */\n  resume(keys?: OneOrMore<string>) {\n    if (is.und(keys)) {\n      this.start({ pause: false })\n    } else {\n      const springs = this.springs as Lookup<SpringValue>\n      each(toArray(keys) as string[], key => springs[key].resume())\n    }\n    return this\n  }\n\n  /** Call a function once per spring value */\n  each(iterator: (spring: SpringValue, key: string) => void) {\n    eachProp(this.springs, iterator as any)\n  }\n\n  /** @internal Called at the end of every animation frame */\n  protected _onFrame() {\n    const { onStart, onChange, onRest } = this._events\n\n    const active = this._active.size > 0\n    const changed = this._changed.size > 0\n\n    if ((active && !this._started) || (changed && !this._started)) {\n      this._started = true\n      flush(onStart, ([onStart, result]) => {\n        result.value = this.get()\n        onStart(result, this, this._item)\n      })\n    }\n\n    const idle = !active && this._started\n    const values = changed || (idle && onRest.size) ? this.get() : null\n\n    if (changed && onChange.size) {\n      flush(onChange, ([onChange, result]) => {\n        result.value = values\n        onChange(result, this, this._item)\n      })\n    }\n\n    // The \"onRest\" queue is only flushed when all springs are idle.\n    if (idle) {\n      this._started = false\n      flush(onRest, ([onRest, result]) => {\n        result.value = values\n        onRest(result, this, this._item)\n      })\n    }\n  }\n\n  /** @internal */\n  eventObserved(event: FrameValue.Event) {\n    if (event.type == 'change') {\n      this._changed.add(event.parent)\n      if (!event.idle) {\n        this._active.add(event.parent)\n      }\n    } else if (event.type == 'idle') {\n      this._active.delete(event.parent)\n    }\n    // The `onFrame` handler runs when a parent is changed or idle.\n    else return\n    raf.onFrame(this._onFrame)\n  }\n}\n\n/**\n * Warning: Props might be mutated.\n */\nexport function flushUpdateQueue(\n  ctrl: Controller<any>,\n  queue: ControllerQueue\n) {\n  return Promise.all(queue.map(props => flushUpdate(ctrl, props))).then(\n    results => getCombinedResult(ctrl, results)\n  )\n}\n\n/**\n * Warning: Props might be mutated.\n *\n * Process a single set of props using the given controller.\n *\n * The returned promise resolves to `true` once the update is\n * applied and any animations it starts are finished without being\n * stopped or cancelled.\n */\nexport async function flushUpdate(\n  ctrl: Controller<any>,\n  props: ControllerQueue[number],\n  isLoop?: boolean\n): AsyncResult {\n  const { keys, to, from, loop, onRest, onResolve } = props\n  const defaults = is.obj(props.default) && props.default\n\n  // Looping must be handled in this function, or else the values\n  // would end up looping out-of-sync in many common cases.\n  if (loop) {\n    props.loop = false\n  }\n\n  // Treat false like null, which gets ignored.\n  if (to === false) props.to = null\n  if (from === false) props.from = null\n\n  const asyncTo = is.arr(to) || is.fun(to) ? to : undefined\n  if (asyncTo) {\n    props.to = undefined\n    props.onRest = undefined\n    if (defaults) {\n      defaults.onRest = undefined\n    }\n  }\n  // For certain events, use batching to prevent multiple calls per frame.\n  // However, batching is avoided when the `to` prop is async, because any\n  // event props are used as default props instead.\n  else {\n    each(BATCHED_EVENTS, key => {\n      const handler: any = props[key]\n      if (is.fun(handler)) {\n        const queue = ctrl['_events'][key]\n        props[key] = (({ finished, cancelled }: AnimationResult) => {\n          const result = queue.get(handler)\n          if (result) {\n            if (!finished) result.finished = false\n            if (cancelled) result.cancelled = true\n          } else {\n            // The \"value\" is set before the \"handler\" is called.\n            queue.set(handler, {\n              value: null,\n              finished: finished || false,\n              cancelled: cancelled || false,\n            })\n          }\n        }) as any\n\n        // Avoid using a batched `handler` as a default prop.\n        if (defaults) {\n          defaults[key] = props[key] as any\n        }\n      }\n    })\n  }\n\n  const state = ctrl['_state']\n\n  // Pause/resume the `asyncTo` when `props.pause` is true/false.\n  if (props.pause === !state.paused) {\n    state.paused = props.pause\n    flushCalls(props.pause ? state.pauseQueue : state.resumeQueue)\n  }\n  // When a controller is paused, its values are also paused.\n  else if (state.paused) {\n    props.pause = true\n  }\n\n  const promises: AsyncResult[] = (keys || Object.keys(ctrl.springs)).map(key =>\n    ctrl.springs[key]!.start(props as any)\n  )\n\n  const cancel =\n    props.cancel === true || getDefaultProp(props, 'cancel') === true\n\n  if (asyncTo || (cancel && state.asyncId)) {\n    promises.push(\n      scheduleProps(++ctrl['_lastAsyncId'], {\n        props,\n        state,\n        actions: {\n          pause: noop,\n          resume: noop,\n          start(props, resolve) {\n            if (cancel) {\n              stopAsync(state, ctrl['_lastAsyncId'])\n              resolve(getCancelledResult(ctrl))\n            } else {\n              props.onRest = onRest\n              resolve(\n                runAsync(\n                  asyncTo as SpringChain | SpringToFn,\n                  props,\n                  state,\n                  ctrl\n                )\n              )\n            }\n          },\n        },\n      })\n    )\n  }\n\n  // Pause after updating each spring, so they can be resumed separately\n  // and so their default `pause` and `cancel` props are updated.\n  if (state.paused) {\n    // Ensure `this` must be resumed before the returned promise\n    // is resolved and before starting the next `loop` repetition.\n    await new Promise<void>(resume => {\n      state.resumeQueue.add(resume)\n    })\n  }\n\n  const result = getCombinedResult<any>(ctrl, await Promise.all(promises))\n  if (loop && result.finished && !(isLoop && result.noop)) {\n    const nextProps = createLoopUpdate(props, loop, to)\n    if (nextProps) {\n      prepareKeys(ctrl, [nextProps])\n      return flushUpdate(ctrl, nextProps, true)\n    }\n  }\n  if (onResolve) {\n    raf.batchedUpdates(() => onResolve(result, ctrl, ctrl.item))\n  }\n  return result\n}\n\n/**\n * From an array of updates, get the map of `SpringValue` objects\n * by their keys. Springs are created when any update wants to\n * animate a new key.\n *\n * Springs created by `getSprings` are neither cached nor observed\n * until they're given to `setSprings`.\n */\nexport function getSprings<State extends Lookup>(\n  ctrl: Controller<Lookup<any>>,\n  props?: OneOrMore<ControllerUpdate<State>>\n) {\n  const springs = { ...ctrl.springs }\n  if (props) {\n    each(toArray(props), (props: any) => {\n      if (is.und(props.keys)) {\n        props = createUpdate(props)\n      }\n      if (!is.obj(props.to)) {\n        // Avoid passing array/function to each spring.\n        props = { ...props, to: undefined }\n      }\n      prepareSprings(springs as any, props, key => {\n        return createSpring(key)\n      })\n    })\n  }\n  setSprings(ctrl, springs)\n  return springs\n}\n\n/**\n * Tell a controller to manage the given `SpringValue` objects\n * whose key is not already in use.\n */\nexport function setSprings(\n  ctrl: Controller<Lookup<any>>,\n  springs: SpringValues<UnknownProps>\n) {\n  eachProp(springs, (spring, key) => {\n    if (!ctrl.springs[key]) {\n      ctrl.springs[key] = spring\n      addFluidObserver(spring, ctrl)\n    }\n  })\n}\n\nfunction createSpring(key: string, observer?: FluidObserver<FrameValue.Event>) {\n  const spring = new SpringValue()\n  spring.key = key\n  if (observer) {\n    addFluidObserver(spring, observer)\n  }\n  return spring\n}\n\n/**\n * Ensure spring objects exist for each defined key.\n *\n * Using the `props`, the `Animated` node of each `SpringValue` may\n * be created or updated.\n */\nfunction prepareSprings(\n  springs: SpringValues,\n  props: ControllerQueue[number],\n  create: (key: string) => SpringValue\n) {\n  if (props.keys) {\n    each(props.keys, key => {\n      const spring = springs[key] || (springs[key] = create(key))\n      spring['_prepareNode'](props)\n    })\n  }\n}\n\n/**\n * Ensure spring objects exist for each defined key, and attach the\n * `ctrl` to them for observation.\n *\n * The queue is expected to contain `createUpdate` results.\n */\nfunction prepareKeys(ctrl: Controller<any>, queue: ControllerQueue[number][]) {\n  each(queue, props => {\n    prepareSprings(ctrl.springs, props, key => {\n      return createSpring(key, ctrl)\n    })\n  })\n}\n", "import * as React from 'react'\nimport { useContext, PropsWithChildren } from 'react'\nimport { useMemoOne } from '@react-spring/shared'\n\n/**\n * This context affects all new and existing `SpringValue` objects\n * created with the hook API or the renderprops API.\n */\nexport interface SpringContext {\n  /** Pause all new and existing animations. */\n  pause?: boolean\n  /** Force all new and existing animations to be immediate. */\n  immediate?: boolean\n}\n\nexport const SpringContext = ({\n  children,\n  ...props\n}: PropsWithChildren<SpringContext>) => {\n  const inherited = useContext(ctx)\n\n  // Inherited values are dominant when truthy.\n  const pause = props.pause || !!inherited.pause,\n    immediate = props.immediate || !!inherited.immediate\n\n  // Memoize the context to avoid unwanted renders.\n  props = useMemoOne(() => ({ pause, immediate }), [pause, immediate])\n\n  const { Provider } = ctx\n  return <Provider value={props}>{children}</Provider>\n}\n\nconst ctx = makeContext(SpringContext, {} as SpringContext)\n\n// Allow `useContext(SpringContext)` in TypeScript.\nSpringContext.Provider = ctx.Provider\nSpringContext.Consumer = ctx.Consumer\n\n/** Make the `target` compatible with `useContext` */\nfunction makeContext<T>(target: any, init: T): React.Context<T> {\n  Object.assign(target, React.createContext(init))\n  target.Provider._context = target\n  target.Consumer._context = target\n  return target\n}\n", "import { each, is, deprecateDirect<PERSON>all } from '@react-spring/shared'\nimport { Lookup, Falsy, OneOrMore } from '@react-spring/types'\nimport { AsyncResult, ControllerUpdate } from './types'\nimport { Controller } from './Controller'\n\nexport interface ControllerUpdateFn<State extends Lookup = Lookup> {\n  (i: number, ctrl: Controller<State>): ControllerUpdate<State> | Falsy\n}\n\nexport interface SpringRef<State extends Lookup = Lookup> {\n  (\n    props?: ControllerUpdate<State> | ControllerUpdateFn<State>\n  ): AsyncResult<Controller<State>>[]\n  current: Controller<State>[]\n\n  /** Add a controller to this ref */\n  add(ctrl: Controller<State>): void\n\n  /** Remove a controller from this ref */\n  delete(ctrl: Controller<State>): void\n\n  /** Pause all animations. */\n  pause(): this\n  /** Pause animations for the given keys. */\n  pause(keys: OneOrMore<string>): this\n  /** Pause some or all animations. */\n  pause(keys?: OneOrMore<string>): this\n\n  /** Resume all animations. */\n  resume(): this\n  /** Resume animations for the given keys. */\n  resume(keys: OneOrMore<string>): this\n  /** Resume some or all animations. */\n  resume(keys?: OneOrMore<string>): this\n\n  /** Update the state of each controller without animating. */\n  set(values: Partial<State>): void\n  /** Update the state of each controller without animating based on their passed state. */\n  set(values: (index: number, ctrl: Controller<State>) => Partial<State>): void\n\n  /** Start the queued animations of each controller. */\n  start(): AsyncResult<Controller<State>>[]\n  /** Update every controller with the same props. */\n  start(props: ControllerUpdate<State>): AsyncResult<Controller<State>>[]\n  /** Update controllers based on their state. */\n  start(props: ControllerUpdateFn<State>): AsyncResult<Controller<State>>[]\n  /** Start animating each controller. */\n  start(\n    props?: ControllerUpdate<State> | ControllerUpdateFn<State>\n  ): AsyncResult<Controller<State>>[]\n\n  /** Stop all animations. */\n  stop(): this\n  /** Stop animations for the given keys. */\n  stop(keys: OneOrMore<string>): this\n  /** Cancel all animations. */\n  stop(cancel: boolean): this\n  /** Cancel animations for the given keys. */\n  stop(cancel: boolean, keys: OneOrMore<string>): this\n  /** Stop some or all animations. */\n  stop(keys?: OneOrMore<string>): this\n  /** Cancel some or all animations. */\n  stop(cancel: boolean, keys?: OneOrMore<string>): this\n\n  /** Add the same props to each controller's update queue. */\n  update(props: ControllerUpdate<State>): this\n  /** Generate separate props for each controller's update queue. */\n  update(props: ControllerUpdateFn<State>): this\n  /** Add props to each controller's update queue. */\n  update(props: ControllerUpdate<State> | ControllerUpdateFn<State>): this\n\n  _getProps(\n    arg: ControllerUpdate<State> | ControllerUpdateFn<State>,\n    ctrl: Controller<State>,\n    index: number\n  ): ControllerUpdate<State> | Falsy\n}\n\nexport const SpringRef = <\n  State extends Lookup = Lookup,\n>(): SpringRef<State> => {\n  const current: Controller<State>[] = []\n\n  const SpringRef: SpringRef<State> = function (props) {\n    deprecateDirectCall()\n\n    const results: AsyncResult[] = []\n\n    each(current, (ctrl, i) => {\n      if (is.und(props)) {\n        results.push(ctrl.start())\n      } else {\n        const update = _getProps(props, ctrl, i)\n        if (update) {\n          results.push(ctrl.start(update))\n        }\n      }\n    })\n\n    return results\n  }\n\n  SpringRef.current = current\n\n  /** Add a controller to this ref */\n  SpringRef.add = function (ctrl: Controller<State>) {\n    if (!current.includes(ctrl)) {\n      current.push(ctrl)\n    }\n  }\n\n  /** Remove a controller from this ref */\n  SpringRef.delete = function (ctrl: Controller<State>) {\n    const i = current.indexOf(ctrl)\n    if (~i) current.splice(i, 1)\n  }\n\n  /** Pause all animations. */\n  SpringRef.pause = function () {\n    each(current, ctrl => ctrl.pause(...arguments))\n    return this\n  }\n\n  /** Resume all animations. */\n  SpringRef.resume = function () {\n    each(current, ctrl => ctrl.resume(...arguments))\n    return this\n  }\n\n  /** Update the state of each controller without animating. */\n  SpringRef.set = function (\n    values:\n      | Partial<State>\n      | ((i: number, ctrl: Controller<State>) => Partial<State>)\n  ) {\n    each(current, (ctrl, i) => {\n      const update = is.fun(values) ? values(i, ctrl) : values\n      if (update) {\n        ctrl.set(update)\n      }\n    })\n  }\n\n  SpringRef.start = function (props?: object | ControllerUpdateFn<State>) {\n    const results: AsyncResult[] = []\n\n    each(current, (ctrl, i) => {\n      if (is.und(props)) {\n        results.push(ctrl.start())\n      } else {\n        const update = this._getProps(props, ctrl, i)\n        if (update) {\n          results.push(ctrl.start(update))\n        }\n      }\n    })\n\n    return results\n  }\n\n  /** Stop all animations. */\n  SpringRef.stop = function () {\n    each(current, ctrl => ctrl.stop(...arguments))\n    return this\n  }\n\n  SpringRef.update = function (props: object | ControllerUpdateFn<State>) {\n    each(current, (ctrl, i) => ctrl.update(this._getProps(props, ctrl, i)))\n    return this\n  }\n\n  /** Overridden by `useTrail` to manipulate props */\n  const _getProps = function (\n    arg: ControllerUpdate<State> | ControllerUpdateFn<State>,\n    ctrl: Controller<State>,\n    index: number\n  ) {\n    return is.fun(arg) ? arg(index, ctrl) : arg\n  }\n\n  SpringRef._getProps = _getProps\n\n  return SpringRef\n}\n", "import { useState } from 'react'\nimport { Lookup } from '@react-spring/types'\nimport { SpringRef } from '../SpringRef'\nimport type { SpringRef as SpringRefType } from '../SpringRef'\n\nconst initSpringRef = () => SpringRef<any>()\n\nexport const useSpringRef = <State extends Lookup = Lookup>() =>\n  useState(initSpringRef)[0] as SpringRefType<State>\n", "import { useConstant, useOnce } from '@react-spring/shared'\n\nimport { SpringValue } from '../SpringValue'\nimport { SpringUpdate } from '../types'\n\n/**\n * Creates a constant single `SpringValue` that can be interacted\n * with imperatively. This is an advanced API and does not react\n * to updates from the parent component e.g. passing a new initial value\n *\n *\n * ```jsx\n * export const MyComponent = () => {\n *   const opacity = useSpringValue(1)\n *\n *   return <animated.div style={{ opacity }} />\n * }\n * ```\n *\n * @param initial – The initial value of the `SpringValue`.\n * @param props – Typically the same props as `useSpring` e.g. `config`, `loop` etc.\n *\n * @public\n */\nexport const useSpringValue = <T>(\n  initial: Exclude<T, object>,\n  props?: SpringUpdate<T>\n) => {\n  const springValue = useConstant(() => new SpringValue(initial, props))\n\n  useOnce(() => () => {\n    springValue.stop()\n  })\n\n  return springValue\n}\n", "import { each, is, useIsomorphicLayoutEffect } from '@react-spring/shared'\nimport { Lookup } from '@react-spring/types'\n\nimport { Valid } from '../types/common'\nimport { PickAnimated, SpringValues } from '../types'\n\nimport { SpringRef } from '../SpringRef'\nimport { Controller } from '../Controller'\n\nimport { UseSpringProps } from './useSpring'\nimport { useSprings } from './useSprings'\nimport { replaceRef } from '../helpers'\n\nexport type UseTrailProps<Props extends object = any> = UseSpringProps<Props>\n\nexport function useTrail<Props extends object>(\n  length: number,\n  props: (\n    i: number,\n    ctrl: Controller\n  ) => UseTrailProps | (Props & Valid<Props, UseTrailProps<Props>>),\n  deps?: readonly any[]\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup<any>\n    ? [SpringValues<State>[], SpringRef<State>]\n    : never\n  : never\n\n/**\n * This hook is an abstraction around `useSprings` and is designed to\n * automatically orchestrate the springs to stagger one after the other\n *\n * ```jsx\n * export const MyComponent = () => {\n *  const trails = useTrail(3, {opacity: 0})\n *\n *  return trails.map(styles => <animated.div style={styles} />)\n * }\n * ```\n *\n * @param length – The number of springs you want to create\n * @param propsArg – The props to pass to the internal `useSprings` hook,\n * therefore is the same as `useSprings`.\n *\n * @public\n */\nexport function useTrail<Props extends object>(\n  length: number,\n  props: UseTrailProps | (Props & Valid<Props, UseTrailProps<Props>>)\n): SpringValues<PickAnimated<Props>>[]\n\n/**\n * This hook is an abstraction around `useSprings` and is designed to\n * automatically orchestrate the springs to stagger one after the other\n *\n * ```jsx\n * export const MyComponent = () => {\n *  const trails = useTrail(3, {opacity: 0}, [])\n *\n *  return trails.map(styles => <animated.div style={styles} />)\n * }\n * ```\n *\n * @param length – The number of springs you want to create\n * @param propsArg – The props to pass to the internal `useSprings` hook,\n * therefore is the same as `useSprings`.\n * @param deps – The optional array of dependencies to pass to the internal\n * `useSprings` hook, therefore is the same as `useSprings`.\n *\n * @public\n */\nexport function useTrail<Props extends object>(\n  length: number,\n  props: UseTrailProps | (Props & Valid<Props, UseTrailProps<Props>>),\n  deps: readonly any[]\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup<any>\n    ? [SpringValues<State>[], SpringRef<State>]\n    : never\n  : never\n\nexport function useTrail(\n  length: number,\n  propsArg: unknown,\n  deps?: readonly any[]\n) {\n  const propsFn = is.fun(propsArg) && propsArg\n  if (propsFn && !deps) deps = []\n\n  // The trail is reversed when every render-based update is reversed.\n  let reverse = true\n  let passedRef: SpringRef | undefined = undefined\n\n  const result = useSprings(\n    length,\n    (i, ctrl) => {\n      const props = propsFn ? propsFn(i, ctrl) : propsArg\n      passedRef = props.ref\n      reverse = reverse && props.reverse\n\n      return props\n    },\n    // Ensure the props function is called when no deps exist.\n    // This works around the 3 argument rule.\n    deps || [{}]\n  )\n\n  useIsomorphicLayoutEffect(() => {\n    /**\n     * Run through the ref passed by the `useSprings` hook.\n     */\n    each(result[1].current, (ctrl, i) => {\n      const parent = result[1].current[i + (reverse ? 1 : -1)]\n\n      /**\n       * If there's a passed ref then we replace the ctrl ref with it\n       */\n      replaceRef(ctrl, passedRef)\n\n      /**\n       * And if there's a ctrl ref then we update instead of start\n       * which means nothing is fired until the start method\n       * of said passedRef is called.\n       */\n      if (ctrl.ref) {\n        if (parent) {\n          ctrl.update({ to: parent.springs })\n        }\n\n        return\n      }\n\n      if (parent) {\n        ctrl.start({ to: parent.springs })\n      } else {\n        ctrl.start()\n      }\n    })\n  }, deps)\n\n  if (propsFn || arguments.length == 3) {\n    const ref = passedRef ?? result[1]\n\n    ref['_getProps'] = (propsArg, ctrl, i) => {\n      const props = is.fun(propsArg) ? propsArg(i, ctrl) : propsArg\n      if (props) {\n        const parent = ref.current[i + (props.reverse ? 1 : -1)]\n        if (parent) props.to = parent.springs\n        return props\n      }\n    }\n    return result\n  }\n\n  return result[0]\n}\n", "import * as React from 'react'\nimport { useContext, useRef, useMemo } from 'react'\nimport { Lookup, OneOrMore, UnknownProps } from '@react-spring/types'\nimport {\n  is,\n  toArray,\n  useForceUpdate,\n  useOnce,\n  usePrev,\n  each,\n  useIsomorphicLayoutEffect,\n} from '@react-spring/shared'\n\nimport {\n  Change,\n  ControllerUpdate,\n  ItemKeys,\n  PickAnimated,\n  TransitionFn,\n  TransitionState,\n  TransitionTo,\n  UseTransitionProps,\n} from '../types'\nimport { Valid } from '../types/common'\nimport {\n  callProp,\n  detachRefs,\n  getDefaultProps,\n  hasProps,\n  inferTo,\n  replaceRef,\n} from '../helpers'\nimport { Controller, getSprings } from '../Controller'\nimport { SpringContext } from '../SpringContext'\nimport { SpringRef } from '../SpringRef'\nimport type { SpringRef as SpringRefType } from '../SpringRef'\nimport { TransitionPhase } from '../TransitionPhase'\n\ndeclare function setTimeout(handler: Function, timeout?: number): number\ndeclare function clearTimeout(timeoutId: number): void\n\nexport function useTransition<Item, Props extends object>(\n  data: OneOrMore<Item>,\n  props: () =>\n    | UseTransitionProps<Item>\n    | (Props & Valid<Props, UseTransitionProps<Item>>),\n  deps?: any[]\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup\n    ? [TransitionFn<Item, PickAnimated<Props>>, SpringRefType<State>]\n    : never\n  : never\n\nexport function useTransition<Item, Props extends object>(\n  data: OneOrMore<Item>,\n  props:\n    | UseTransitionProps<Item>\n    | (Props & Valid<Props, UseTransitionProps<Item>>)\n): TransitionFn<Item, PickAnimated<Props>>\n\nexport function useTransition<Item, Props extends object>(\n  data: OneOrMore<Item>,\n  props:\n    | UseTransitionProps<Item>\n    | (Props & Valid<Props, UseTransitionProps<Item>>),\n  deps: any[] | undefined\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup\n    ? [TransitionFn<Item, State>, SpringRefType<State>]\n    : never\n  : never\n\nexport function useTransition(\n  data: unknown,\n  props: UseTransitionProps | (() => any),\n  deps?: any[]\n): any {\n  const propsFn = is.fun(props) && props\n\n  const {\n    reset,\n    sort,\n    trail = 0,\n    expires = true,\n    exitBeforeEnter = false,\n    onDestroyed,\n    ref: propsRef,\n    config: propsConfig,\n  }: UseTransitionProps<any> = propsFn ? propsFn() : props\n\n  // Return a `SpringRef` if a deps array was passed.\n  const ref = useMemo(\n    () => (propsFn || arguments.length == 3 ? SpringRef() : void 0),\n    []\n  )\n\n  // Every item has its own transition.\n  const items = toArray(data)\n  const transitions: TransitionState[] = []\n\n  // The \"onRest\" callbacks need a ref to the latest transitions.\n  const usedTransitions = useRef<TransitionState[] | null>(null)\n  const prevTransitions = reset ? null : usedTransitions.current\n\n  useIsomorphicLayoutEffect(() => {\n    usedTransitions.current = transitions\n  })\n\n  useOnce(() => {\n    /**\n     * If transitions exist on mount of the component\n     * then reattach their refs on-mount, this was required\n     * for react18 strict mode to work properly.\n     *\n     * See https://github.com/pmndrs/react-spring/issues/1890\n     */\n\n    each(transitions, t => {\n      ref?.add(t.ctrl)\n      t.ctrl.ref = ref\n    })\n\n    // Destroy all transitions on dismount.\n    return () => {\n      each(usedTransitions.current!, t => {\n        if (t.expired) {\n          clearTimeout(t.expirationId!)\n        }\n        detachRefs(t.ctrl, ref)\n        t.ctrl.stop(true)\n      })\n    }\n  })\n\n  // Keys help with reusing transitions between renders.\n  // The `key` prop can be undefined (which means the items themselves are used\n  // as keys), or a function (which maps each item to its key), or an array of\n  // keys (which are assigned to each item by index).\n  const keys = getKeys(items, propsFn ? propsFn() : props, prevTransitions)\n\n  // Expired transitions that need clean up.\n  const expired = (reset && usedTransitions.current) || []\n  useIsomorphicLayoutEffect(() =>\n    each(expired, ({ ctrl, item, key }) => {\n      detachRefs(ctrl, ref)\n      callProp(onDestroyed, item, key)\n    })\n  )\n\n  // Map old indices to new indices.\n  const reused: number[] = []\n  if (prevTransitions)\n    each(prevTransitions, (t, i) => {\n      // Expired transitions are not rendered.\n      if (t.expired) {\n        clearTimeout(t.expirationId!)\n        expired.push(t)\n      } else {\n        i = reused[i] = keys.indexOf(t.key)\n        if (~i) transitions[i] = t\n      }\n    })\n\n  // Mount new items with fresh transitions.\n  each(items, (item, i) => {\n    if (!transitions[i]) {\n      transitions[i] = {\n        key: keys[i],\n        item,\n        phase: TransitionPhase.MOUNT,\n        ctrl: new Controller(),\n      }\n\n      transitions[i].ctrl.item = item\n    }\n  })\n\n  // Update the item of any transition whose key still exists,\n  // and ensure leaving transitions are rendered until they finish.\n  if (reused.length) {\n    let i = -1\n    const { leave }: UseTransitionProps<any> = propsFn ? propsFn() : props\n    each(reused, (keyIndex, prevIndex) => {\n      const t = prevTransitions![prevIndex]\n      if (~keyIndex) {\n        i = transitions.indexOf(t)\n        transitions[i] = { ...t, item: items[keyIndex] }\n      } else if (leave) {\n        transitions.splice(++i, 0, t)\n      }\n    })\n  }\n\n  if (is.fun(sort)) {\n    transitions.sort((a, b) => sort(a.item, b.item))\n  }\n\n  // Track cumulative delay for the \"trail\" prop.\n  let delay = -trail\n\n  // Expired transitions use this to dismount.\n  const forceUpdate = useForceUpdate()\n\n  // These props are inherited by every phase change.\n  const defaultProps = getDefaultProps<UseTransitionProps>(props)\n  // Generate changes to apply in useEffect.\n  const changes = new Map<TransitionState, Change>()\n  const exitingTransitions = useRef(new Map<TransitionState, Change>())\n\n  const forceChange = useRef(false)\n  each(transitions, (t, i) => {\n    const key = t.key\n    const prevPhase = t.phase\n\n    const p: UseTransitionProps<any> = propsFn ? propsFn() : props\n\n    let to: TransitionTo<any>\n    let phase: TransitionPhase\n\n    const propsDelay = callProp(p.delay || 0, key)\n\n    if (prevPhase == TransitionPhase.MOUNT) {\n      to = p.enter\n      phase = TransitionPhase.ENTER\n    } else {\n      const isLeave = keys.indexOf(key) < 0\n      if (prevPhase != TransitionPhase.LEAVE) {\n        if (isLeave) {\n          to = p.leave\n          phase = TransitionPhase.LEAVE\n        } else if ((to = p.update)) {\n          phase = TransitionPhase.UPDATE\n        } else return\n      } else if (!isLeave) {\n        to = p.enter\n        phase = TransitionPhase.ENTER\n      } else return\n    }\n\n    // When \"to\" is a function, it can return (1) an array of \"useSpring\" props,\n    // (2) an async function, or (3) an object with any \"useSpring\" props.\n    to = callProp(to, t.item, i)\n    to = is.obj(to) ? inferTo(to) : { to }\n\n    /**\n     * This would allow us to give different delays for phases.\n     * If we were to do this, we'd have to suffle the prop\n     * spreading below to set delay last.\n     * But if we were going to do that, we should consider letting\n     * the prop trail also be part of a phase.\n     */\n    // if (to.delay) {\n    //   phaseDelay = callProp(to.delay, key)\n    // }\n\n    if (!to.config) {\n      const config = propsConfig || defaultProps.config\n      to.config = callProp(config, t.item, i, phase)\n    }\n\n    delay += trail\n\n    // The payload is used to update the spring props once the current render is committed.\n    const payload: ControllerUpdate<UnknownProps> = {\n      ...defaultProps,\n      // we need to add our props.delay value you here.\n      delay: propsDelay + delay,\n      ref: propsRef,\n      immediate: p.immediate,\n      // This prevents implied resets.\n      reset: false,\n      // Merge any phase-specific props.\n      ...(to as any),\n    }\n\n    if (phase == TransitionPhase.ENTER && is.und(payload.from)) {\n      const p = propsFn ? propsFn() : props\n      // The `initial` prop is used on the first render of our parent component,\n      // as well as when `reset: true` is passed. It overrides the `from` prop\n      // when defined, and it makes `enter` instant when null.\n      const from = is.und(p.initial) || prevTransitions ? p.from : p.initial\n\n      payload.from = callProp(from, t.item, i)\n    }\n\n    const { onResolve } = payload\n    payload.onResolve = result => {\n      callProp(onResolve, result)\n\n      const transitions = usedTransitions.current!\n      const t = transitions.find(t => t.key === key)\n      if (!t) return\n\n      // Reset the phase of a cancelled enter/leave transition, so it can\n      // retry the animation on the next render.\n      if (result.cancelled && t.phase != TransitionPhase.UPDATE) {\n        /**\n         * @legacy Reset the phase of a cancelled enter/leave transition, so it can\n         * retry the animation on the next render.\n         *\n         * Note: leaving this here made the transitioned item respawn.\n         */\n        // t.phase = prevPhase\n        return\n      }\n\n      if (t.ctrl.idle) {\n        const idle = transitions.every(t => t.ctrl.idle)\n        if (t.phase == TransitionPhase.LEAVE) {\n          const expiry = callProp(expires, t.item)\n          if (expiry !== false) {\n            const expiryMs = expiry === true ? 0 : expiry\n            t.expired = true\n\n            // Force update once the expiration delay ends.\n            if (!idle && expiryMs > 0) {\n              // The maximum timeout is 2^31-1\n              if (expiryMs <= 0x7fffffff)\n                t.expirationId = setTimeout(forceUpdate, expiryMs)\n              return\n            }\n          }\n        }\n        // Force update once idle and expired items exist.\n        if (idle && transitions.some(t => t.expired)) {\n          /**\n           * Remove the exited transition from the list\n           * this may not exist but we'll try anyway.\n           */\n          exitingTransitions.current.delete(t)\n\n          if (exitBeforeEnter) {\n            /**\n             * If we have exitBeforeEnter == true\n             * we need to force the animation to start\n             */\n            forceChange.current = true\n          }\n\n          forceUpdate()\n        }\n      }\n    }\n\n    const springs = getSprings(t.ctrl, payload)\n\n    /**\n     * Make a separate map for the exiting changes and \"regular\" changes\n     */\n    if (phase === TransitionPhase.LEAVE && exitBeforeEnter) {\n      exitingTransitions.current.set(t, { phase, springs, payload })\n    } else {\n      changes.set(t, { phase, springs, payload })\n    }\n  })\n\n  // The prop overrides from an ancestor.\n  const context = useContext(SpringContext)\n  const prevContext = usePrev(context)\n  const hasContext = context !== prevContext && hasProps(context)\n\n  // Merge the context into each transition.\n  useIsomorphicLayoutEffect(() => {\n    if (hasContext) {\n      each(transitions, t => {\n        t.ctrl.start({ default: context })\n      })\n    }\n  }, [context])\n\n  each(changes, (_, t) => {\n    /**\n     * If we have children to exit because exitBeforeEnter is\n     * set to true, we remove the transitions so they go to back\n     * to their initial state.\n     */\n    if (exitingTransitions.current.size) {\n      const ind = transitions.findIndex(state => state.key === t.key)\n      transitions.splice(ind, 1)\n    }\n  })\n\n  useIsomorphicLayoutEffect(\n    () => {\n      /*\n       * if exitingTransitions.current has a size it means we're exiting before enter\n       * so we want to map through those and fire those first.\n       */\n      each(\n        exitingTransitions.current.size ? exitingTransitions.current : changes,\n        ({ phase, payload }, t) => {\n          const { ctrl } = t\n\n          t.phase = phase\n\n          // Attach the controller to our local ref.\n          ref?.add(ctrl)\n\n          // Merge the context into new items.\n          if (hasContext && phase == TransitionPhase.ENTER) {\n            ctrl.start({ default: context })\n          }\n\n          if (payload) {\n            // Update the injected ref if needed.\n            replaceRef(ctrl, payload.ref)\n\n            /**\n             * When an injected ref exists, the update is postponed\n             * until the ref has its `start` method called.\n             * Unless we have exitBeforeEnter in which case will skip\n             * to enter the new animation straight away as if they \"overlapped\"\n             */\n            if ((ctrl.ref || ref) && !forceChange.current) {\n              ctrl.update(payload)\n            } else {\n              ctrl.start(payload)\n\n              if (forceChange.current) {\n                forceChange.current = false\n              }\n            }\n          }\n        }\n      )\n    },\n    reset ? void 0 : deps\n  )\n\n  const renderTransitions: TransitionFn = render => (\n    <>\n      {transitions.map((t, i) => {\n        const { springs } = changes.get(t) || t.ctrl\n        const elem: any = render({ ...springs }, t.item, t, i)\n        return elem && elem.type ? (\n          <elem.type\n            {...elem.props}\n            key={is.str(t.key) || is.num(t.key) ? t.key : t.ctrl.id}\n            ref={elem.ref}\n          />\n        ) : (\n          elem\n        )\n      })}\n    </>\n  )\n\n  return ref ? [renderTransitions, ref] : renderTransitions\n}\n\n/** Local state for auto-generated item keys */\nlet nextKey = 1\n\nfunction getKeys(\n  items: readonly any[],\n  { key, keys = key }: { key?: ItemKeys; keys?: ItemKeys },\n  prevTransitions: TransitionState[] | null\n): readonly any[] {\n  if (keys === null) {\n    const reused = new Set()\n    return items.map(item => {\n      const t =\n        prevTransitions &&\n        prevTransitions.find(\n          t =>\n            t.item === item &&\n            t.phase !== TransitionPhase.LEAVE &&\n            !reused.has(t)\n        )\n      if (t) {\n        reused.add(t)\n        return t.key\n      }\n      return nextKey++\n    })\n  }\n  return is.und(keys) ? items : is.fun(keys) ? items.map(keys) : toArray(keys)\n}\n", "import { MutableRefObject } from 'react'\nimport { each, onScroll, useIsomorphicLayoutEffect } from '@react-spring/shared'\n\nimport { SpringProps, SpringValues } from '../types'\n\nimport { useSpring } from './useSpring'\n\nexport interface UseScrollOptions extends Omit<SpringProps, 'to' | 'from'> {\n  container?: MutableRefObject<HTMLElement>\n}\n\n/**\n * A small utility abstraction around our signature useSpring hook. It's a great way to create\n * a scroll-linked animation. With either the raw value of distance or a 0-1 progress value.\n * You can either use the scroll values of the whole document, or just a specific element.\n *\n * \n ```jsx\n    import { useScroll, animated } from '@react-spring/web'\n\n    function MyComponent() {\n      const { scrollYProgress } = useScroll()\n\n      return (\n        <animated.div style={{ opacity: scrollYProgress }}>\n          Hello World\n        </animated.div>\n      )\n    }\n  ```\n * \n * @param {UseScrollOptions} useScrollOptions options for the useScroll hook.\n * @param {MutableRefObject<HTMLElement>} useScrollOptions.container the container to listen to scroll events on, defaults to the window.\n *\n * @returns {SpringValues<{scrollX: number; scrollY: number; scrollXProgress: number; scrollYProgress: number}>} SpringValues the collection of values returned from the inner hook\n */\nexport const useScroll = ({\n  container,\n  ...springOptions\n}: UseScrollOptions = {}): SpringValues<{\n  scrollX: number\n  scrollY: number\n  scrollXProgress: number\n  scrollYProgress: number\n}> => {\n  const [scrollValues, api] = useSpring(\n    () => ({\n      scrollX: 0,\n      scrollY: 0,\n      scrollXProgress: 0,\n      scrollYProgress: 0,\n      ...springOptions,\n    }),\n    []\n  )\n\n  useIsomorphicLayoutEffect(() => {\n    const cleanupScroll = onScroll(\n      ({ x, y }) => {\n        api.start({\n          scrollX: x.current,\n          scrollXProgress: x.progress,\n          scrollY: y.current,\n          scrollYProgress: y.progress,\n        })\n      },\n      { container: container?.current || undefined }\n    )\n\n    return () => {\n      /**\n       * Stop the springs on unmount.\n       */\n      each(Object.values(scrollValues), value => value.stop())\n\n      cleanupScroll()\n    }\n  }, [])\n\n  return scrollValues\n}\n", "import { MutableRefObject } from 'react'\nimport { onResize, each, useIsomorphicLayoutEffect } from '@react-spring/shared'\n\nimport { SpringProps, SpringValues } from '../types'\n\nimport { useSpring } from './useSpring'\n\nexport interface UseResizeOptions extends Omit<SpringProps, 'to' | 'from'> {\n  container?: MutableRefObject<HTMLElement | null | undefined>\n}\n\n/**\n * A small abstraction around the `useSpring` hook. It returns a `SpringValues` \n * object with the `width` and `height` of the element it's attached to & doesn't \n * necessarily have to be attached to the window, by passing a `container` you \n * can observe that element's size instead.\n * \n ```jsx\n    import { useResize, animated } from '@react-spring/web'\n\n    function MyComponent() {\n      const { width } = useResize()\n\n      return (\n        <animated.div style={{ width }}>\n          Hello World\n        </animated.div>\n      )\n    }\n  ```\n * \n * @param {UseResizeOptions} UseResizeOptions options for the useScroll hook.\n * @param {MutableRefObject<HTMLElement>} UseResizeOptions.container the container to listen to scroll events on, defaults to the window.\n *\n * @returns {SpringValues<{width: number; height: number;}>} SpringValues the collection of values returned from the inner hook\n */\nexport const useResize = ({\n  container,\n  ...springOptions\n}: UseResizeOptions): SpringValues<{\n  width: number\n  height: number\n}> => {\n  const [sizeValues, api] = useSpring(\n    () => ({\n      width: 0,\n      height: 0,\n      ...springOptions,\n    }),\n    []\n  )\n\n  useIsomorphicLayoutEffect(() => {\n    const cleanupScroll = onResize(\n      ({ width, height }) => {\n        api.start({\n          width,\n          height,\n          immediate:\n            sizeValues.width.get() === 0 || sizeValues.height.get() === 0,\n        })\n      },\n      { container: container?.current || undefined }\n    )\n\n    return () => {\n      /**\n       * Stop the springs on unmount.\n       */\n      each(Object.values(sizeValues), value => value.stop())\n\n      cleanupScroll()\n    }\n  }, [])\n\n  return sizeValues\n}\n", "import { RefObject, useRef, useState } from 'react'\nimport { is, useIsomorphicLayoutEffect } from '@react-spring/shared'\nimport { Lookup } from '@react-spring/types'\n\nimport { PickAnimated, SpringValues } from '../types'\nimport { useSpring, UseSpringProps } from './useSpring'\nimport { Valid } from '../types/common'\n\nexport interface IntersectionArgs\n  extends Omit<IntersectionObserverInit, 'root' | 'threshold'> {\n  root?: React.MutableRefObject<HTMLElement>\n  once?: boolean\n  amount?: 'any' | 'all' | number | number[]\n}\n\nconst defaultThresholdOptions = {\n  any: 0,\n  all: 1,\n}\n\nexport function useInView(args?: IntersectionArgs): [RefObject<any>, boolean]\nexport function useInView<Props extends object>(\n  /**\n   * TODO: make this narrower to only accept reserved props.\n   */\n  props: () => Props & Valid<Props, UseSpringProps<Props>>,\n  args?: IntersectionArgs\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup\n    ? [RefObject<any>, SpringValues<State>]\n    : never\n  : never\nexport function useInView<TElement extends HTMLElement>(\n  props?: (() => UseSpringProps<any>) | IntersectionArgs,\n  args?: IntersectionArgs\n) {\n  const [isInView, setIsInView] = useState(false)\n  const ref = useRef<TElement>()\n\n  const propsFn = is.fun(props) && props\n\n  const springsProps = propsFn ? propsFn() : {}\n  const { to = {}, from = {}, ...restSpringProps } = springsProps\n\n  const intersectionArguments = propsFn ? args : props\n\n  const [springs, api] = useSpring(() => ({ from, ...restSpringProps }), [])\n\n  useIsomorphicLayoutEffect(() => {\n    const element = ref.current\n    const {\n      root,\n      once,\n      amount = 'any',\n      ...restArgs\n    } = intersectionArguments ?? {}\n\n    if (\n      !element ||\n      (once && isInView) ||\n      typeof IntersectionObserver === 'undefined'\n    )\n      return\n\n    const activeIntersections = new WeakMap<Element, VoidFunction>()\n\n    const onEnter = () => {\n      if (to) {\n        // @ts-expect-error – TODO: fix this type error\n        api.start(to)\n      }\n\n      setIsInView(true)\n\n      const cleanup = () => {\n        if (from) {\n          api.start(from)\n        }\n        setIsInView(false)\n      }\n\n      return once ? undefined : cleanup\n    }\n\n    const handleIntersection: IntersectionObserverCallback = entries => {\n      entries.forEach(entry => {\n        const onLeave = activeIntersections.get(entry.target)\n\n        if (entry.isIntersecting === Boolean(onLeave)) {\n          return\n        }\n\n        if (entry.isIntersecting) {\n          const newOnLeave = onEnter()\n          if (is.fun(newOnLeave)) {\n            activeIntersections.set(entry.target, newOnLeave)\n          } else {\n            observer.unobserve(entry.target)\n          }\n        } else if (onLeave) {\n          onLeave()\n          activeIntersections.delete(entry.target)\n        }\n      })\n    }\n\n    const observer = new IntersectionObserver(handleIntersection, {\n      root: (root && root.current) || undefined,\n      threshold:\n        typeof amount === 'number' || Array.isArray(amount)\n          ? amount\n          : defaultThresholdOptions[amount],\n      ...restArgs,\n    })\n\n    observer.observe(element)\n\n    return () => observer.unobserve(element)\n  }, [intersectionArguments])\n\n  if (propsFn) {\n    return [ref, springs]\n  }\n\n  return [ref, isInView]\n}\n", "import { NoInfer, UnknownProps } from '@react-spring/types'\nimport { useSpring, UseSpringProps } from '../hooks/useSpring'\nimport { SpringValues, SpringToFn, SpringChain } from '../types'\n\nexport type SpringComponentProps<State extends object = UnknownProps> =\n  unknown &\n    UseSpringProps<State> & {\n      children: (values: SpringValues<State>) => JSX.Element | null\n    }\n\n// Infer state from \"from\" object prop.\nexport function Spring<State extends object>(\n  props: {\n    from: State\n    to?: SpringChain<NoInfer<State>> | SpringToFn<NoInfer<State>>\n  } & Omit<SpringComponentProps<NoInfer<State>>, 'from' | 'to'>\n): JSX.Element | null\n\n// Infer state from \"to\" object prop.\nexport function Spring<State extends object>(\n  props: { to: State } & Omit<SpringComponentProps<NoInfer<State>>, 'to'>\n): JSX.Element | null\n\nexport function Spring({ children, ...props }: any) {\n  return children(useSpring(props))\n}\n", "import { ReactNode } from 'react'\nimport { NoInfer, Falsy } from '@react-spring/types'\nimport { is } from '@react-spring/shared'\n\nimport { Valid } from '../types/common'\nimport { PickAnimated, SpringValues } from '../types'\nimport { UseSpringProps } from '../hooks/useSpring'\nimport { useTrail } from '../hooks/useTrail'\n\nexport type TrailComponentProps<Item, Props extends object = any> = unknown &\n  UseSpringProps<Props> & {\n    items: readonly Item[]\n    children: (\n      item: NoInfer<Item>,\n      index: number\n    ) => ((values: SpringValues<PickAnimated<Props>>) => ReactNode) | Falsy\n  }\n\nexport function Trail<Item, Props extends TrailComponentProps<Item>>({\n  items,\n  children,\n  ...props\n}: Props & Valid<Props, TrailComponentProps<Item, Props>>) {\n  const trails: any[] = useTrail(items.length, props)\n  return items.map((item, index) => {\n    const result = children(item, index)\n    return is.fun(result) ? result(trails[index]) : result\n  })\n}\n", "import { Valid } from '../types/common'\nimport { TransitionComponentProps } from '../types'\nimport { useTransition } from '../hooks'\n\nexport function Transition<Item, Props extends TransitionComponentProps<Item>>(\n  props:\n    | TransitionComponentProps<Item>\n    | (Props & Valid<Props, TransitionComponentProps<Item, Props>>)\n): JSX.Element\n\nexport function Transition({\n  items,\n  children,\n  ...props\n}: TransitionComponentProps<any>) {\n  return useTransition(items, props)(children)\n}\n", "import { FluidValue, deprecateInterpolate } from '@react-spring/shared'\nimport {\n  Constrain,\n  OneOrMore,\n  Animatable,\n  ExtrapolateType,\n  InterpolatorConfig,\n  InterpolatorFn,\n} from '@react-spring/types'\nimport { Interpolation } from './Interpolation'\n\n/** Map the value of one or more dependencies */\nexport const to: Interpolator = (source: any, ...args: [any]) =>\n  new Interpolation(source, args)\n\n/** @deprecated Use the `to` export instead */\nexport const interpolate: Interpolator = (source: any, ...args: [any]) => (\n  deprecateInterpolate(), new Interpolation(source, args)\n)\n\n/** Extract the raw value types that are being interpolated */\nexport type Interpolated<T extends ReadonlyArray<any>> = {\n  [P in keyof T]: T[P] extends infer Element\n    ? Element extends FluidValue<infer U>\n      ? U\n      : Element\n    : never\n}\n\n/**\n * This interpolates one or more `FluidValue` objects.\n * The exported `interpolate` function uses this type.\n */\nexport interface Interpolator {\n  // Tuple of parent values\n  <Input extends ReadonlyArray<any>, Output>(\n    parents: Input,\n    interpolator: (...args: Interpolated<Input>) => Output\n  ): Interpolation<Output>\n\n  // Single parent value\n  <Input, Output>(\n    parent: FluidValue<Input> | Input,\n    interpolator: InterpolatorFn<Input, Output>\n  ): Interpolation<Output>\n\n  // Interpolation config\n  <Out>(\n    parents: OneOrMore<FluidValue>,\n    config: InterpolatorConfig<Out>\n  ): Interpolation<Animatable<Out>>\n\n  // Range shortcuts\n  <Out>(\n    parents: OneOrMore<FluidValue<number>> | FluidValue<number[]>,\n    range: readonly number[],\n    output: readonly Constrain<Out, Animatable>[],\n    extrapolate?: ExtrapolateType\n  ): Interpolation<Animatable<Out>>\n}\n", "import { Arrify, InterpolatorArgs, InterpolatorFn } from '@react-spring/types'\nimport {\n  is,\n  raf,\n  each,\n  isEqual,\n  toArray,\n  frameLoop,\n  FluidValue,\n  getFluidValue,\n  createInterpolator,\n  Globals as G,\n  callFluidObservers,\n  addFluidObserver,\n  removeFluidObserver,\n  hasFluidValue,\n} from '@react-spring/shared'\n\nimport { FrameValue, isFrameValue } from './FrameValue'\nimport {\n  getAnimated,\n  setAnimated,\n  getAnimatedType,\n  getPayload,\n} from '@react-spring/animated'\n\n/**\n * An `Interpolation` is a memoized value that's computed whenever one of its\n * `FluidValue` dependencies has its value changed.\n *\n * Other `FrameValue` objects can depend on this. For example, passing an\n * `Interpolation` as the `to` prop of a `useSpring` call will trigger an\n * animation toward the memoized value.\n */\nexport class Interpolation<\n  Input = any,\n  Output = any,\n> extends FrameValue<Output> {\n  /** Useful for debugging. */\n  key?: string\n\n  /** Equals false when in the frameloop */\n  idle = true\n\n  /** The function that maps inputs values to output */\n  readonly calc: InterpolatorFn<Input, Output>\n\n  /** The inputs which are currently animating */\n  protected _active = new Set<FluidValue>()\n\n  constructor(\n    /** The source of input values */\n    readonly source: unknown,\n    args: InterpolatorArgs<Input, Output>\n  ) {\n    super()\n    this.calc = createInterpolator(...args)\n\n    const value = this._get()\n    const nodeType = getAnimatedType(value)\n\n    // Assume the computed value never changes type.\n    setAnimated(this, nodeType.create(value))\n  }\n\n  advance(_dt?: number) {\n    const value = this._get()\n    const oldValue = this.get()\n    if (!isEqual(value, oldValue)) {\n      getAnimated(this)!.setValue(value)\n      this._onChange(value, this.idle)\n    }\n    // Become idle when all parents are idle or paused.\n    if (!this.idle && checkIdle(this._active)) {\n      becomeIdle(this)\n    }\n  }\n\n  protected _get() {\n    const inputs: Arrify<Input> = is.arr(this.source)\n      ? this.source.map(getFluidValue)\n      : (toArray(getFluidValue(this.source)) as any)\n\n    return this.calc(...inputs)\n  }\n\n  protected _start() {\n    if (this.idle && !checkIdle(this._active)) {\n      this.idle = false\n\n      each(getPayload(this)!, node => {\n        node.done = false\n      })\n\n      if (G.skipAnimation) {\n        raf.batchedUpdates(() => this.advance())\n        becomeIdle(this)\n      } else {\n        frameLoop.start(this)\n      }\n    }\n  }\n\n  // Observe our sources only when we're observed.\n  protected _attach() {\n    let priority = 1\n    each(toArray(this.source), source => {\n      if (hasFluidValue(source)) {\n        addFluidObserver(source, this)\n      }\n      if (isFrameValue(source)) {\n        if (!source.idle) {\n          this._active.add(source)\n        }\n        priority = Math.max(priority, source.priority + 1)\n      }\n    })\n    this.priority = priority\n    this._start()\n  }\n\n  // Stop observing our sources once we have no observers.\n  protected _detach() {\n    each(toArray(this.source), source => {\n      if (hasFluidValue(source)) {\n        removeFluidObserver(source, this)\n      }\n    })\n    this._active.clear()\n    becomeIdle(this)\n  }\n\n  /** @internal */\n  eventObserved(event: FrameValue.Event) {\n    // Update our value when an idle parent is changed,\n    // and enter the frameloop when a parent is resumed.\n    if (event.type == 'change') {\n      if (event.idle) {\n        this.advance()\n      } else {\n        this._active.add(event.parent)\n        this._start()\n      }\n    }\n    // Once all parents are idle, the `advance` method runs one more time,\n    // so we should avoid updating the `idle` status here.\n    else if (event.type == 'idle') {\n      this._active.delete(event.parent)\n    }\n    // Ensure our priority is greater than all parents, which means\n    // our value won't be updated until our parents have updated.\n    else if (event.type == 'priority') {\n      this.priority = toArray(this.source).reduce(\n        (highest: number, parent) =>\n          Math.max(highest, (isFrameValue(parent) ? parent.priority : 0) + 1),\n        0\n      )\n    }\n  }\n}\n\n/** Returns true for an idle source. */\nfunction isIdle(source: any) {\n  return source.idle !== false\n}\n\n/** Return true if all values in the given set are idle or paused. */\nfunction checkIdle(active: Set<FluidValue>) {\n  // Parents can be active even when paused, so the `.every` check\n  // removes us from the frameloop if all active parents are paused.\n  return !active.size || Array.from(active).every(isIdle)\n}\n\n/** Become idle if not already idle. */\nfunction becomeIdle(self: Interpolation) {\n  if (!self.idle) {\n    self.idle = true\n\n    each(getPayload(self)!, node => {\n      node.done = true\n    })\n\n    callFluidObservers(self, {\n      type: 'idle',\n      parent: self,\n    })\n  }\n}\n", "import {\n  Globals,\n  frameLoop,\n  createStringInterpolator,\n} from '@react-spring/shared'\nimport { Interpolation } from './Interpolation'\n\n// Sane defaults\nGlobals.assign({\n  createStringInterpolator,\n  to: (source, args) => new Interpolation(source, args),\n})\n\nexport { Globals }\n\n/** Advance all animations by the given time */\nexport const update = frameLoop.advance\n"], "mappings": "+nBAAA,IAAAA,EAAA,GAAAC,GAAAD,EAAA,gBAAAE,GAAA,eAAAC,GAAA,eAAAC,GAAA,yCAAAC,GAAA,WAAAC,GAAA,kBAAAC,GAAA,cAAAC,GAAA,gBAAAC,GAAA,UAAAC,GAAA,eAAAC,GAAA,WAAAC,GAAA,gFAAAC,GAAA,gBAAAC,GAAA,OAAAC,GAAA,WAAAC,GAAA,aAAAC,GAAA,cAAAC,GAAA,kHAAAC,GAAA,cAAAC,GAAA,cAAAC,GAAA,iBAAAC,GAAA,mBAAAC,GAAA,eAAAC,GAAA,aAAAC,GAAA,kBAAAC,KAAA,eAAAC,GAAA3B,GCAA,IAAA4B,EAAgD,gCCAhD,IAAAC,EAQO,gCAMA,SAASC,EACdC,KACGC,EACoC,CACvC,OAAO,KAAG,IAAID,CAAK,EAAIA,EAAM,GAAGC,CAAI,EAAID,CAC1C,CAGO,IAAME,GAAY,CACvBF,EACAG,IAEAH,IAAU,IACV,CAAC,EACCG,GACAH,IACC,KAAG,IAAIA,CAAK,EAAIA,EAAMG,CAAG,KAAI,WAAQH,CAAK,EAAE,SAASG,CAAG,IAGhDC,GAAc,CACzBC,EACAF,IACI,KAAG,IAAIE,CAAI,EAAIF,GAAQE,EAAaF,CAAG,EAAIE,EAU1C,IAAMC,GAAiB,CAC5BC,EACAC,IAEAD,EAAM,UAAY,GACdA,EAAMC,CAAG,EACTD,EAAM,QACJA,EAAM,QAAQC,CAAG,EACjB,OAEFC,GAAiBC,GAAeA,EASzBC,GAAkB,CAC7BJ,EACAK,EAA8CH,KACxC,CACN,IAAII,EAA0BC,GAC1BP,EAAM,SAAWA,EAAM,UAAY,KACrCA,EAAQA,EAAM,QACdM,EAAO,OAAO,KAAKN,CAAK,GAE1B,IAAMQ,EAAgB,CAAC,EACvB,QAAWP,KAAOK,EAAM,CACtB,IAAMH,EAAQE,EAAUL,EAAMC,CAAG,EAAGA,CAAG,EAClC,KAAG,IAAIE,CAAK,IACfK,EAASP,CAAG,EAAIE,GAGpB,OAAOK,CACT,EAaaD,GAAgB,CAC3B,SACA,UACA,UACA,WACA,UACA,WACA,QACF,EAEME,GAEF,CACF,OAAQ,EACR,KAAM,EACN,GAAI,EACJ,IAAK,EACL,KAAM,EACN,MAAO,EACP,MAAO,EACP,OAAQ,EACR,QAAS,EACT,UAAW,EACX,QAAS,EACT,MAAO,EACP,QAAS,EACT,QAAS,EACT,SAAU,EACV,QAAS,EACT,SAAU,EACV,OAAQ,EACR,UAAW,EAGX,MAAO,EACP,MAAO,EACP,KAAM,EACN,QAAS,EACT,QAAS,EACT,MAAO,EACP,OAAQ,EACR,MAAO,EACP,SAAU,EACV,YAAa,EAGb,KAAM,EACN,OAAQ,EACR,SAAU,CACZ,EAOA,SAASC,GACPV,EACiC,CACjC,IAAMW,EAAe,CAAC,EAElBC,EAAQ,EAQZ,MAPA,YAASZ,EAAO,CAACG,EAAOU,IAAS,CAC1BJ,GAAeI,CAAI,IACtBF,EAAQE,CAAI,EAAIV,EAChBS,IAEJ,CAAC,EAEGA,EACF,OAAOD,CAEX,CAMO,SAASG,GAA0Bd,EAAsB,CAC9D,IAAMe,EAAKL,GAAgBV,CAAK,EAChC,GAAIe,EAAI,CACN,IAAMC,EAAW,CAAE,GAAAD,CAAG,EACtB,qBAASf,EAAO,CAACiB,EAAKhB,IAAQA,KAAOc,IAAOC,EAAIf,CAAG,EAAIgB,EAAI,EACpDD,EAET,MAAO,CAAE,GAAGhB,CAAM,CACpB,CAGO,SAASkB,GAAef,EAA6B,CAC1D,OAAAA,KAAQ,iBAAcA,CAAK,EACpB,KAAG,IAAIA,CAAK,EACfA,EAAM,IAAIe,EAAW,KACrB,oBAAiBf,CAAK,EACnB,EAAAgB,QAAE,yBAAyB,CAC1B,MAAO,CAAC,EAAG,CAAC,EACZ,OAAQ,CAAChB,EAAOA,CAAK,CACvB,CAAC,EAAE,CAAC,EACJA,CACR,CAEO,SAASiB,GAASpB,EAAe,CACtC,QAAWqB,KAAKrB,EAAO,MAAO,GAC9B,MAAO,EACT,CAEO,SAASsB,GAAUP,EAAS,CACjC,OAAO,KAAG,IAAIA,CAAE,GAAM,KAAG,IAAIA,CAAE,GAAK,KAAG,IAAIA,EAAG,CAAC,CAAC,CAClD,CAGO,SAASQ,GAAWC,EAAkBC,EAAiB,CAC5DD,EAAK,KAAK,OAAOA,CAAI,EACrBC,GAAK,OAAOD,CAAI,CAClB,CAGO,SAASE,GAAWF,EAAkBC,EAAiB,CACxDA,GAAOD,EAAK,MAAQC,IACtBD,EAAK,KAAK,OAAOA,CAAI,EACrBC,EAAI,IAAID,CAAI,EACZA,EAAK,IAAMC,EAEf,CD/LO,SAASE,GACdC,EACAC,EACAC,EAAY,IACZ,IACA,6BAA0B,IAAM,CAC9B,GAAID,EAAW,CACb,IAAIE,EAAY,KAChB,QAAKH,EAAM,CAACI,EAAKC,IAAM,CACrB,IAAMC,EAAcF,EAAI,QACxB,GAAIE,EAAY,OAAQ,CACtB,IAAIC,EAAQL,EAAYD,EAAUI,CAAC,EAG/B,MAAME,CAAK,EAAGA,EAAQJ,EACrBA,EAAYI,KAEjB,QAAKD,EAAaE,GAAQ,IACxB,QAAKA,EAAK,MAAOC,GAAS,CAExB,IAAMC,EAAoBD,EAAM,MAChCA,EAAM,MAAQE,GAAOJ,EAAQK,EAASF,GAAqB,EAAGC,CAAG,CACnE,CAAC,CACH,CAAC,EAEDP,EAAI,MAAM,EAEd,CAAC,MACI,CACL,IAAIS,EAAkB,QAAQ,QAAQ,KACtC,QAAKb,EAAMI,GAAO,CAChB,IAAME,EAAcF,EAAI,QACxB,GAAIE,EAAY,OAAQ,CAEtB,IAAMQ,EAASR,EAAY,IAAIE,GAAQ,CACrC,IAAMO,EAAIP,EAAK,MACf,OAAAA,EAAK,MAAQ,CAAC,EACPO,CACT,CAAC,EAGDF,EAAIA,EAAE,KAAK,QACT,QAAKP,EAAa,CAACE,EAAMH,OACvB,QAAKS,EAAOT,CAAC,GAAK,CAAC,EAAGW,GAAUR,EAAK,MAAM,KAAKQ,CAAM,CAAC,CACzD,EACO,QAAQ,IAAIZ,EAAI,MAAM,CAAC,EAC/B,EAEL,CAAC,EAEL,CAAC,CACH,CE7EA,IAAAa,GAAmB,gCCDnB,IAAAC,EAA4C,iBAE5CC,EAOO,gCCTP,IAAAC,EAkBO,gCACPC,EAQO,kCC3BP,IAAAC,EAA4B,gCCCrB,IAAMC,GAAS,CACpB,QAAS,CAAE,QAAS,IAAK,SAAU,EAAG,EACtC,OAAQ,CAAE,QAAS,IAAK,SAAU,EAAG,EACrC,OAAQ,CAAE,QAAS,IAAK,SAAU,EAAG,EACrC,MAAO,CAAE,QAAS,IAAK,SAAU,EAAG,EACpC,KAAM,CAAE,QAAS,IAAK,SAAU,EAAG,EACnC,SAAU,CAAE,QAAS,IAAK,SAAU,GAAI,CAC1C,EDJA,IAAMC,GAAgB,CACpB,GAAGC,GAAQ,QACX,KAAM,EACN,QAAS,EACT,OAAQ,UAAQ,OAChB,MAAO,EACT,EAEaC,GAAN,KAAsB,CA2I3B,aAAc,CAnFd,cAA8B,EAoF5B,OAAO,OAAO,KAAMF,EAAQ,CAC9B,CACF,EAQO,SAASG,GACdF,EACAG,EACAC,EACA,CACIA,IACFA,EAAgB,CAAE,GAAGA,CAAc,EACnCC,GAAeD,EAAeD,CAAS,EACvCA,EAAY,CAAE,GAAGC,EAAe,GAAGD,CAAU,GAG/CE,GAAeL,EAAQG,CAAS,EAChC,OAAO,OAAOH,EAAQG,CAAS,EAE/B,QAAWG,KAAOP,GACZC,EAAOM,CAAG,GAAK,OACjBN,EAAOM,CAAG,EAAIP,GAASO,CAAG,GAI9B,GAAI,CAAE,UAAAC,EAAW,QAAAC,CAAQ,EAAIR,EACvB,CAAE,KAAAS,CAAK,EAAIT,EACjB,OAAK,KAAG,IAAIO,CAAS,IACfA,EAAY,MAAMA,EAAY,KAC9BC,EAAU,IAAGA,EAAU,GAC3BR,EAAO,QAAU,KAAK,IAAK,EAAI,KAAK,GAAMO,EAAW,CAAC,EAAIE,EAC1DT,EAAO,SAAY,EAAI,KAAK,GAAKQ,EAAUC,EAAQF,GAG9CP,CACT,CAIA,SAASK,GACPL,EACAU,EACA,CACA,GAAI,CAAC,KAAG,IAAIA,EAAM,KAAK,EACrBV,EAAO,SAAW,WACb,CACL,IAAMW,EAAkB,CAAC,KAAG,IAAID,EAAM,OAAO,GAAK,CAAC,KAAG,IAAIA,EAAM,QAAQ,GAEtEC,GACA,CAAC,KAAG,IAAID,EAAM,SAAS,GACvB,CAAC,KAAG,IAAIA,EAAM,OAAO,GACrB,CAAC,KAAG,IAAIA,EAAM,IAAI,KAElBV,EAAO,SAAW,OAClBA,EAAO,MAAQ,QAEbW,IACFX,EAAO,UAAY,QAGzB,CEnNA,IAAMY,GAA6B,CAAC,EAIvBC,GAAN,KAAyB,CAAzB,cACL,aAAU,GACV,YAAmCD,GACnC,cAAqC,KACrC,gBAAgCA,GAIhC,YAAS,IAAIE,GACb,eAAY,GACd,ECpBA,IAAAC,GAA+C,gCAiCxC,SAASC,GACdC,EACA,CAAE,IAAAC,EAAK,MAAAC,EAAO,aAAAC,EAAc,MAAAC,EAAO,QAAAC,CAAQ,EAC3B,CAChB,OAAO,IAAI,QAAQ,CAACC,EAASC,IAAW,CACtC,IAAIC,EACAC,EAEAC,EAASC,GAAUT,EAAM,QAAUC,GAAc,OAAQF,CAAG,EAChE,GAAIS,EACFE,EAAQ,MACH,CAEA,MAAG,IAAIV,EAAM,KAAK,IACrBE,EAAM,OAASO,GAAUT,EAAM,MAAOD,CAAG,GAI3C,IAAIY,EAAQV,GAAc,MACtBU,IAAU,KACZA,EAAQT,EAAM,QAAUO,GAAUE,EAAOZ,CAAG,GAG9CO,EAAQM,EAASZ,EAAM,OAAS,EAAGD,CAAG,EAClCY,GACFT,EAAM,YAAY,IAAIW,CAAQ,EAC9BV,EAAQ,MAAM,IAEdA,EAAQ,OAAO,EACfU,EAAS,GAIb,SAASC,GAAU,CACjBZ,EAAM,YAAY,IAAIW,CAAQ,EAC9BX,EAAM,SAAS,OAAOK,CAAO,EAC7BA,EAAQ,OAAO,EAEfD,EAAQC,EAAQ,KAAO,OAAI,IAAI,CACjC,CAEA,SAASM,GAAW,CACdP,EAAQ,GAAK,CAAC,GAAAS,QAAE,eAClBb,EAAM,QAAU,GAChBK,EAAU,OAAI,WAAWG,EAASJ,CAAK,EACvCJ,EAAM,WAAW,IAAIY,CAAO,EAC5BZ,EAAM,SAAS,IAAIK,CAAO,GAE1BG,EAAQ,CAEZ,CAEA,SAASA,GAAU,CACbR,EAAM,UACRA,EAAM,QAAU,IAGlBA,EAAM,WAAW,OAAOY,CAAO,EAC/BZ,EAAM,SAAS,OAAOK,CAAO,EAGzBT,IAAWI,EAAM,UAAY,KAC/BM,EAAS,IAGX,GAAI,CACFL,EAAQ,MAAM,CAAE,GAAGH,EAAO,OAAAF,EAAQ,OAAAU,CAAO,EAAGJ,CAAO,CACrD,OAASY,EAAP,CACAX,EAAOW,CAAG,CACZ,CACF,CACF,CAAC,CACH,CCzGA,IAAAC,EAOO,gCCHA,IAAMC,GAAoB,CAC/BC,EACAC,IAEAA,EAAQ,QAAU,EACdA,EAAQ,CAAC,EACTA,EAAQ,KAAKC,GAAUA,EAAO,SAAS,EACrCC,GAAmBH,EAAO,IAAI,CAAC,EAC/BC,EAAQ,MAAMC,GAAUA,EAAO,IAAI,EACjCE,GAAcJ,EAAO,IAAI,CAAC,EAC1BK,EACEL,EAAO,IAAI,EACXC,EAAQ,MAAMC,GAAUA,EAAO,QAAQ,CACzC,EAGGE,GAAiBE,IAAgB,CAC5C,MAAAA,EACA,KAAM,GACN,SAAU,GACV,UAAW,EACb,GAEaD,EAAoB,CAC/BC,EACAC,EACAC,EAAY,MACR,CACJ,MAAAF,EACA,SAAAC,EACA,UAAAC,CACF,GAEaL,GAAsBG,IAAgB,CACjD,MAAAA,EACA,UAAW,GACX,SAAU,EACZ,GDKO,SAASG,GACdC,EACAC,EACAC,EACAC,EACgB,CAChB,GAAM,CAAE,OAAAC,EAAQ,SAAAC,EAAU,OAAAC,CAAO,EAAIL,EAC/B,CAAE,QAASM,EAAQ,QAASC,CAAY,EAAIN,EAElD,MAAI,CAACG,GAAYL,IAAOO,GAAU,CAACN,EAAM,MAChCO,EAGDN,EAAM,SAAW,SAAY,CACnCA,EAAM,QAAUE,EAChBF,EAAM,QAAUF,EAGhB,IAAMS,EAAeC,GAA+BT,EAAO,CAACU,EAAOC,IAEjEA,IAAQ,SAAW,OAAYD,CACjC,EAEIE,EACAC,EAGEC,EAAc,IAAI,QACtB,CAACC,EAASC,KAAaJ,EAAcG,EAAWF,EAAOG,EACzD,EAEMC,EAAeC,GAA2B,CAC9C,IAAMC,EAEHhB,IAAWF,EAAM,UAAY,IAAMmB,GAAmBlB,CAAM,GAE5DC,IAAWF,EAAM,SAAWoB,EAAkBnB,EAAQ,EAAK,EAE9D,GAAIiB,EACF,MAAAD,EAAW,OAASC,EAIpBN,EAAKK,CAAU,EACTA,CAEV,EAEMI,EAAe,CAACC,EAAWC,IAAe,CAG9C,IAAMN,EAAa,IAAIO,GACjBC,EAAsB,IAAIC,GAEhC,OAAQ,SAAY,CAClB,GAAI,EAAAC,QAAE,cAMJ,MAAAC,GAAU5B,CAAK,EAGfyB,EAAoB,OAASL,EAAkBnB,EAAQ,EAAK,EAC5DW,EAAKa,CAAmB,EAClBA,EAGRT,EAAYC,CAAU,EAEtB,IAAMlB,EAAa,KAAG,IAAIuB,CAAI,EAAI,CAAE,GAAGA,CAAK,EAAI,CAAE,GAAGC,EAAM,GAAID,CAAK,EACpEvB,EAAM,SAAWG,KAEjB,YAASK,EAAc,CAACE,EAAOC,IAAQ,CACjC,KAAG,IAAIX,EAAMW,CAAG,CAAC,IACnBX,EAAMW,CAAG,EAAID,EAEjB,CAAC,EAED,IAAMoB,EAAS,MAAM5B,EAAO,MAAMF,CAAK,EACvC,OAAAiB,EAAYC,CAAU,EAElBjB,EAAM,QACR,MAAM,IAAI,QAAc8B,GAAU,CAChC9B,EAAM,YAAY,IAAI8B,CAAM,CAC9B,CAAC,EAGID,CACT,GAAG,CACL,EAEIA,EAEJ,GAAI,EAAAF,QAAE,cAKJ,OAAAC,GAAU5B,CAAK,EACRoB,EAAkBnB,EAAQ,EAAK,EAGxC,GAAI,CACF,IAAI8B,EAGA,KAAG,IAAIjC,CAAE,EACXiC,GAAa,MAAOC,GAAiB,CACnC,QAAWjC,KAASiC,EAClB,MAAMX,EAAQtB,CAAK,CAEvB,GAAGD,CAAE,EAKLiC,EAAY,QAAQ,QAAQjC,EAAGuB,EAASpB,EAAO,KAAK,KAAKA,CAAM,CAAC,CAAC,EAGnE,MAAM,QAAQ,IAAI,CAAC8B,EAAU,KAAKpB,CAAW,EAAGE,CAAW,CAAC,EAC5DgB,EAAST,EAAkBnB,EAAO,IAAI,EAAG,GAAM,EAAK,CAGtD,OAASgC,EAAP,CACA,GAAIA,aAAeT,GACjBK,EAASI,EAAI,eACJA,aAAeP,GACxBG,EAASI,EAAI,WAEb,OAAMA,CAIV,QAAE,CACI/B,GAAUF,EAAM,UAClBA,EAAM,QAAUG,EAChBH,EAAM,QAAUG,EAAWE,EAAS,OACpCL,EAAM,QAAUG,EAAWG,EAAc,OAE7C,CAEA,OAAI,KAAG,IAAIF,CAAM,GACf,MAAI,eAAe,IAAM,CACvBA,EAAOyB,EAAQ5B,EAAQA,EAAO,IAAI,CACpC,CAAC,EAGI4B,CACT,GAAG,CACL,CAGO,SAASD,GAAU5B,EAAsBkC,EAA2B,IACzE,SAAMlC,EAAM,SAAUmC,GAAKA,EAAE,OAAO,CAAC,EACrCnC,EAAM,WAAW,MAAM,EACvBA,EAAM,YAAY,MAAM,EACxBA,EAAM,QAAUA,EAAM,QAAUA,EAAM,QAAU,OAC5CkC,IAAUlC,EAAM,SAAWkC,EACjC,CAGO,IAAMV,GAAN,cAAyB,KAAM,CAEpC,aAAc,CACZ,MACE,yIAEF,CACF,CACF,EAEaE,GAAN,cAAkC,KAAM,CAG7C,aAAc,CACZ,MAAM,qBAAqB,CAC7B,CACF,EEjOA,IAAAU,EAMO,gCAEPC,GAA4B,kCAIfC,GAAgBC,GAC3BA,aAAiBC,GAEfC,GAAS,EAOSD,GAAf,cAA2C,YAGhD,CAHK,kCAIL,KAAS,GAAKC,KAKd,KAAU,UAAY,EAEtB,IAAI,UAAW,CACb,OAAO,KAAK,SACd,CACA,IAAI,SAASC,EAAkB,CACzB,KAAK,WAAaA,IACpB,KAAK,UAAYA,EACjB,KAAK,kBAAkBA,CAAQ,EAEnC,CAGA,KAAS,CACP,IAAMC,KAAO,gBAAY,IAAI,EAC7B,OAAOA,GAAQA,EAAK,SAAS,CAC/B,CAGA,MAAWC,EAAgC,CACzC,OAAO,EAAAC,QAAE,GAAG,KAAMD,CAAI,CACxB,CAGA,eAAoBA,EAAgC,CAClD,iCAAqB,EACd,EAAAC,QAAE,GAAG,KAAMD,CAAI,CACxB,CAEA,QAAS,CACP,OAAO,KAAK,IAAI,CAClB,CAEU,cAAcE,EAAe,CACjCA,GAAS,GAAG,KAAK,QAAQ,CAC/B,CAEU,gBAAgBA,EAAe,CACnCA,GAAS,GAAG,KAAK,QAAQ,CAC/B,CASU,SAAU,CAAC,CAGX,SAAU,CAAC,CAGX,UAAUP,EAAUQ,EAAO,GAAO,IAC1C,sBAAmB,KAAM,CACvB,KAAM,SACN,OAAQ,KACR,MAAAR,EACA,KAAAQ,CACF,CAAC,CACH,CAGU,kBAAkBL,EAAkB,CACvC,KAAK,MACR,YAAU,KAAK,IAAI,KAErB,sBAAmB,KAAM,CACvB,KAAM,WACN,OAAQ,KACR,SAAAA,CACF,CAAC,CACH,CACF,ECxGA,IAAMM,GAAK,OAAO,IAAI,aAAa,EAE7BC,GAAe,EACfC,GAAe,EACfC,GAAY,EAGLC,GAAeC,IAAiBA,EAAOL,EAAE,EAAIC,IAAgB,EAG7DK,GAAeD,IAAiBA,EAAOL,EAAE,EAAIE,IAAgB,EAG7DK,GAAYF,IAAiBA,EAAOL,EAAE,EAAIG,IAAa,EAGvDK,GAAe,CAACH,EAAaI,IACxCA,EACKJ,EAAOL,EAAE,GAAKE,GAAeD,GAC7BI,EAAOL,EAAE,GAAK,CAACE,GAETQ,GAAe,CAACL,EAAaM,IACxCA,EAAUN,EAAOL,EAAE,GAAKG,GAAcE,EAAOL,EAAE,GAAK,CAACG,GRqDhD,IAAMS,GAAN,cAAmCC,EAAc,CAmCtD,YAAYC,EAAYC,EAAY,CAClC,MAAM,EA/BR,eAAY,IAAIC,GAMhB,kBAAsC,CAAC,EAGvC,KAAU,OAAwC,CAChD,OAAQ,GACR,QAAS,GACT,WAAY,IAAI,IAChB,YAAa,IAAI,IACjB,SAAU,IAAI,GAChB,EAGA,KAAU,cAAgB,IAAI,IAG9B,KAAU,YAAc,EAGxB,KAAU,UAAY,EAEtB,KAAU,kBAAoB,EAMxB,IAAC,KAAG,IAAIF,CAAI,GAAK,CAAC,KAAG,IAAIC,CAAI,EAAG,CAClC,IAAME,EAAQ,KAAG,IAAIH,CAAI,EAAI,CAAE,GAAGA,CAAK,EAAI,CAAE,GAAGC,EAAM,KAAMD,CAAK,EAC7D,KAAG,IAAIG,EAAM,OAAO,IACtBA,EAAM,QAAU,IAElB,KAAK,MAAMA,CAAK,EAEpB,CAGA,IAAI,MAAO,CACT,MAAO,EAAEC,GAAY,IAAI,GAAK,KAAK,OAAO,UAAYC,GAAS,IAAI,CACrE,CAEA,IAAI,MAAO,CACT,SAAO,iBAAc,KAAK,UAAU,EAAE,CACxC,CAEA,IAAI,UAA4B,CAC9B,IAAMC,KAAO,eAAY,IAAI,EAC7B,OACEA,aAAgB,gBACZA,EAAK,cAAgB,EACrBA,EAAK,WAAW,EAAE,IAAIA,GAAQA,EAAK,cAAgB,CAAC,CAE5D,CAKA,IAAI,aAAc,CAChB,OAAOC,GAAY,IAAI,CACzB,CAMA,IAAI,aAAc,CAChB,OAAOH,GAAY,IAAI,CACzB,CAKA,IAAI,UAAW,CACb,OAAOC,GAAS,IAAI,CACtB,CAMA,IAAI,WAAY,CACd,OAAO,KAAK,OAAO,OACrB,CAGA,QAAQG,EAAY,CAClB,IAAIC,EAAO,GACPC,EAAU,GAERC,EAAO,KAAK,UACd,CAAE,SAAAC,CAAS,EAAID,EACb,CAAE,OAAAE,CAAO,EAAIF,EAEbG,KAAU,cAAWH,EAAK,EAAE,EAC9B,CAACG,MAAW,iBAAcH,EAAK,EAAE,IACnCC,KAAW,cAAQ,iBAAcD,EAAK,EAAE,CAAC,GAG3CA,EAAK,OAAO,QAAQ,CAACL,EAAMS,IAAM,CAC/B,GAAIT,EAAK,KAAM,OAEf,IAAMU,EAEJV,EAAK,aAAe,iBAChB,EACAQ,EACEA,EAAQC,CAAC,EAAE,aACXH,EAAUG,CAAC,EAEfE,EAAWN,EAAK,UAChBO,EAAWF,EAEf,GAAI,CAACC,EAAU,CAIb,GAHAC,EAAWZ,EAAK,aAGZO,EAAO,SAAW,EAAG,CACvBP,EAAK,KAAO,GACZ,OAGF,IAAIa,EAAWb,EAAK,aAAeE,EAC7BY,EAAOT,EAAK,WAAWI,CAAC,EAExBM,EACJf,EAAK,IAAM,KACPA,EAAK,GACJA,EAAK,GAAK,KAAG,IAAIO,EAAO,QAAQ,EAC7BA,EAAO,SAASE,CAAC,EACjBF,EAAO,SAEbS,EAOEC,EACJV,EAAO,YACNO,GAAQJ,EAAK,KAAQ,KAAK,IAAI,EAAG,KAAK,IAAIA,EAAKI,CAAI,EAAI,IAAK,GAG/D,GAAK,KAAG,IAAIP,EAAO,QAAQ,EAqCtB,GAAIA,EAAO,MAAO,CACrB,IAAMW,EAAQX,EAAO,QAAU,GAAO,KAAQA,EAAO,MAC/CY,EAAI,KAAK,IAAI,EAAE,EAAID,GAASL,CAAO,EAEzCD,EAAWE,EAAQC,GAAM,EAAIG,IAAW,EAAIC,GAC5CR,EAAW,KAAK,IAAIX,EAAK,aAAeY,CAAQ,GAAKK,EAGrDD,EAAWD,EAAKI,MAIb,CACHH,EAAWhB,EAAK,cAAgB,KAAOe,EAAKf,EAAK,aAGjD,IAAMoB,EAAeb,EAAO,cAAgBU,EAAY,GAGlDI,EAAed,EAAO,MAAQ,EAAIA,EAAO,OACzCe,EAAY,CAAC,KAAG,IAAID,CAAY,EAGhCE,EAAYT,GAAQJ,EAAKV,EAAK,GAAK,EAAIc,EAAOJ,EAGhDc,EAGAC,EAAa,GAEXC,EAAO,EACPC,GAAW,KAAK,KAAKzB,EAAKwB,CAAI,EACpC,QAASE,EAAI,EAAGA,EAAID,KAClBH,EAAW,KAAK,IAAIR,CAAQ,EAAII,EAE5B,GAACI,IACHb,EAAW,KAAK,IAAID,EAAKE,CAAQ,GAAKK,EAClCN,KALsB,EAAEiB,EAAG,CAU7BN,IACFG,EAAab,GAAYF,GAAME,EAAWF,GAAMa,EAG5CE,IACFT,EAAW,CAACA,EAAWK,EACvBT,EAAWF,IAIf,IAAMmB,EAAc,CAACtB,EAAO,QAAU,MAAYK,EAAWF,GACvDoB,EAAe,CAACvB,EAAO,SAAW,KAAQS,EAC1Ce,GAAgBF,EAAcC,GAAgBvB,EAAO,KAE3DS,EAAWA,EAAWe,EAAeL,EACrCd,EAAWA,EAAWI,EAAWU,OA/FP,CAC5B,IAAIM,EAAI,EACJzB,EAAO,SAAW,IAOhB,KAAK,oBAAsBA,EAAO,WAEpC,KAAK,kBAAoBA,EAAO,SAG5BP,EAAK,iBAAmB,IAE1BA,EAAK,YAAcO,EAAO,SAAWP,EAAK,iBAE1Ca,EAAUb,EAAK,aAAeE,IAKlC8B,GAAKzB,EAAO,UAAY,GAAKM,EAAU,KAAK,kBAE5CmB,EAAIA,EAAI,EAAI,EAAIA,EAAI,EAAI,EAAIA,EAE5BhC,EAAK,iBAAmBgC,GAG1BpB,EAAWE,EAAOP,EAAO,OAAOyB,CAAC,GAAKtB,EAAKI,GAC3CE,GAAYJ,EAAWZ,EAAK,cAAgBE,EAE5CS,EAAWqB,GAAK,EAkElBhC,EAAK,aAAegB,EAEhB,OAAO,MAAMJ,CAAQ,IACvB,QAAQ,KAAK,2BAA4B,IAAI,EAC7CD,EAAW,IAKXH,GAAW,CAACA,EAAQC,CAAC,EAAE,OACzBE,EAAW,IAGTA,EACFX,EAAK,KAAO,GAEZG,EAAO,GAGLH,EAAK,SAASY,EAAUL,EAAO,KAAK,IACtCH,EAAU,GAEd,CAAC,EAED,IAAMJ,KAAO,eAAY,IAAI,EAKvBiC,EAAUjC,EAAK,SAAS,EAC9B,GAAIG,EAAM,CAER,IAAM+B,KAAW,iBAAc7B,EAAK,EAAE,GAKjC4B,IAAYC,GAAY9B,IAAY,CAACG,EAAO,OAE/CP,EAAK,SAASkC,CAAQ,EACtB,KAAK,UAAUA,CAAQ,GACd9B,GAAWG,EAAO,OAK3B,KAAK,UAAU0B,CAAO,EAGxB,KAAK,MAAM,OACF7B,GAKT,KAAK,UAAU6B,CAAO,CAE1B,CAGA,IAAIE,EAA0B,CAC5B,aAAI,eAAe,IAAM,CACvB,KAAK,MAAM,EAIX,KAAK,OAAOA,CAAK,EACjB,KAAK,KAAKA,CAAK,CACjB,CAAC,EACM,IACT,CAMA,OAAQ,CACN,KAAK,QAAQ,CAAE,MAAO,EAAK,CAAC,CAC9B,CAGA,QAAS,CACP,KAAK,QAAQ,CAAE,MAAO,EAAM,CAAC,CAC/B,CAGA,QAAS,CACP,GAAIrC,GAAY,IAAI,EAAG,CACrB,GAAM,CAAE,GAAAY,EAAI,OAAAH,CAAO,EAAI,KAAK,UAC5B,MAAI,eAAe,IAAM,CAEvB,KAAK,SAAS,EAITA,EAAO,OACV,KAAK,KAAKG,EAAI,EAAK,EAGrB,KAAK,MAAM,CACb,CAAC,EAEH,OAAO,IACT,CAGA,OAAOb,EAAwB,CAE7B,OADc,KAAK,QAAU,KAAK,MAAQ,CAAC,IACrC,KAAKA,CAAK,EACT,IACT,CAeA,MAAMa,EAAUf,EAAY,CAC1B,IAAIyC,EACJ,OAAK,KAAG,IAAI1B,CAAE,GAGZ0B,EAAQ,KAAK,OAAS,CAAC,EACvB,KAAK,MAAQ,CAAC,GAHdA,EAAQ,CAAC,KAAG,IAAI1B,CAAE,EAAIA,EAAK,CAAE,GAAGf,EAAM,GAAAe,CAAG,CAAC,EAMrC,QAAQ,IACb0B,EAAM,IAAIvC,GACG,KAAK,QAAQA,CAAK,CAE9B,CACH,EAAE,KAAKwC,GAAWC,GAAkB,KAAMD,CAAO,CAAC,CACpD,CAOA,KAAKE,EAAkB,CACrB,GAAM,CAAE,GAAA7B,CAAG,EAAI,KAAK,UAGpB,YAAK,OAAO,KAAK,IAAI,CAAC,EAEtB8B,GAAU,KAAK,OAAQD,GAAU,KAAK,WAAW,EACjD,MAAI,eAAe,IAAM,KAAK,MAAM7B,EAAI6B,CAAM,CAAC,EAExC,IACT,CAGA,OAAQ,CACN,KAAK,QAAQ,CAAE,MAAO,EAAK,CAAC,CAC9B,CAGA,cAAcE,EAAyB,CACjCA,EAAM,MAAQ,SAChB,KAAK,OAAO,EACHA,EAAM,MAAQ,aACvB,KAAK,SAAWA,EAAM,SAAW,EAErC,CAQU,aAAa5C,EAKpB,CACD,IAAM6C,EAAM,KAAK,KAAO,GAEpB,CAAE,GAAAhC,EAAI,KAAAI,CAAK,EAAIjB,EAEnBa,EAAK,KAAG,IAAIA,CAAE,EAAIA,EAAGgC,CAAG,EAAIhC,GACxBA,GAAM,MAAQiC,GAAUjC,CAAE,KAC5BA,EAAK,QAGPI,EAAO,KAAG,IAAIA,CAAI,EAAIA,EAAK4B,CAAG,EAAI5B,EAC9BA,GAAQ,OACVA,EAAO,QAIT,IAAM8B,EAAQ,CAAE,GAAAlC,EAAI,KAAAI,CAAK,EAIzB,OAAKb,GAAY,IAAI,IACfJ,EAAM,UAAS,CAACa,EAAII,CAAI,EAAI,CAACA,EAAMJ,CAAE,GAEzCI,KAAO,iBAAcA,CAAI,EACpB,KAAG,IAAIA,CAAI,KAIN,eAAY,IAAI,GACxB,KAAK,KAAKJ,CAAE,EAJZ,KAAK,KAAKI,CAAI,GAQX8B,CACT,CAGU,QACR,CAAE,GAAG/C,CAAM,EACXgD,EAC6B,CAC7B,GAAM,CAAE,IAAAH,EAAK,aAAAI,CAAa,EAAI,KAG1BjD,EAAM,SACR,OAAO,OACLiD,EACAC,GAAgBlD,EAAO,CAACsC,EAAOa,IAC7B,MAAM,KAAKA,CAAI,EAAIC,GAAYd,EAAOO,CAAG,EAAIP,CAC/C,CACF,EAEFe,GAAc,KAAMrD,EAAO,SAAS,EACpCsD,GAAU,KAAM,UAAWtD,EAAO,IAAI,EAGtC,IAAM+C,EAAQ,KAAK,aAAa/C,CAAK,EAErC,GAAI,OAAO,SAAS,IAAI,EACtB,MAAM,MACJ,4IAEF,EAGF,IAAMuD,EAAQ,KAAK,OAEnB,OAAOC,GAAc,EAAE,KAAK,YAAa,CACvC,IAAAX,EACA,MAAA7C,EACA,aAAAiD,EACA,MAAAM,EACA,QAAS,CACP,MAAO,IAAM,CACNrD,GAAS,IAAI,IAChBuD,GAAa,KAAM,EAAI,KACvB,cAAWF,EAAM,UAAU,EAC3BD,GACE,KACA,UACAI,EAAkB,KAAMC,GAAc,KAAM,KAAK,UAAU,EAAE,CAAC,EAC9D,IACF,EAEJ,EACA,OAAQ,IAAM,CACRzD,GAAS,IAAI,IACfuD,GAAa,KAAM,EAAK,EACpBxD,GAAY,IAAI,GAClB,KAAK,QAAQ,KAEf,cAAWsD,EAAM,WAAW,EAC5BD,GACE,KACA,WACAI,EAAkB,KAAMC,GAAc,KAAM,KAAK,UAAU,EAAE,CAAC,EAC9D,IACF,EAEJ,EACA,MAAO,KAAK,OAAO,KAAK,KAAMZ,CAAK,CACrC,CACF,CAAC,EAAE,KAAKa,GAAU,CAChB,GAAI5D,EAAM,MAAQ4D,EAAO,UAAY,EAAEZ,GAAUY,EAAO,MAAO,CAC7D,IAAMC,EAAYC,GAAiB9D,CAAK,EACxC,GAAI6D,EACF,OAAO,KAAK,QAAQA,EAAW,EAAI,EAGvC,OAAOD,CACT,CAAC,CACH,CAGU,OACRb,EACA/C,EACA+D,EACM,CAGN,GAAI/D,EAAM,OACR,YAAK,KAAK,EAAI,EACP+D,EAAQC,GAAmB,IAAI,CAAC,EAIzC,IAAMC,EAAY,CAAC,KAAG,IAAIlB,EAAM,EAAE,EAG5BmB,EAAc,CAAC,KAAG,IAAInB,EAAM,IAAI,EAItC,GAAIkB,GAAaC,EACf,GAAIlE,EAAM,OAAS,KAAK,UACtB,KAAK,UAAYA,EAAM,WAEvB,QAAO+D,EAAQC,GAAmB,IAAI,CAAC,EAI3C,GAAM,CAAE,IAAAnB,EAAK,aAAAI,EAAc,UAAWzC,CAAK,EAAI,KACzC,CAAE,GAAI2D,EAAQ,KAAMC,CAAS,EAAI5D,EACnC,CAAE,GAAAK,EAAKsD,EAAQ,KAAAlD,EAAOmD,CAAS,EAAIrB,EAInCmB,GAAe,CAACD,IAAc,CAACjE,EAAM,SAAW,KAAG,IAAIa,CAAE,KAC3DA,EAAKI,GAIHjB,EAAM,UAAS,CAACa,EAAII,CAAI,EAAI,CAACA,EAAMJ,CAAE,GAGzC,IAAMwD,EAAiB,IAAC,WAAQpD,EAAMmD,CAAQ,EAE1CC,IACF7D,EAAK,KAAOS,GAIdA,KAAO,iBAAcA,CAAI,EAGzB,IAAMqD,EAAe,IAAC,WAAQzD,EAAIsD,CAAM,EAEpCG,GACF,KAAK,OAAOzD,CAAE,EAIhB,IAAM0D,EAAazB,GAAU9C,EAAM,EAAE,EAE/B,CAAE,OAAAU,CAAO,EAAIF,EACb,CAAE,MAAAa,EAAO,SAAAF,CAAS,EAAIT,GAGxBuD,GAAaC,KACfxD,EAAO,SAAW,GAKhBV,EAAM,QAAU,CAACuE,GACnBC,GACE9D,EACA+D,EAASzE,EAAM,OAAQ6C,CAAI,EAE3B7C,EAAM,SAAWiD,EAAa,OAC1BwB,EAASxB,EAAa,OAAQJ,CAAI,EAClC,MACN,EAKF,IAAI1C,KAAO,eAAY,IAAI,EAC3B,GAAI,CAACA,GAAQ,KAAG,IAAIU,CAAE,EACpB,OAAOkD,EAAQL,EAAkB,KAAM,EAAI,CAAC,EAI9C,IAAMgB,EAIJ,KAAG,IAAI1E,EAAM,KAAK,EACdkE,GAAe,CAAClE,EAAM,QACtB,CAAC,KAAG,IAAIiB,CAAI,GAAK0D,GAAU3E,EAAM,MAAO6C,CAAG,EAG3CP,EAAQoC,EAASzD,EAAa,KAAK,IAAI,EAGvC2D,EAAOC,GAAiBhE,CAAE,EAG1BiE,EAAe,KAAG,IAAIF,CAAI,GAAK,KAAG,IAAIA,CAAI,MAAK,oBAAiBA,CAAI,EAGpEG,EACJ,CAACR,IACA,CAACO,GACAH,GAAU1B,EAAa,WAAajD,EAAM,UAAW6C,CAAG,GAE5D,GAAIyB,EAAc,CAChB,IAAMU,KAAW,mBAAgBnE,CAAE,EACnC,GAAImE,IAAa7E,EAAK,YACpB,GAAI4E,EACF5E,EAAO,KAAK,KAAKyE,CAAI,MAErB,OAAM,MACJ,0BAA0BzE,EAAK,YAAY,YAAY6E,EAAS,iCAClE,EAKN,IAAMC,EAAW9E,EAAK,YAKlB+E,KAAU,iBAAcrE,CAAE,EAC1BC,GAAW,GAEf,GAAI,CAACoE,EAAS,CAEZ,IAAMC,EAAkBT,GAAU,CAACtE,GAAY,IAAI,GAAKiE,GAIpDC,GAAgBa,KAClBrE,MAAW,WAAQ+D,GAAYvC,CAAK,EAAGsC,CAAI,EAC3CM,EAAU,CAACpE,KAKV,IAAC,WAAQN,EAAK,UAAWuE,CAAS,GAAK,CAACA,GACzC,IAAC,WAAQrE,EAAO,MAAOW,CAAK,GAC5B,IAAC,WAAQX,EAAO,SAAUS,CAAQ,KAElC+D,EAAU,IAiBd,GAZIpE,IAAYb,GAAY,IAAI,IAG1BO,EAAK,SAAW,CAACkE,EACnBQ,EAAU,GAGFA,GACR,KAAK,MAAMf,CAAM,GAIjB,CAACI,KAGCW,MAAW,iBAAcf,CAAM,KACjC3D,EAAK,OAASL,EAAK,WAAW,EAC9BK,EAAK,YAAW,iBAAcK,CAAE,EAC5B,KACAoE,GAAY,iBACV,CAAC,CAAC,KACF,WAAQL,CAAI,GAGhBpE,EAAK,WAAauE,IACpBvE,EAAK,UAAYuE,EAGb,CAACA,GAAa,CAACL,GACjB,KAAK,KAAKP,CAAM,GAIhBe,GAAS,CACX,GAAM,CAAE,OAAAE,CAAO,EAAI5E,KAGnB,QAAK6E,GAAeC,GAAQjC,GAAc,KAAMrD,EAAOsF,CAAI,CAAC,EAE5D,IAAM1B,EAASF,EAAkB,KAAMC,GAAc,KAAMQ,CAAM,CAAC,KAClE,cAAW,KAAK,cAAeP,CAAM,EACrC,KAAK,cAAc,IAAIG,CAAO,EAE1BvD,EAAK,SACP,MAAI,eAAe,IAAM,CAEvBA,EAAK,QAAU,CAACkE,EAGhBU,IAASxB,EAAQ,IAAI,EAIjBc,EACFD,EAASxB,EAAa,OAAQW,CAAM,EAMpCpD,EAAK,UAAUoD,EAAQ,IAAI,CAE/B,CAAC,EAIHc,GACF,KAAK,KAAKpC,CAAK,EAGbiC,EACFR,EAAQwB,GAASvF,EAAM,GAAIA,EAAO,KAAK,OAAQ,IAAI,CAAC,EAI7CkF,EACP,KAAK,OAAO,EAKLjF,GAAY,IAAI,GAAK,CAACqE,EAC7B,KAAK,cAAc,IAAIP,CAAO,EAK9BA,EAAQyB,GAAclD,CAAK,CAAC,CAEhC,CAGU,OAAOA,EAA0B,CACzC,IAAM9B,EAAO,KAAK,UACd8B,IAAU9B,EAAK,QACb,qBAAkB,IAAI,GACxB,KAAK,QAAQ,EAEfA,EAAK,GAAK8B,KACN,qBAAkB,IAAI,GACxB,KAAK,QAAQ,EAGnB,CAEU,SAAU,CAClB,IAAImD,EAAW,EAET,CAAE,GAAA5E,CAAG,EAAI,KAAK,aAChB,iBAAcA,CAAE,OAClB,oBAAiBA,EAAI,IAAI,EACrB6E,GAAa7E,CAAE,IACjB4E,EAAW5E,EAAG,SAAW,IAI7B,KAAK,SAAW4E,CAClB,CAEU,SAAU,CAClB,GAAM,CAAE,GAAA5E,CAAG,EAAI,KAAK,aAChB,iBAAcA,CAAE,MAClB,uBAAoBA,EAAI,IAAI,CAEhC,CAMU,KAAK8E,EAAwBrF,EAAO,GAA4B,CACxE,IAAMgC,KAAQ,iBAAcqD,CAAG,EAC/B,GAAI,CAAC,KAAG,IAAIrD,CAAK,EAAG,CAClB,IAAMsD,KAAU,eAAY,IAAI,EAChC,GAAI,CAACA,GAAW,IAAC,WAAQtD,EAAOsD,EAAQ,SAAS,CAAC,EAAG,CAEnD,IAAMZ,KAAW,mBAAgB1C,CAAK,EAClC,CAACsD,GAAWA,EAAQ,aAAeZ,KACrC,eAAY,KAAMA,EAAS,OAAO1C,CAAK,CAAC,EAExCsD,EAAQ,SAAStD,CAAK,EAGpBsD,GACF,MAAI,eAAe,IAAM,CACvB,KAAK,UAAUtD,EAAOhC,CAAI,CAC5B,CAAC,GAIP,SAAO,eAAY,IAAI,CACzB,CAEU,UAAW,CACnB,IAAME,EAAO,KAAK,UACbA,EAAK,UACRA,EAAK,QAAU,GACf8C,GACE,KACA,UACAI,EAAkB,KAAMC,GAAc,KAAMnD,EAAK,EAAE,CAAC,EACpD,IACF,EAEJ,CAEU,UAAU8B,EAAUhC,EAAgB,CACvCA,IACH,KAAK,SAAS,EACdmE,EAAS,KAAK,UAAU,SAAUnC,EAAO,IAAI,GAE/CmC,EAAS,KAAK,aAAa,SAAUnC,EAAO,IAAI,EAChD,MAAM,UAAUA,EAAOhC,CAAI,CAC7B,CAKU,QAAS,CACjB,IAAME,EAAO,KAAK,aAGlB,eAAY,IAAI,EAAG,SAAM,iBAAcA,EAAK,EAAE,CAAC,EAG1CA,EAAK,YACRA,EAAK,WAAaA,EAAK,OAAO,IAAIL,GAAQA,EAAK,YAAY,GAGxDF,GAAY,IAAI,IACnB4F,GAAa,KAAM,EAAI,EAClB3F,GAAS,IAAI,GAChB,KAAK,QAAQ,EAGnB,CAEU,SAAU,CAEd,EAAA4F,QAAE,cACJ,KAAK,OAAO,EAEZ,YAAU,MAAM,IAAI,CAExB,CAOU,MAAMlB,EAAYlC,EAAkB,CAC5C,GAAIzC,GAAY,IAAI,EAAG,CACrB4F,GAAa,KAAM,EAAK,EAExB,IAAMrF,EAAO,KAAK,aAClB,QAAKA,EAAK,OAAQL,GAAQ,CACxBA,EAAK,KAAO,EACd,CAAC,EAKGK,EAAK,WACPA,EAAK,SAAWA,EAAK,QAAUA,EAAK,SAAW,WAGjD,sBAAmB,KAAM,CACvB,KAAM,OACN,OAAQ,IACV,CAAC,EAED,IAAMoD,EAASlB,EACXsB,GAAmB,KAAK,IAAI,CAAC,EAC7BN,EAAkB,KAAK,IAAI,EAAGC,GAAc,KAAMiB,GAAQpE,EAAK,EAAE,CAAC,KAEtE,cAAW,KAAK,cAAeoD,CAAM,EACjCpD,EAAK,UACPA,EAAK,QAAU,GACf8C,GAAU,KAAM,SAAUM,EAAQ,IAAI,GAG5C,CACF,EAGA,SAASD,GAAiBoC,EAAwBlF,EAAuB,CACvE,IAAM+D,EAAOC,GAAYhE,CAAE,EACrByB,EAAQuC,GAAYkB,EAAO,IAAI,CAAC,EACtC,SAAO,WAAQzD,EAAOsC,CAAI,CAC5B,CAEO,SAASd,GACd9D,EACAgG,EAAOhG,EAAM,KACba,EAAKb,EAAM,GACI,CACf,IAAMiG,EAAUxB,EAASuB,CAAI,EAC7B,GAAIC,EAAS,CACX,IAAMC,EAAYD,IAAY,IAAQE,GAAQF,CAAO,EAC/CG,GAAWF,GAAalG,GAAO,QAC/B0E,EAAQ,CAACwB,GAAaA,EAAU,MACtC,OAAOG,GAAa,CAClB,GAAGrG,EACH,KAAAgG,EAGA,QAAS,GAGT,MAAO,OAKP,GAAI,CAACI,GAAWtD,GAAUjC,CAAE,EAAIA,EAAK,OAGrC,KAAM6D,EAAQ1E,EAAM,KAAO,OAC3B,MAAA0E,EAIA,GAAGwB,CACL,CAAC,EAEL,CASO,SAASG,GAAarG,EAAY,CACvC,GAAM,CAAE,GAAAa,EAAI,KAAAI,CAAK,EAAKjB,EAAQmG,GAAQnG,CAAK,EAGrCsG,EAAO,IAAI,IAEjB,OAAI,KAAG,IAAIzF,CAAE,GAAG0F,GAAY1F,EAAIyF,CAAI,EAChC,KAAG,IAAIrF,CAAI,GAAGsF,GAAYtF,EAAMqF,CAAI,EAGxCtG,EAAM,KAAOsG,EAAK,KAAO,MAAM,KAAKA,CAAI,EAAI,KAErCtG,CACT,CAKO,SAASwG,GAAcxG,EAAY,CACxC,IAAMyG,EAASJ,GAAarG,CAAK,EACjC,OAAI,KAAG,IAAIyG,EAAO,OAAO,IACvBA,EAAO,QAAUvD,GAAgBuD,CAAM,GAElCA,CACT,CAGA,SAASF,GAAYG,EAAgBJ,EAAmB,IACtD,YAASI,EAAQ,CAACpE,EAAOO,IAAQP,GAAS,MAAQgE,EAAK,IAAIzD,CAAU,CAAC,CACxE,CAGA,IAAMwC,GAAgB,CACpB,UACA,SACA,WACA,UACA,UACF,EAEA,SAAShC,GACP0C,EACA/F,EACAsF,EACA,CACAS,EAAO,UAAUT,CAAI,EACnBtF,EAAMsF,CAAI,IAAMqB,GAAe3G,EAAOsF,CAAI,EACtClC,GAAiBpD,EAAMsF,CAAI,EAAGS,EAAO,GAAG,EACxC,MACR,CAOA,SAASzC,GACPyC,EACAT,KACGsB,EACH,CACAb,EAAO,UAAUT,CAAI,IAAI,GAAIsB,CAAmB,EAChDb,EAAO,aAAaT,CAAI,IAAI,GAAIsB,CAAmB,CACrD,CSnnCA,IAAAC,EAWO,gCAuBP,IAAMC,GAAiB,CAAC,UAAW,WAAY,QAAQ,EAEnDC,GAAS,EAWAC,GAAN,KAAgD,CA2DrD,YACEC,EACAC,EACA,CA7DF,KAAS,GAAKH,KAGd,aAA+B,CAAC,EAGhC,WAAgC,CAAC,EAejC,KAAU,aAAe,EAGzB,KAAU,QAAU,IAAI,IAGxB,KAAU,SAAW,IAAI,IAGzB,KAAU,SAAW,GAKrB,KAAU,OAA8B,CACtC,OAAQ,GACR,WAAY,IAAI,IAChB,YAAa,IAAI,IACjB,SAAU,IAAI,GAChB,EAGA,KAAU,QAAU,CAClB,QAAS,IAAI,IAIb,SAAU,IAAI,IAId,OAAQ,IAAI,GAId,EAME,KAAK,SAAW,KAAK,SAAS,KAAK,IAAI,EACnCG,IACF,KAAK,OAASA,GAEZD,GACF,KAAK,MAAM,CAAE,QAAS,GAAM,GAAGA,CAAM,CAAC,CAE1C,CAMA,IAAI,MAAO,CACT,MACE,CAAC,KAAK,OAAO,SACb,OAAO,OAAO,KAAK,OAA8B,EAAE,MAAME,GAChDA,EAAO,MAAQ,CAACA,EAAO,WAAa,CAACA,EAAO,QACpD,CAEL,CAEA,IAAI,MAAO,CACT,OAAO,KAAK,KACd,CAEA,IAAI,KAAKC,EAAM,CACb,KAAK,MAAQA,CACf,CAGA,KAA4B,CAC1B,IAAMC,EAAc,CAAC,EACrB,YAAK,KAAK,CAACF,EAAQG,IAASD,EAAOC,CAAG,EAAIH,EAAO,IAAI,CAAE,EAChDE,CACT,CAGA,IAAIA,EAAwB,CAC1B,QAAWC,KAAOD,EAAQ,CACxB,IAAME,EAAQF,EAAOC,CAAG,EACnB,KAAG,IAAIC,CAAK,GACf,KAAK,QAAQD,CAAG,EAAE,IAAIC,CAAK,EAGjC,CAGA,OAAON,EAAwC,CAC7C,OAAIA,GACF,KAAK,MAAM,KAAKO,GAAaP,CAAK,CAAC,EAE9B,IACT,CASA,MAAMA,EAAsE,CAC1E,GAAI,CAAE,MAAAQ,CAAM,EAAI,KAOhB,OANIR,EACFQ,KAAQ,WAAaR,CAAK,EAAE,IAAIO,EAAY,EAE5C,KAAK,MAAQ,CAAC,EAGZ,KAAK,OACA,KAAK,OAAO,KAAMC,CAAK,GAGhCC,GAAY,KAAMD,CAAK,EAChBE,GAAiB,KAAMF,CAAK,EACrC,CAeA,KAAKG,EAAmCC,EAA0B,CAIhE,GAHID,IAAQ,CAAC,CAACA,IACZC,EAAOD,GAELC,EAAM,CACR,IAAMC,EAAU,KAAK,WACrB,WAAK,WAAQD,CAAI,EAAeP,GAAOQ,EAAQR,CAAG,EAAE,KAAK,CAAC,CAACM,CAAG,CAAC,OAE/DG,GAAU,KAAK,OAAQ,KAAK,YAAY,EACxC,KAAK,KAAKZ,GAAUA,EAAO,KAAK,CAAC,CAACS,CAAG,CAAC,EAExC,OAAO,IACT,CAGA,MAAMC,EAA0B,CAC9B,GAAI,KAAG,IAAIA,CAAI,EACb,KAAK,MAAM,CAAE,MAAO,EAAK,CAAC,MACrB,CACL,IAAMC,EAAU,KAAK,WACrB,WAAK,WAAQD,CAAI,EAAeP,GAAOQ,EAAQR,CAAG,EAAE,MAAM,CAAC,EAE7D,OAAO,IACT,CAGA,OAAOO,EAA0B,CAC/B,GAAI,KAAG,IAAIA,CAAI,EACb,KAAK,MAAM,CAAE,MAAO,EAAM,CAAC,MACtB,CACL,IAAMC,EAAU,KAAK,WACrB,WAAK,WAAQD,CAAI,EAAeP,GAAOQ,EAAQR,CAAG,EAAE,OAAO,CAAC,EAE9D,OAAO,IACT,CAGA,KAAKU,EAAsD,IACzD,YAAS,KAAK,QAASA,CAAe,CACxC,CAGU,UAAW,CACnB,GAAM,CAAE,QAAAC,EAAS,SAAAC,EAAU,OAAAC,CAAO,EAAI,KAAK,QAErCC,EAAS,KAAK,QAAQ,KAAO,EAC7BC,EAAU,KAAK,SAAS,KAAO,GAEhCD,GAAU,CAAC,KAAK,UAAcC,GAAW,CAAC,KAAK,YAClD,KAAK,SAAW,MAChB,SAAMJ,EAAS,CAAC,CAACA,EAASK,CAAM,IAAM,CACpCA,EAAO,MAAQ,KAAK,IAAI,EACxBL,EAAQK,EAAQ,KAAM,KAAK,KAAK,CAClC,CAAC,GAGH,IAAMC,EAAO,CAACH,GAAU,KAAK,SACvBf,EAASgB,GAAYE,GAAQJ,EAAO,KAAQ,KAAK,IAAI,EAAI,KAE3DE,GAAWH,EAAS,SACtB,SAAMA,EAAU,CAAC,CAACA,EAAUI,CAAM,IAAM,CACtCA,EAAO,MAAQjB,EACfa,EAASI,EAAQ,KAAM,KAAK,KAAK,CACnC,CAAC,EAICC,IACF,KAAK,SAAW,MAChB,SAAMJ,EAAQ,CAAC,CAACA,EAAQG,CAAM,IAAM,CAClCA,EAAO,MAAQjB,EACfc,EAAOG,EAAQ,KAAM,KAAK,KAAK,CACjC,CAAC,EAEL,CAGA,cAAcE,EAAyB,CACrC,GAAIA,EAAM,MAAQ,SAChB,KAAK,SAAS,IAAIA,EAAM,MAAM,EACzBA,EAAM,MACT,KAAK,QAAQ,IAAIA,EAAM,MAAM,UAEtBA,EAAM,MAAQ,OACvB,KAAK,QAAQ,OAAOA,EAAM,MAAM,MAG7B,QACL,MAAI,QAAQ,KAAK,QAAQ,CAC3B,CACF,EAKO,SAASb,GACdc,EACAhB,EACA,CACA,OAAO,QAAQ,IAAIA,EAAM,IAAIR,GAASyB,GAAYD,EAAMxB,CAAK,CAAC,CAAC,EAAE,KAC/D0B,GAAWC,GAAkBH,EAAME,CAAO,CAC5C,CACF,CAWA,eAAsBD,GACpBD,EACAxB,EACA4B,EACa,CACb,GAAM,CAAE,KAAAhB,EAAM,GAAAiB,EAAI,KAAAC,EAAM,KAAAC,EAAM,OAAAb,EAAQ,UAAAc,CAAU,EAAIhC,EAC9CiC,EAAW,KAAG,IAAIjC,EAAM,OAAO,GAAKA,EAAM,QAI5C+B,IACF/B,EAAM,KAAO,IAIX6B,IAAO,KAAO7B,EAAM,GAAK,MACzB8B,IAAS,KAAO9B,EAAM,KAAO,MAEjC,IAAMkC,EAAU,KAAG,IAAIL,CAAE,GAAK,KAAG,IAAIA,CAAE,EAAIA,EAAK,OAC5CK,GACFlC,EAAM,GAAK,OACXA,EAAM,OAAS,OACXiC,IACFA,EAAS,OAAS,YAOpB,QAAKpC,GAAgBQ,GAAO,CAC1B,IAAM8B,EAAenC,EAAMK,CAAG,EAC9B,GAAI,KAAG,IAAI8B,CAAO,EAAG,CACnB,IAAM3B,EAAQgB,EAAK,QAAWnB,CAAG,EACjCL,EAAMK,CAAG,EAAK,CAAC,CAAE,SAAA+B,EAAU,UAAAC,CAAU,IAAuB,CAC1D,IAAMhB,EAASb,EAAM,IAAI2B,CAAO,EAC5Bd,GACGe,IAAUf,EAAO,SAAW,IAC7BgB,IAAWhB,EAAO,UAAY,KAGlCb,EAAM,IAAI2B,EAAS,CACjB,MAAO,KACP,SAAUC,GAAY,GACtB,UAAWC,GAAa,EAC1B,CAAC,CAEL,EAGIJ,IACFA,EAAS5B,CAAG,EAAIL,EAAMK,CAAG,GAG/B,CAAC,EAGH,IAAMiC,EAAQd,EAAK,OAGfxB,EAAM,QAAU,CAACsC,EAAM,QACzBA,EAAM,OAAStC,EAAM,SACrB,cAAWA,EAAM,MAAQsC,EAAM,WAAaA,EAAM,WAAW,GAGtDA,EAAM,SACbtC,EAAM,MAAQ,IAGhB,IAAMuC,GAA2B3B,GAAQ,OAAO,KAAKY,EAAK,OAAO,GAAG,IAAInB,GACtEmB,EAAK,QAAQnB,CAAG,EAAG,MAAML,CAAY,CACvC,EAEMwC,EACJxC,EAAM,SAAW,IAAQyC,GAAezC,EAAO,QAAQ,IAAM,IAE3DkC,GAAYM,GAAUF,EAAM,UAC9BC,EAAS,KACPG,GAAc,EAAElB,EAAK,aAAiB,CACpC,MAAAxB,EACA,MAAAsC,EACA,QAAS,CACP,MAAO,OACP,OAAQ,OACR,MAAMtC,EAAO2C,EAAS,CAChBH,GACF1B,GAAUwB,EAAOd,EAAK,YAAe,EACrCmB,EAAQC,GAAmBpB,CAAI,CAAC,IAEhCxB,EAAM,OAASkB,EACfyB,EACEE,GACEX,EACAlC,EACAsC,EACAd,CACF,CACF,EAEJ,CACF,CACF,CAAC,CACH,EAKEc,EAAM,QAGR,MAAM,IAAI,QAAcQ,GAAU,CAChCR,EAAM,YAAY,IAAIQ,CAAM,CAC9B,CAAC,EAGH,IAAMzB,EAASM,GAAuBH,EAAM,MAAM,QAAQ,IAAIe,CAAQ,CAAC,EACvE,GAAIR,GAAQV,EAAO,UAAY,EAAEO,GAAUP,EAAO,MAAO,CACvD,IAAM0B,EAAYC,GAAiBhD,EAAO+B,EAAMF,CAAE,EAClD,GAAIkB,EACF,OAAAtC,GAAYe,EAAM,CAACuB,CAAS,CAAC,EACtBtB,GAAYD,EAAMuB,EAAW,EAAI,EAG5C,OAAIf,GACF,MAAI,eAAe,IAAMA,EAAUX,EAAQG,EAAMA,EAAK,IAAI,CAAC,EAEtDH,CACT,CAUO,SAAS4B,GACdzB,EACAxB,EACA,CACA,IAAMa,EAAU,CAAE,GAAGW,EAAK,OAAQ,EAClC,OAAIxB,MACF,WAAK,WAAQA,CAAK,EAAIA,GAAe,CAC/B,KAAG,IAAIA,EAAM,IAAI,IACnBA,EAAQO,GAAaP,CAAK,GAEvB,KAAG,IAAIA,EAAM,EAAE,IAElBA,EAAQ,CAAE,GAAGA,EAAO,GAAI,MAAU,GAEpCkD,GAAerC,EAAgBb,EAAOK,GAC7B8C,GAAa9C,CAAG,CACxB,CACH,CAAC,EAEH+C,GAAW5B,EAAMX,CAAO,EACjBA,CACT,CAMO,SAASuC,GACd5B,EACAX,EACA,IACA,YAASA,EAAS,CAACX,EAAQG,IAAQ,CAC5BmB,EAAK,QAAQnB,CAAG,IACnBmB,EAAK,QAAQnB,CAAG,EAAIH,KACpB,oBAAiBA,EAAQsB,CAAI,EAEjC,CAAC,CACH,CAEA,SAAS2B,GAAa9C,EAAagD,EAA4C,CAC7E,IAAMnD,EAAS,IAAIoD,GACnB,OAAApD,EAAO,IAAMG,EACTgD,MACF,oBAAiBnD,EAAQmD,CAAQ,EAE5BnD,CACT,CAQA,SAASgD,GACPrC,EACAb,EACAuD,EACA,CACIvD,EAAM,SACR,QAAKA,EAAM,KAAMK,GAAO,EACPQ,EAAQR,CAAG,IAAMQ,EAAQR,CAAG,EAAIkD,EAAOlD,CAAG,IAClD,aAAgBL,CAAK,CAC9B,CAAC,CAEL,CAQA,SAASS,GAAYe,EAAuBhB,EAAkC,IAC5E,QAAKA,EAAOR,GAAS,CACnBkD,GAAe1B,EAAK,QAASxB,EAAOK,GAC3B8C,GAAa9C,EAAKmB,CAAI,CAC9B,CACH,CAAC,CACH,CCnhBA,IAAAgC,GAAuB,qBACvBC,GAA8C,iBAC9CC,GAA2B,gCAadC,GAAgB,CAAC,CAC5B,SAAAC,EACA,GAAGC,CACL,IAAwC,CACtC,IAAMC,KAAY,eAAWC,EAAG,EAG1BC,EAAQH,EAAM,OAAS,CAAC,CAACC,EAAU,MACvCG,EAAYJ,EAAM,WAAa,CAAC,CAACC,EAAU,UAG7CD,KAAQ,eAAW,KAAO,CAAE,MAAAG,EAAO,UAAAC,CAAU,GAAI,CAACD,EAAOC,CAAS,CAAC,EAEnE,GAAM,CAAE,SAAAC,CAAS,EAAIH,GACrB,OAAO,iBAACG,EAAA,CAAS,MAAOL,GAAQD,CAAS,CAC3C,EAEMG,GAAMI,GAAYR,GAAe,CAAC,CAAkB,EAG1DA,GAAc,SAAWI,GAAI,SAC7BJ,GAAc,SAAWI,GAAI,SAG7B,SAASI,GAAeC,EAAaC,EAA2B,CAC9D,cAAO,OAAOD,EAAc,iBAAcC,CAAI,CAAC,EAC/CD,EAAO,SAAS,SAAWA,EAC3BA,EAAO,SAAS,SAAWA,EACpBA,CACT,CC5CA,IAAAE,EAA8C,gCA8EjCC,GAAY,IAEA,CACvB,IAAMC,EAA+B,CAAC,EAEhCD,EAA8B,SAAUE,EAAO,IACnD,uBAAoB,EAEpB,IAAMC,EAAyB,CAAC,EAEhC,iBAAKF,EAAS,CAACG,EAAMC,IAAM,CACzB,GAAI,KAAG,IAAIH,CAAK,EACdC,EAAQ,KAAKC,EAAK,MAAM,CAAC,MACpB,CACL,IAAME,EAASC,EAAUL,EAAOE,EAAMC,CAAC,EACnCC,GACFH,EAAQ,KAAKC,EAAK,MAAME,CAAM,CAAC,EAGrC,CAAC,EAEMH,CACT,EAEAH,EAAU,QAAUC,EAGpBD,EAAU,IAAM,SAAUI,EAAyB,CAC5CH,EAAQ,SAASG,CAAI,GACxBH,EAAQ,KAAKG,CAAI,CAErB,EAGAJ,EAAU,OAAS,SAAUI,EAAyB,CACpD,IAAMC,EAAIJ,EAAQ,QAAQG,CAAI,EAC1B,CAACC,GAAGJ,EAAQ,OAAOI,EAAG,CAAC,CAC7B,EAGAL,EAAU,MAAQ,UAAY,CAC5B,iBAAKC,EAASG,GAAQA,EAAK,MAAM,GAAG,SAAS,CAAC,EACvC,IACT,EAGAJ,EAAU,OAAS,UAAY,CAC7B,iBAAKC,EAASG,GAAQA,EAAK,OAAO,GAAG,SAAS,CAAC,EACxC,IACT,EAGAJ,EAAU,IAAM,SACdQ,EAGA,IACA,QAAKP,EAAS,CAACG,EAAMC,IAAM,CACzB,IAAMC,EAAS,KAAG,IAAIE,CAAM,EAAIA,EAAOH,EAAGD,CAAI,EAAII,EAC9CF,GACFF,EAAK,IAAIE,CAAM,CAEnB,CAAC,CACH,EAEAN,EAAU,MAAQ,SAAUE,EAA4C,CACtE,IAAMC,EAAyB,CAAC,EAEhC,iBAAKF,EAAS,CAACG,EAAMC,IAAM,CACzB,GAAI,KAAG,IAAIH,CAAK,EACdC,EAAQ,KAAKC,EAAK,MAAM,CAAC,MACpB,CACL,IAAME,EAAS,KAAK,UAAUJ,EAAOE,EAAMC,CAAC,EACxCC,GACFH,EAAQ,KAAKC,EAAK,MAAME,CAAM,CAAC,EAGrC,CAAC,EAEMH,CACT,EAGAH,EAAU,KAAO,UAAY,CAC3B,iBAAKC,EAASG,GAAQA,EAAK,KAAK,GAAG,SAAS,CAAC,EACtC,IACT,EAEAJ,EAAU,OAAS,SAAUE,EAA2C,CACtE,iBAAKD,EAAS,CAACG,EAAMC,IAAMD,EAAK,OAAO,KAAK,UAAUF,EAAOE,EAAMC,CAAC,CAAC,CAAC,EAC/D,IACT,EAGA,IAAME,EAAY,SAChBE,EACAL,EACAM,EACA,CACA,OAAO,KAAG,IAAID,CAAG,EAAIA,EAAIC,EAAON,CAAI,EAAIK,CAC1C,EAEA,OAAAT,EAAU,UAAYO,EAEfP,CACT,EZ9GO,SAASW,GACdC,EACAC,EACAC,EACK,CACL,IAAMC,EAAU,KAAG,IAAIF,CAAK,GAAKA,EAC7BE,GAAW,CAACD,IAAMA,EAAO,CAAC,GAG9B,IAAME,KAAM,WACV,IAAOD,GAAW,UAAU,QAAU,EAAIE,GAAU,EAAI,OACxD,CAAC,CACH,EAYMC,KAAW,UAAO,CAAC,EACnBC,KAAc,kBAAe,EAG7BC,KAAQ,WACZ,KAAc,CACZ,MAAO,CAAC,EACR,MAAO,CAAC,EACR,MAAMC,EAAMC,EAAS,CACnB,IAAMC,EAAUC,GAAWH,EAAMC,CAAO,EASxC,OAJEJ,EAAS,QAAU,GACnB,CAACE,EAAM,MAAM,QACb,CAAC,OAAO,KAAKG,CAAO,EAAE,KAAKE,GAAO,CAACJ,EAAK,QAAQI,CAAG,CAAC,EAGlDC,GAAiBL,EAAMC,CAAO,EAC9B,IAAI,QAAaK,GAAW,CAC1BC,GAAWP,EAAME,CAAO,EACxBH,EAAM,MAAM,KAAK,IAAM,CACrBO,EAAQD,GAAiBL,EAAMC,CAAO,CAAC,CACzC,CAAC,EACDH,EAAY,CACd,CAAC,CACP,CACF,GACA,CAAC,CACH,EAEMU,KAAQ,UAAO,CAAC,GAAGT,EAAM,KAAK,CAAC,EAC/BE,EAAiB,CAAC,EAGlBQ,KAAa,WAAQlB,CAAM,GAAK,KAItC,WAAQ,IAAM,IAEZ,QAAKiB,EAAM,QAAQ,MAAMjB,EAAQkB,CAAU,EAAGT,GAAQ,CACpDU,GAAWV,EAAML,CAAG,EACpBK,EAAK,KAAK,EAAI,CAChB,CAAC,EACDQ,EAAM,QAAQ,OAASjB,EAEvBoB,EAAeF,EAAYlB,CAAM,CACnC,EAAG,CAACA,CAAM,CAAC,KAGX,WAAQ,IAAM,CACZoB,EAAe,EAAG,KAAK,IAAIF,EAAYlB,CAAM,CAAC,CAChD,EAAGE,CAAI,EAGP,SAASkB,EAAeC,EAAoBC,EAAkB,CAC5D,QAASC,EAAIF,EAAYE,EAAID,EAAUC,IAAK,CAC1C,IAAMd,EACJQ,EAAM,QAAQM,CAAC,IACdN,EAAM,QAAQM,CAAC,EAAI,IAAIC,GAAW,KAAMhB,EAAM,KAAK,GAEhDiB,EAA8BtB,EAChCA,EAAQoB,EAAGd,CAAI,EACdR,EAAcsB,CAAC,EAEhBE,IACFf,EAAQa,CAAC,EAAIG,GAAcD,CAAM,GAGvC,CAKA,IAAMd,EAAUM,EAAM,QAAQ,IAAI,CAACR,EAAMc,IAAMX,GAAWH,EAAMC,EAAQa,CAAC,CAAC,CAAC,EAErEI,KAAU,cAAWC,EAAa,EAClCC,KAAc,WAAQF,CAAO,EAC7BG,EAAaH,IAAYE,GAAeE,GAASJ,CAAO,KAE9D,6BAA0B,IAAM,CAC9BrB,EAAS,UAGTE,EAAM,MAAQS,EAAM,QAGpB,GAAM,CAAE,MAAAe,CAAM,EAAIxB,EACdwB,EAAM,SACRxB,EAAM,MAAQ,CAAC,KACf,QAAKwB,EAAOC,GAAMA,EAAG,CAAC,MAIxB,QAAKhB,EAAM,QAAS,CAACR,EAAMc,IAAM,CAE/BnB,GAAK,IAAIK,CAAI,EAGTqB,GACFrB,EAAK,MAAM,CAAE,QAASkB,CAAQ,CAAC,EAIjC,IAAMF,EAASf,EAAQa,CAAC,EACpBE,IAEFS,GAAWzB,EAAMgB,EAAO,GAAG,EAIvBhB,EAAK,IACPA,EAAK,MAAM,KAAKgB,CAAM,EAEtBhB,EAAK,MAAMgB,CAAM,EAGvB,CAAC,CACH,CAAC,KAGD,WAAQ,IAAM,IAAM,IAClB,QAAKjB,EAAM,MAAOC,GAAQA,EAAK,KAAK,EAAI,CAAC,CAC3C,CAAC,EAID,IAAM0B,EAASxB,EAAQ,IAAIyB,IAAM,CAAE,GAAGA,CAAE,EAAE,EAE1C,OAAOhC,EAAM,CAAC+B,EAAQ/B,CAAG,EAAI+B,CAC/B,CDvKO,SAASE,GAAUC,EAAYC,EAAuB,CAC3D,IAAMC,EAAO,MAAG,IAAIF,CAAK,EACnB,CAAC,CAACG,CAAM,EAAGC,CAAG,EAAIC,GACtB,EACAH,EAAOF,EAAQ,CAACA,CAAK,EACrBE,EAAOD,GAAQ,CAAC,EAAIA,CACtB,EACA,OAAOC,GAAQ,UAAU,QAAU,EAAI,CAACC,EAAQC,CAAG,EAAID,CACzD,CctEA,IAAAG,GAAyB,iBAKzB,IAAMC,GAAgB,IAAMC,GAAe,EAE9BC,GAAe,OAC1B,aAASF,EAAa,EAAE,CAAC,ECR3B,IAAAG,GAAqC,gCAwB9B,IAAMC,GAAiB,CAC5BC,EACAC,IACG,CACH,IAAMC,KAAc,gBAAY,IAAM,IAAIC,GAAYH,EAASC,CAAK,CAAC,EAErE,qBAAQ,IAAM,IAAM,CAClBC,EAAY,KAAK,CACnB,CAAC,EAEMA,CACT,ECnCA,IAAAE,GAAoD,gCAiF7C,SAASC,GACdC,EACAC,EACAC,EACA,CACA,IAAMC,EAAU,MAAG,IAAIF,CAAQ,GAAKA,EAChCE,GAAW,CAACD,IAAMA,EAAO,CAAC,GAG9B,IAAIE,EAAU,GACVC,EAEEC,EAASC,GACbP,EACA,CAAC,EAAGQ,IAAS,CACX,IAAMC,EAAQN,EAAUA,EAAQ,EAAGK,CAAI,EAAIP,EAC3C,OAAAI,EAAYI,EAAM,IAClBL,EAAUA,GAAWK,EAAM,QAEpBA,CACT,EAGAP,GAAQ,CAAC,CAAC,CAAC,CACb,EAmCA,MAjCA,8BAA0B,IAAM,IAI9B,SAAKI,EAAO,CAAC,EAAE,QAAS,CAACE,EAAME,IAAM,CACnC,IAAMC,EAASL,EAAO,CAAC,EAAE,QAAQI,GAAKN,EAAU,EAAI,GAAG,EAYvD,GAPAQ,GAAWJ,EAAMH,CAAS,EAOtBG,EAAK,IAAK,CACRG,GACFH,EAAK,OAAO,CAAE,GAAIG,EAAO,OAAQ,CAAC,EAGpC,OAGEA,EACFH,EAAK,MAAM,CAAE,GAAIG,EAAO,OAAQ,CAAC,EAEjCH,EAAK,MAAM,CAEf,CAAC,CACH,EAAGN,CAAI,EAEHC,GAAW,UAAU,QAAU,EAAG,CACpC,IAAMU,EAAMR,GAAaC,EAAO,CAAC,EAEjC,OAAAO,EAAI,UAAe,CAACZ,EAAUO,EAAME,IAAM,CACxC,IAAMD,EAAQ,MAAG,IAAIR,CAAQ,EAAIA,EAASS,EAAGF,CAAI,EAAIP,EACrD,GAAIQ,EAAO,CACT,IAAME,EAASE,EAAI,QAAQH,GAAKD,EAAM,QAAU,EAAI,GAAG,EACvD,OAAIE,IAAQF,EAAM,GAAKE,EAAO,SACvBF,EAEX,EACOH,EAGT,OAAOA,EAAO,CAAC,CACjB,CC3JA,IAAAQ,GAAuB,qBACvBC,GAA4C,iBAE5CC,EAQO,gCA6DA,SAASC,GACdC,EACAC,EACAC,EACK,CACL,IAAMC,EAAU,KAAG,IAAIF,CAAK,GAAKA,EAE3B,CACJ,MAAAG,EACA,KAAAC,EACA,MAAAC,EAAQ,EACR,QAAAC,EAAU,GACV,gBAAAC,EAAkB,GAClB,YAAAC,EACA,IAAKC,EACL,OAAQC,CACV,EAA6BR,EAAUA,EAAQ,EAAIF,EAG7CW,KAAM,YACV,IAAOT,GAAW,UAAU,QAAU,EAAIU,GAAU,EAAI,OACxD,CAAC,CACH,EAGMC,KAAQ,WAAQd,CAAI,EACpBe,EAAiC,CAAC,EAGlCC,KAAkB,WAAiC,IAAI,EACvDC,EAAkBb,EAAQ,KAAOY,EAAgB,WAEvD,6BAA0B,IAAM,CAC9BA,EAAgB,QAAUD,CAC5B,CAAC,KAED,WAAQ,QASN,QAAKA,EAAaG,GAAK,CACrBN,GAAK,IAAIM,EAAE,IAAI,EACfA,EAAE,KAAK,IAAMN,CACf,CAAC,EAGM,IAAM,IACX,QAAKI,EAAgB,QAAUE,GAAK,CAC9BA,EAAE,SACJ,aAAaA,EAAE,YAAa,EAE9BC,GAAWD,EAAE,KAAMN,CAAG,EACtBM,EAAE,KAAK,KAAK,EAAI,CAClB,CAAC,CACH,EACD,EAMD,IAAME,EAAOC,GAAQP,EAAOX,EAAUA,EAAQ,EAAIF,EAAOgB,CAAe,EAGlEK,EAAWlB,GAASY,EAAgB,SAAY,CAAC,KACvD,6BAA0B,OACxB,QAAKM,EAAS,CAAC,CAAE,KAAAC,EAAM,KAAAC,EAAM,IAAAC,CAAI,IAAM,CACrCN,GAAWI,EAAMX,CAAG,EACpBc,EAASjB,EAAae,EAAMC,CAAG,CACjC,CAAC,CACH,EAGA,IAAME,EAAmB,CAAC,EA6B1B,GA5BIV,MACF,QAAKA,EAAiB,CAACC,EAAGU,IAAM,CAE1BV,EAAE,SACJ,aAAaA,EAAE,YAAa,EAC5BI,EAAQ,KAAKJ,CAAC,IAEdU,EAAID,EAAOC,CAAC,EAAIR,EAAK,QAAQF,EAAE,GAAG,EAC9B,CAACU,IAAGb,EAAYa,CAAC,EAAIV,GAE7B,CAAC,KAGH,QAAKJ,EAAO,CAACU,EAAMI,IAAM,CAClBb,EAAYa,CAAC,IAChBb,EAAYa,CAAC,EAAI,CACf,IAAKR,EAAKQ,CAAC,EACX,KAAAJ,EACA,cACA,KAAM,IAAIK,EACZ,EAEAd,EAAYa,CAAC,EAAE,KAAK,KAAOJ,EAE/B,CAAC,EAIGG,EAAO,OAAQ,CACjB,IAAIC,EAAI,GACF,CAAE,MAAAE,CAAM,EAA6B3B,EAAUA,EAAQ,EAAIF,KACjE,QAAK0B,EAAQ,CAACI,EAAUC,IAAc,CACpC,IAAMd,EAAID,EAAiBe,CAAS,EAChC,CAACD,GACHH,EAAIb,EAAY,QAAQG,CAAC,EACzBH,EAAYa,CAAC,EAAI,CAAE,GAAGV,EAAG,KAAMJ,EAAMiB,CAAQ,CAAE,GACtCD,GACTf,EAAY,OAAO,EAAEa,EAAG,EAAGV,CAAC,CAEhC,CAAC,EAGC,KAAG,IAAIb,CAAI,GACbU,EAAY,KAAK,CAACkB,EAAGC,IAAM7B,EAAK4B,EAAE,KAAMC,EAAE,IAAI,CAAC,EAIjD,IAAIC,EAAQ,CAAC7B,EAGP8B,KAAc,kBAAe,EAG7BC,EAAeC,GAAoCrC,CAAK,EAExDsC,EAAU,IAAI,IACdC,KAAqB,WAAO,IAAI,GAA8B,EAE9DC,KAAc,WAAO,EAAK,KAChC,QAAK1B,EAAa,CAACG,EAAGU,IAAM,CAC1B,IAAMH,EAAMP,EAAE,IACRwB,EAAYxB,EAAE,MAEdyB,EAA6BxC,EAAUA,EAAQ,EAAIF,EAErD2C,EACAC,EAEEC,GAAapB,EAASiB,EAAE,OAAS,EAAGlB,CAAG,EAE7C,GAAIiB,WACFE,EAAKD,EAAE,MACPE,cACK,CACL,IAAME,EAAU3B,EAAK,QAAQK,CAAG,EAAI,EACpC,GAAIiB,WACF,GAAIK,EACFH,EAAKD,EAAE,MACPE,kBACUD,EAAKD,EAAE,OACjBE,eACK,gBACE,CAACE,EACVH,EAAKD,EAAE,MACPE,cACK,QAmBT,GAdAD,EAAKlB,EAASkB,EAAI1B,EAAE,KAAMU,CAAC,EAC3BgB,EAAK,KAAG,IAAIA,CAAE,EAAII,GAAQJ,CAAE,EAAI,CAAE,GAAAA,CAAG,EAajC,CAACA,EAAG,OAAQ,CACd,IAAMK,EAAStC,GAAe0B,EAAa,OAC3CO,EAAG,OAASlB,EAASuB,EAAQ/B,EAAE,KAAMU,EAAGiB,CAAK,EAG/CV,GAAS7B,EAGT,IAAM4C,GAA0C,CAC9C,GAAGb,EAEH,MAAOS,GAAaX,EACpB,IAAKzB,EACL,UAAWiC,EAAE,UAEb,MAAO,GAEP,GAAIC,CACN,EAEA,GAAIC,YAAkC,KAAG,IAAIK,GAAQ,IAAI,EAAG,CAC1D,IAAMP,EAAIxC,EAAUA,EAAQ,EAAIF,EAI1BkD,GAAO,KAAG,IAAIR,EAAE,OAAO,GAAK1B,EAAkB0B,EAAE,KAAOA,EAAE,QAE/DO,GAAQ,KAAOxB,EAASyB,GAAMjC,EAAE,KAAMU,CAAC,EAGzC,GAAM,CAAE,UAAAwB,EAAU,EAAIF,GACtBA,GAAQ,UAAYG,GAAU,CAC5B3B,EAAS0B,GAAWC,CAAM,EAE1B,IAAMtC,GAAcC,EAAgB,QAC9BE,GAAIH,GAAY,KAAKG,IAAKA,GAAE,MAAQO,CAAG,EAC7C,GAAKP,IAID,EAAAmC,EAAO,WAAanC,GAAE,kBAWtBA,GAAE,KAAK,KAAM,CACf,IAAMoC,GAAOvC,GAAY,MAAMG,IAAKA,GAAE,KAAK,IAAI,EAC/C,GAAIA,GAAE,eAAgC,CACpC,IAAMqC,GAAS7B,EAASnB,EAASW,GAAE,IAAI,EACvC,GAAIqC,KAAW,GAAO,CACpB,IAAMC,GAAWD,KAAW,GAAO,EAAIA,GAIvC,GAHArC,GAAE,QAAU,GAGR,CAACoC,IAAQE,GAAW,EAAG,CAErBA,IAAY,aACdtC,GAAE,aAAe,WAAWkB,EAAaoB,EAAQ,GACnD,SAKFF,IAAQvC,GAAY,KAAKG,IAAKA,GAAE,OAAO,IAKzCsB,EAAmB,QAAQ,OAAOtB,EAAC,EAE/BV,IAKFiC,EAAY,QAAU,IAGxBL,EAAY,GAGlB,EAEA,IAAMqB,GAAUC,GAAWxC,EAAE,KAAMgC,EAAO,EAKtCL,aAAmCrC,EACrCgC,EAAmB,QAAQ,IAAItB,EAAG,CAAE,MAAA2B,EAAO,QAAAY,GAAS,QAAAP,EAAQ,CAAC,EAE7DX,EAAQ,IAAIrB,EAAG,CAAE,MAAA2B,EAAO,QAAAY,GAAS,QAAAP,EAAQ,CAAC,CAE9C,CAAC,EAGD,IAAMS,KAAU,eAAWC,EAAa,EAClCC,KAAc,WAAQF,CAAO,EAC7BG,GAAaH,IAAYE,GAAeE,GAASJ,CAAO,KAG9D,6BAA0B,IAAM,CAC1BG,OACF,QAAK/C,EAAaG,GAAK,CACrBA,EAAE,KAAK,MAAM,CAAE,QAASyC,CAAQ,CAAC,CACnC,CAAC,CAEL,EAAG,CAACA,CAAO,CAAC,KAEZ,QAAKpB,EAAS,CAACyB,EAAG9C,IAAM,CAMtB,GAAIsB,EAAmB,QAAQ,KAAM,CACnC,IAAMyB,EAAMlD,EAAY,UAAUmD,GAASA,EAAM,MAAQhD,EAAE,GAAG,EAC9DH,EAAY,OAAOkD,EAAK,CAAC,EAE7B,CAAC,KAED,6BACE,IAAM,IAKJ,QACEzB,EAAmB,QAAQ,KAAOA,EAAmB,QAAUD,EAC/D,CAAC,CAAE,MAAAM,EAAO,QAAAK,CAAQ,EAAGhC,IAAM,CACzB,GAAM,CAAE,KAAAK,CAAK,EAAIL,EAEjBA,EAAE,MAAQ2B,EAGVjC,GAAK,IAAIW,CAAI,EAGTuC,IAAcjB,YAChBtB,EAAK,MAAM,CAAE,QAASoC,CAAQ,CAAC,EAG7BT,IAEFiB,GAAW5C,EAAM2B,EAAQ,GAAG,GAQvB3B,EAAK,KAAOX,IAAQ,CAAC6B,EAAY,QACpClB,EAAK,OAAO2B,CAAO,GAEnB3B,EAAK,MAAM2B,CAAO,EAEdT,EAAY,UACdA,EAAY,QAAU,KAI9B,CACF,CACF,EACArC,EAAQ,OAASF,CACnB,EAEA,IAAMkE,EAAkCC,GACtC,kCACGtD,EAAY,IAAI,CAACG,EAAGU,IAAM,CACzB,GAAM,CAAE,QAAA6B,CAAQ,EAAIlB,EAAQ,IAAIrB,CAAC,GAAKA,EAAE,KAClCoD,EAAYD,EAAO,CAAE,GAAGZ,CAAQ,EAAGvC,EAAE,KAAMA,EAAGU,CAAC,EACrD,OAAO0C,GAAQA,EAAK,KAClB,iBAACA,EAAK,KAAL,CACE,GAAGA,EAAK,MACT,IAAK,KAAG,IAAIpD,EAAE,GAAG,GAAK,KAAG,IAAIA,EAAE,GAAG,EAAIA,EAAE,IAAMA,EAAE,KAAK,GACrD,IAAKoD,EAAK,IACZ,EAEAA,CAEJ,CAAC,CACH,EAGF,OAAO1D,EAAM,CAACwD,EAAmBxD,CAAG,EAAIwD,CAC1C,CAGA,IAAIG,GAAU,EAEd,SAASlD,GACPP,EACA,CAAE,IAAAW,EAAK,KAAAL,EAAOK,CAAI,EAClBR,EACgB,CAChB,GAAIG,IAAS,KAAM,CACjB,IAAMO,EAAS,IAAI,IACnB,OAAOb,EAAM,IAAIU,GAAQ,CACvB,IAAMN,EACJD,GACAA,EAAgB,KACdC,GACEA,EAAE,OAASM,GACXN,EAAE,iBACF,CAACS,EAAO,IAAIT,CAAC,CACjB,EACF,OAAIA,GACFS,EAAO,IAAIT,CAAC,EACLA,EAAE,KAEJqD,IACT,CAAC,EAEH,OAAO,KAAG,IAAInD,CAAI,EAAIN,EAAQ,KAAG,IAAIM,CAAI,EAAIN,EAAM,IAAIM,CAAI,KAAI,WAAQA,CAAI,CAC7E,CC5dA,IAAAoD,GAA0D,gCAmCnD,IAAMC,GAAY,CAAC,CACxB,UAAAC,EACA,GAAGC,CACL,EAAsB,CAAC,IAKjB,CACJ,GAAM,CAACC,EAAcC,CAAG,EAAIC,GAC1B,KAAO,CACL,QAAS,EACT,QAAS,EACT,gBAAiB,EACjB,gBAAiB,EACjB,GAAGH,CACL,GACA,CAAC,CACH,EAEA,uCAA0B,IAAM,CAC9B,IAAMI,KAAgB,aACpB,CAAC,CAAE,EAAAC,EAAG,EAAAC,CAAE,IAAM,CACZJ,EAAI,MAAM,CACR,QAASG,EAAE,QACX,gBAAiBA,EAAE,SACnB,QAASC,EAAE,QACX,gBAAiBA,EAAE,QACrB,CAAC,CACH,EACA,CAAE,UAAWP,GAAW,SAAW,MAAU,CAC/C,EAEA,MAAO,IAAM,IAIX,SAAK,OAAO,OAAOE,CAAY,EAAGM,GAASA,EAAM,KAAK,CAAC,EAEvDH,EAAc,CAChB,CACF,EAAG,CAAC,CAAC,EAEEH,CACT,EC/EA,IAAAO,GAA0D,gCAmCnD,IAAMC,GAAY,CAAC,CACxB,UAAAC,EACA,GAAGC,CACL,IAGM,CACJ,GAAM,CAACC,EAAYC,CAAG,EAAIC,GACxB,KAAO,CACL,MAAO,EACP,OAAQ,EACR,GAAGH,CACL,GACA,CAAC,CACH,EAEA,uCAA0B,IAAM,CAC9B,IAAMI,KAAgB,aACpB,CAAC,CAAE,MAAAC,EAAO,OAAAC,CAAO,IAAM,CACrBJ,EAAI,MAAM,CACR,MAAAG,EACA,OAAAC,EACA,UACEL,EAAW,MAAM,IAAI,IAAM,GAAKA,EAAW,OAAO,IAAI,IAAM,CAChE,CAAC,CACH,EACA,CAAE,UAAWF,GAAW,SAAW,MAAU,CAC/C,EAEA,MAAO,IAAM,IAIX,SAAK,OAAO,OAAOE,CAAU,EAAGM,GAASA,EAAM,KAAK,CAAC,EAErDH,EAAc,CAChB,CACF,EAAG,CAAC,CAAC,EAEEH,CACT,EC5EA,IAAAO,GAA4C,iBAC5CC,GAA8C,gCAc9C,IAAMC,GAA0B,CAC9B,IAAK,EACL,IAAK,CACP,EAcO,SAASC,GACdC,EACAC,EACA,CACA,GAAM,CAACC,EAAUC,CAAW,KAAI,aAAS,EAAK,EACxCC,KAAM,WAAiB,EAEvBC,EAAU,MAAG,IAAIL,CAAK,GAAKA,EAE3BM,EAAeD,EAAUA,EAAQ,EAAI,CAAC,EACtC,CAAE,GAAAE,EAAK,CAAC,EAAG,KAAAC,EAAO,CAAC,EAAG,GAAGC,CAAgB,EAAIH,EAE7CI,EAAwBL,EAAUJ,EAAOD,EAEzC,CAACW,EAASC,CAAG,EAAIC,GAAU,KAAO,CAAE,KAAAL,EAAM,GAAGC,CAAgB,GAAI,CAAC,CAAC,EA0EzE,SAxEA,8BAA0B,IAAM,CAC9B,IAAMK,EAAUV,EAAI,QACd,CACJ,KAAAW,EACA,KAAAC,EACA,OAAAC,EAAS,MACT,GAAGC,CACL,EAAIR,GAAyB,CAAC,EAE9B,GACE,CAACI,GACAE,GAAQd,GACT,OAAO,qBAAyB,IAEhC,OAEF,IAAMiB,EAAsB,IAAI,QAE1BC,EAAU,KACVb,GAEFK,EAAI,MAAML,CAAE,EAGdJ,EAAY,EAAI,EASTa,EAAO,OAPE,IAAM,CAChBR,GACFI,EAAI,MAAMJ,CAAI,EAEhBL,EAAY,EAAK,CACnB,GAKIkB,EAAmDC,GAAW,CAClEA,EAAQ,QAAQC,GAAS,CACvB,IAAMC,EAAUL,EAAoB,IAAII,EAAM,MAAM,EAEpD,GAAIA,EAAM,iBAAmB,EAAQC,EAIrC,GAAID,EAAM,eAAgB,CACxB,IAAME,EAAaL,EAAQ,EACvB,MAAG,IAAIK,CAAU,EACnBN,EAAoB,IAAII,EAAM,OAAQE,CAAU,EAEhDC,EAAS,UAAUH,EAAM,MAAM,OAExBC,IACTA,EAAQ,EACRL,EAAoB,OAAOI,EAAM,MAAM,EAE3C,CAAC,CACH,EAEMG,EAAW,IAAI,qBAAqBL,EAAoB,CAC5D,KAAON,GAAQA,EAAK,SAAY,OAChC,UACE,OAAOE,GAAW,UAAY,MAAM,QAAQA,CAAM,EAC9CA,EACAnB,GAAwBmB,CAAM,EACpC,GAAGC,CACL,CAAC,EAED,OAAAQ,EAAS,QAAQZ,CAAO,EAEjB,IAAMY,EAAS,UAAUZ,CAAO,CACzC,EAAG,CAACJ,CAAqB,CAAC,EAEtBL,EACK,CAACD,EAAKO,CAAO,EAGf,CAACP,EAAKF,CAAQ,CACvB,CCtGO,SAASyB,GAAO,CAAE,SAAAC,EAAU,GAAGC,CAAM,EAAQ,CAClD,OAAOD,EAASE,GAAUD,CAAK,CAAC,CAClC,CCvBA,IAAAE,GAAmB,gCAgBZ,SAASC,GAAqD,CACnE,MAAAC,EACA,SAAAC,EACA,GAAGC,CACL,EAA2D,CACzD,IAAMC,EAAgBC,GAASJ,EAAM,OAAQE,CAAK,EAClD,OAAOF,EAAM,IAAI,CAACK,EAAMC,IAAU,CAChC,IAAMC,EAASN,EAASI,EAAMC,CAAK,EACnC,OAAO,MAAG,IAAIC,CAAM,EAAIA,EAAOJ,EAAOG,CAAK,CAAC,EAAIC,CAClD,CAAC,CACH,CClBO,SAASC,GAAW,CACzB,MAAAC,EACA,SAAAC,EACA,GAAGC,CACL,EAAkC,CAChC,OAAOC,GAAcH,EAAOE,CAAK,EAAED,CAAQ,CAC7C,CChBA,IAAAG,GAAiD,gCCCjD,IAAAC,EAeO,gCAGP,IAAAC,GAKO,kCAUMC,GAAN,cAGGC,EAAmB,CAa3B,YAEWC,EACTC,EACA,CACA,MAAM,EAHG,YAAAD,EAVX,UAAO,GAMP,KAAU,QAAU,IAAI,IAQtB,KAAK,QAAO,sBAAmB,GAAGC,CAAI,EAEtC,IAAMC,EAAQ,KAAK,KAAK,EAClBC,KAAW,oBAAgBD,CAAK,KAGtC,gBAAY,KAAMC,EAAS,OAAOD,CAAK,CAAC,CAC1C,CAEA,QAAQE,EAAc,CACpB,IAAMF,EAAQ,KAAK,KAAK,EAClBG,EAAW,KAAK,IAAI,KACrB,WAAQH,EAAOG,CAAQ,OAC1B,gBAAY,IAAI,EAAG,SAASH,CAAK,EACjC,KAAK,UAAUA,EAAO,KAAK,IAAI,GAG7B,CAAC,KAAK,MAAQI,GAAU,KAAK,OAAO,GACtCC,GAAW,IAAI,CAEnB,CAEU,MAAO,CACf,IAAMC,EAAwB,KAAG,IAAI,KAAK,MAAM,EAC5C,KAAK,OAAO,IAAI,eAAa,KAC5B,cAAQ,iBAAc,KAAK,MAAM,CAAC,EAEvC,OAAO,KAAK,KAAK,GAAGA,CAAM,CAC5B,CAEU,QAAS,CACb,KAAK,MAAQ,CAACF,GAAU,KAAK,OAAO,IACtC,KAAK,KAAO,MAEZ,WAAK,eAAW,IAAI,EAAIG,GAAQ,CAC9BA,EAAK,KAAO,EACd,CAAC,EAEG,EAAAC,QAAE,eACJ,MAAI,eAAe,IAAM,KAAK,QAAQ,CAAC,EACvCH,GAAW,IAAI,GAEf,YAAU,MAAM,IAAI,EAG1B,CAGU,SAAU,CAClB,IAAII,EAAW,KACf,WAAK,WAAQ,KAAK,MAAM,EAAGX,GAAU,IAC/B,iBAAcA,CAAM,MACtB,oBAAiBA,EAAQ,IAAI,EAE3BY,GAAaZ,CAAM,IAChBA,EAAO,MACV,KAAK,QAAQ,IAAIA,CAAM,EAEzBW,EAAW,KAAK,IAAIA,EAAUX,EAAO,SAAW,CAAC,EAErD,CAAC,EACD,KAAK,SAAWW,EAChB,KAAK,OAAO,CACd,CAGU,SAAU,IAClB,WAAK,WAAQ,KAAK,MAAM,EAAGX,GAAU,IAC/B,iBAAcA,CAAM,MACtB,uBAAoBA,EAAQ,IAAI,CAEpC,CAAC,EACD,KAAK,QAAQ,MAAM,EACnBO,GAAW,IAAI,CACjB,CAGA,cAAcM,EAAyB,CAGjCA,EAAM,MAAQ,SACZA,EAAM,KACR,KAAK,QAAQ,GAEb,KAAK,QAAQ,IAAIA,EAAM,MAAM,EAC7B,KAAK,OAAO,GAKPA,EAAM,MAAQ,OACrB,KAAK,QAAQ,OAAOA,EAAM,MAAM,EAIzBA,EAAM,MAAQ,aACrB,KAAK,YAAW,WAAQ,KAAK,MAAM,EAAE,OACnC,CAACC,EAAiBC,IAChB,KAAK,IAAID,GAAUF,GAAaG,CAAM,EAAIA,EAAO,SAAW,GAAK,CAAC,EACpE,CACF,EAEJ,CACF,EAGA,SAASC,GAAOhB,EAAa,CAC3B,OAAOA,EAAO,OAAS,EACzB,CAGA,SAASM,GAAUW,EAAyB,CAG1C,MAAO,CAACA,EAAO,MAAQ,MAAM,KAAKA,CAAM,EAAE,MAAMD,EAAM,CACxD,CAGA,SAAST,GAAWW,EAAqB,CAClCA,EAAK,OACRA,EAAK,KAAO,MAEZ,WAAK,eAAWA,CAAI,EAAIT,GAAQ,CAC9BA,EAAK,KAAO,EACd,CAAC,KAED,sBAAmBS,EAAM,CACvB,KAAM,OACN,OAAQA,CACV,CAAC,EAEL,CD/KO,IAAMC,GAAmB,CAACC,KAAgBC,IAC/C,IAAIC,GAAcF,EAAQC,CAAI,EAGnBE,GAA4B,CAACH,KAAgBC,QACxD,yBAAqB,EAAG,IAAIC,GAAcF,EAAQC,CAAI,GEjBxD,IAAAG,GAIO,gCAIP,WAAQ,OAAO,CACb,qDACA,GAAI,CAACC,EAAQC,IAAS,IAAIC,GAAcF,EAAQC,CAAI,CACtD,CAAC,EAKM,IAAME,GAAS,aAAU,Q7BFhC,IAAAC,GAKO,gCAIPC,EAAAC,EAAc,+BAvBd", "names": ["src_exports", "__export", "BailSignal", "Controller", "FrameValue", "Interpolation", "Spring", "SpringContext", "SpringRef", "SpringValue", "Trail", "Transition", "config", "inferTo", "interpolate", "to", "update", "<PERSON><PERSON><PERSON>n", "useInView", "useResize", "useScroll", "useSpring", "useSpringRef", "useSpringValue", "useSprings", "useTrail", "useTransition", "__toCommonJS", "import_shared", "import_shared", "callProp", "value", "args", "matchProp", "key", "resolveProp", "prop", "getDefaultProp", "props", "key", "noopTransform", "value", "getDefaultProps", "transform", "keys", "DEFAULT_PROPS", "defaults", "RESERVED_PROPS", "getForwardProps", "forward", "count", "prop", "inferTo", "to", "out", "val", "computeGoal", "G", "hasProps", "_", "isAsyncTo", "detachRefs", "ctrl", "ref", "replaceRef", "<PERSON><PERSON><PERSON>n", "refs", "timeSteps", "timeFrame", "prev<PERSON><PERSON><PERSON>", "ref", "i", "controllers", "delay", "ctrl", "props", "memoizedDelayProp", "key", "callProp", "p", "queues", "q", "update", "import_shared", "import_react", "import_shared", "import_shared", "import_animated", "import_shared", "config", "defaults", "config", "AnimationConfig", "mergeConfig", "newConfig", "defaultConfig", "sanitizeConfig", "key", "frequency", "damping", "mass", "props", "isTensionConfig", "emptyArray", "Animation", "AnimationConfig", "import_shared", "scheduleProps", "callId", "key", "props", "defaultProps", "state", "actions", "resolve", "reject", "delay", "timeout", "cancel", "matchProp", "onStart", "pause", "callProp", "onResume", "onPause", "G", "err", "import_shared", "getCombinedResult", "target", "results", "result", "getCancelledResult", "getNoopResult", "getFinishedResult", "value", "finished", "cancelled", "runAsync", "to", "props", "state", "target", "callId", "parentId", "onRest", "prevTo", "prevPromise", "defaultProps", "getDefaultProps", "value", "key", "preventBail", "bail", "bailPromise", "resolve", "reject", "bailIfEnded", "bailSignal", "bailResult", "getCancelledResult", "getFinishedResult", "animate", "arg1", "arg2", "BailSignal", "skipAnimationSignal", "SkipAnimationSignal", "G", "stopAsync", "result", "resume", "animating", "queue", "err", "cancelId", "t", "import_shared", "import_animated", "isFrameValue", "value", "FrameValue", "nextId", "priority", "node", "args", "G", "count", "idle", "$P", "HAS_ANIMATED", "IS_ANIMATING", "IS_PAUSED", "hasAnimated", "target", "isAnimating", "isPaused", "setActiveBit", "active", "setPausedBit", "paused", "SpringValue", "FrameValue", "arg1", "arg2", "Animation", "props", "isAnimating", "isPaused", "node", "hasAnimated", "dt", "idle", "changed", "anim", "to<PERSON><PERSON><PERSON>", "config", "payload", "i", "to", "finished", "position", "elapsed", "from", "v0", "velocity", "precision", "decay", "e", "restVelocity", "bounceFactor", "canBounce", "isGrowing", "isMoving", "isBouncing", "step", "numSteps", "n", "springForce", "dampingForce", "acceleration", "p", "currVal", "finalVal", "value", "queue", "results", "getCombinedResult", "cancel", "stopAsync", "event", "key", "isAsyncTo", "range", "isLoop", "defaultProps", "getDefaultProps", "prop", "resolveProp", "mergeActiveFn", "sendEvent", "state", "scheduleProps", "setPausedBit", "getFinishedResult", "checkFinished", "result", "nextProps", "createLoopUpdate", "resolve", "getCancelledResult", "hasToProp", "hasFromProp", "prevTo", "prevFrom", "has<PERSON>romC<PERSON>ed", "hasToChanged", "hasAsyncTo", "mergeConfig", "callProp", "reset", "matchProp", "goal", "computeGoal", "isAnimatable", "immediate", "nodeType", "goalType", "started", "has<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onRest", "ACTIVE_EVENTS", "type", "runAsync", "getNoopResult", "priority", "isFrameValue", "arg", "oldNode", "setActiveBit", "G", "target", "loop", "loopRet", "overrides", "inferTo", "reverse", "createUpdate", "keys", "findDefined", "declareUpdate", "update", "values", "getDefaultProp", "args", "import_shared", "BATCHED_EVENTS", "nextId", "Controller", "props", "flush", "spring", "item", "values", "key", "value", "createUpdate", "queue", "prepare<PERSON>eys", "flushUpdateQueue", "arg", "keys", "springs", "stopAsync", "iterator", "onStart", "onChange", "onRest", "active", "changed", "result", "idle", "event", "ctrl", "flushUpdate", "results", "getCombinedResult", "isLoop", "to", "from", "loop", "onResolve", "defaults", "asyncTo", "handler", "finished", "cancelled", "state", "promises", "cancel", "getDefaultProp", "scheduleProps", "resolve", "getCancelledResult", "runAsync", "resume", "nextProps", "createLoopUpdate", "getSprings", "prepareSprings", "createSpring", "setSprings", "observer", "SpringValue", "create", "React", "import_react", "import_shared", "SpringContext", "children", "props", "inherited", "ctx", "pause", "immediate", "Provider", "makeContext", "target", "init", "import_shared", "SpringRef", "current", "props", "results", "ctrl", "i", "update", "_getProps", "values", "arg", "index", "useSprings", "length", "props", "deps", "propsFn", "ref", "SpringRef", "layoutId", "forceUpdate", "state", "ctrl", "updates", "springs", "getSprings", "key", "flushUpdateQueue", "resolve", "setSprings", "ctrls", "prevLength", "detachRefs", "declareUpdates", "startIndex", "endIndex", "i", "Controller", "update", "declareUpdate", "context", "SpringContext", "prevContext", "hasContext", "hasProps", "queue", "cb", "replaceRef", "values", "x", "useSpring", "props", "deps", "isFn", "values", "ref", "useSprings", "import_react", "initSpringRef", "SpringRef", "useSpringRef", "import_shared", "useSpringValue", "initial", "props", "springValue", "SpringValue", "import_shared", "useTrail", "length", "propsArg", "deps", "propsFn", "reverse", "passedRef", "result", "useSprings", "ctrl", "props", "i", "parent", "replaceRef", "ref", "React", "import_react", "import_shared", "useTransition", "data", "props", "deps", "propsFn", "reset", "sort", "trail", "expires", "exitBeforeEnter", "onDestroyed", "propsRef", "propsConfig", "ref", "SpringRef", "items", "transitions", "usedTransitions", "prevTransitions", "t", "detachRefs", "keys", "get<PERSON><PERSON><PERSON>", "expired", "ctrl", "item", "key", "callProp", "reused", "i", "Controller", "leave", "keyIndex", "prevIndex", "a", "b", "delay", "forceUpdate", "defaultProps", "getDefaultProps", "changes", "exitingTransitions", "forceChange", "prevPhase", "p", "to", "phase", "props<PERSON><PERSON><PERSON>", "isLeave", "inferTo", "config", "payload", "from", "onResolve", "result", "idle", "expiry", "expiryMs", "springs", "getSprings", "context", "SpringContext", "prevContext", "hasContext", "hasProps", "_", "ind", "state", "replaceRef", "renderTransitions", "render", "elem", "<PERSON><PERSON><PERSON>", "import_shared", "useScroll", "container", "springOptions", "scrollValues", "api", "useSpring", "cleanupScroll", "x", "y", "value", "import_shared", "useResize", "container", "springOptions", "sizeValues", "api", "useSpring", "cleanupScroll", "width", "height", "value", "import_react", "import_shared", "defaultThresholdOptions", "useInView", "props", "args", "isInView", "setIsInView", "ref", "propsFn", "springsProps", "to", "from", "restSpringProps", "intersectionArguments", "springs", "api", "useSpring", "element", "root", "once", "amount", "restArgs", "activeIntersections", "onEnter", "handleIntersection", "entries", "entry", "onLeave", "newOnLeave", "observer", "Spring", "children", "props", "useSpring", "import_shared", "Trail", "items", "children", "props", "trails", "useTrail", "item", "index", "result", "Transition", "items", "children", "props", "useTransition", "import_shared", "import_shared", "import_animated", "Interpolation", "FrameValue", "source", "args", "value", "nodeType", "_dt", "oldValue", "checkIdle", "becomeIdle", "inputs", "node", "G", "priority", "isFrameValue", "event", "highest", "parent", "isIdle", "active", "self", "to", "source", "args", "Interpolation", "interpolate", "import_shared", "source", "args", "Interpolation", "update", "import_shared", "__reExport", "src_exports"]}