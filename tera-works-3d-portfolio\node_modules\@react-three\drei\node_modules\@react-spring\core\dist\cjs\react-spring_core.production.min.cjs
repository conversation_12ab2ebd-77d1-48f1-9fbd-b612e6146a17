"use strict";var qt=Object.create;var ze=Object.defineProperty;var Qt=Object.getOwnPropertyDescriptor;var zt=Object.getOwnPropertyNames;var Gt=Object.getPrototypeOf,Bt=Object.prototype.hasOwnProperty;var Kt=(t,n)=>{for(var e in n)ze(t,e,{get:n[e],enumerable:!0})},Qe=(t,n,e,r)=>{if(n&&typeof n=="object"||typeof n=="function")for(let o of zt(n))!Bt.call(t,o)&&o!==e&&ze(t,o,{get:()=>n[o],enumerable:!(r=Qt(n,o))||r.enumerable});return t},E=(t,n,e)=>(Qe(t,n,"default"),e&&Qe(e,n,"default")),Pt=(t,n,e)=>(e=t!=null?qt(Gt(t)):{},Qe(n||!t||!t.__esModule?ze(e,"default",{value:t,enumerable:!0}):e,t)),Xt=t=>Qe(ze({},"__esModule",{value:!0}),t);var U={};Kt(U,{BailSignal:()=>Ce,Controller:()=>le,FrameValue:()=>te,Globals:()=>Ae.Globals,Interpolation:()=>se,Spring:()=>mn,SpringContext:()=>re,SpringRef:()=>ce,SpringValue:()=>pe,Trail:()=>hn,Transition:()=>gn,config:()=>it,createInterpolator:()=>de.createInterpolator,easings:()=>de.easings,inferTo:()=>Se,interpolate:()=>Pn,to:()=>Sn,update:()=>Tn,useChain:()=>$t,useInView:()=>dn,useIsomorphicLayoutEffect:()=>de.useIsomorphicLayoutEffect,useReducedMotion:()=>de.useReducedMotion,useResize:()=>cn,useScroll:()=>ln,useSpring:()=>oe,useSpringRef:()=>sn,useSpringValue:()=>an,useSprings:()=>je,useTrail:()=>ht,useTransition:()=>gt});module.exports=Xt(U);var Z=require("@react-spring/shared");var _=require("@react-spring/shared");function k(t,...n){return _.is.fun(t)?t(...n):t}var ge=(t,n)=>t===!0||!!(n&&t&&(_.is.fun(t)?t(n):(0,_.toArray)(t).includes(n))),st=(t,n)=>_.is.obj(t)?n&&t[n]:t;var Ge=(t,n)=>t.default===!0?t[n]:t.default?t.default[n]:void 0,Yt=t=>t,ye=(t,n=Yt)=>{let e=Ht;t.default&&t.default!==!0&&(t=t.default,e=Object.keys(t));let r={};for(let o of e){let s=n(t[o],o);_.is.und(s)||(r[o]=s)}return r},Ht=["config","onProps","onStart","onChange","onPause","onResume","onRest"],Jt={config:1,from:1,to:1,ref:1,loop:1,reset:1,pause:1,cancel:1,reverse:1,immediate:1,default:1,delay:1,onProps:1,onStart:1,onChange:1,onPause:1,onResume:1,onRest:1,onResolve:1,items:1,trail:1,sort:1,expires:1,initial:1,enter:1,update:1,leave:1,children:1,onDestroyed:1,keys:1,callId:1,parentId:1};function Wt(t){let n={},e=0;if((0,_.eachProp)(t,(r,o)=>{Jt[o]||(n[o]=r,e++)}),e)return n}function Se(t){let n=Wt(t);if(n){let e={to:n};return(0,_.eachProp)(t,(r,o)=>o in n||(e[o]=r)),e}return{...t}}function Re(t){return t=(0,_.getFluidValue)(t),_.is.arr(t)?t.map(Re):(0,_.isAnimatedString)(t)?_.Globals.createStringInterpolator({range:[0,1],output:[t,t]})(1):t}function Be(t){for(let n in t)return!0;return!1}function Ke(t){return _.is.fun(t)||_.is.arr(t)&&_.is.obj(t[0])}function ke(t,n){t.ref?.delete(t),n?.delete(t)}function ve(t,n){n&&t.ref!==n&&(t.ref?.delete(t),n.add(t),t.ref=n)}function $t(t,n,e=1e3){(0,Z.useIsomorphicLayoutEffect)(()=>{if(n){let r=0;(0,Z.each)(t,(o,s)=>{let u=o.current;if(u.length){let i=e*n[s];isNaN(i)?i=r:r=i,(0,Z.each)(u,l=>{(0,Z.each)(l.queue,p=>{let R=p.delay;p.delay=c=>i+k(R||0,c)})}),o.start()}})}else{let r=Promise.resolve();(0,Z.each)(t,o=>{let s=o.current;if(s.length){let u=s.map(i=>{let l=i.queue;return i.queue=[],l});r=r.then(()=>((0,Z.each)(s,(i,l)=>(0,Z.each)(u[l]||[],p=>i.queue.push(p))),Promise.all(o.start())))}})}})}var Et=require("@react-spring/shared");var W=require("react"),j=require("@react-spring/shared");var a=require("@react-spring/shared"),O=require("@react-spring/animated");var J=require("@react-spring/shared");var it={default:{tension:170,friction:26},gentle:{tension:120,friction:14},wobbly:{tension:180,friction:12},stiff:{tension:210,friction:20},slow:{tension:280,friction:60},molasses:{tension:280,friction:120}};var at={...it.default,mass:1,damping:1,easing:J.easings.linear,clamp:!1},Xe=class{constructor(){this.velocity=0;Object.assign(this,at)}};function xt(t,n,e){e&&(e={...e},Tt(e,n),n={...e,...n}),Tt(t,n),Object.assign(t,n);for(let u in at)t[u]==null&&(t[u]=at[u]);let{frequency:r,damping:o}=t,{mass:s}=t;return J.is.und(r)||(r<.01&&(r=.01),o<0&&(o=0),t.tension=Math.pow(2*Math.PI/r,2)*s,t.friction=4*Math.PI*o*s/r),t}function Tt(t,n){if(!J.is.und(n.decay))t.duration=void 0;else{let e=!J.is.und(n.tension)||!J.is.und(n.friction);(e||!J.is.und(n.frequency)||!J.is.und(n.damping)||!J.is.und(n.mass))&&(t.duration=void 0,t.decay=void 0),e&&(t.frequency=void 0)}}var bt=[],Ye=class{constructor(){this.changed=!1;this.values=bt;this.toValues=null;this.fromValues=bt;this.config=new Xe;this.immediate=!1}};var Pe=require("@react-spring/shared");function He(t,{key:n,props:e,defaultProps:r,state:o,actions:s}){return new Promise((u,i)=>{let l,p,R=ge(e.cancel??r?.cancel,n);if(R)y();else{Pe.is.und(e.pause)||(o.paused=ge(e.pause,n));let g=r?.pause;g!==!0&&(g=o.paused||ge(g,n)),l=k(e.delay||0,n),g?(o.resumeQueue.add(S),s.pause()):(s.resume(),S())}function c(){o.resumeQueue.add(S),o.timeouts.delete(p),p.cancel(),l=p.time-Pe.raf.now()}function S(){l>0&&!Pe.Globals.skipAnimation?(o.delayed=!0,p=Pe.raf.setTimeout(y,l),o.pauseQueue.add(c),o.timeouts.add(p)):y()}function y(){o.delayed&&(o.delayed=!1),o.pauseQueue.delete(c),o.timeouts.delete(p),t<=(o.cancelId||0)&&(R=!0);try{s.start({...e,callId:t,cancel:R},u)}catch(g){i(g)}}})}var Q=require("@react-spring/shared");var Ue=(t,n)=>n.length==1?n[0]:n.some(e=>e.cancelled)?ee(t.get()):n.every(e=>e.noop)?ut(t.get()):B(t.get(),n.every(e=>e.finished)),ut=t=>({value:t,noop:!0,finished:!0,cancelled:!1}),B=(t,n,e=!1)=>({value:t,finished:n,cancelled:e}),ee=t=>({value:t,cancelled:!0,finished:!1});function We(t,n,e,r){let{callId:o,parentId:s,onRest:u}=n,{asyncTo:i,promise:l}=e;return!s&&t===i&&!n.reset?l:e.promise=(async()=>{e.asyncId=o,e.asyncTo=t;let p=ye(n,(m,d)=>d==="onRest"?void 0:m),R,c,S=new Promise((m,d)=>(R=m,c=d)),y=m=>{let d=o<=(e.cancelId||0)&&ee(r)||o!==e.asyncId&&B(r,!1);if(d)throw m.result=d,c(m),m},g=(m,d)=>{let b=new Ce,T=new Je;return(async()=>{if(Q.Globals.skipAnimation)throw Te(e),T.result=B(r,!1),c(T),T;y(b);let I=Q.is.obj(m)?{...m}:{...d,to:m};I.parentId=o,(0,Q.eachProp)(p,(F,V)=>{Q.is.und(I[V])&&(I[V]=F)});let A=await r.start(I);return y(b),e.paused&&await new Promise(F=>{e.resumeQueue.add(F)}),A})()},f;if(Q.Globals.skipAnimation)return Te(e),B(r,!1);try{let m;Q.is.arr(t)?m=(async d=>{for(let b of d)await g(b)})(t):m=Promise.resolve(t(g,r.stop.bind(r))),await Promise.all([m.then(R),S]),f=B(r.get(),!0,!1)}catch(m){if(m instanceof Ce)f=m.result;else if(m instanceof Je)f=m.result;else throw m}finally{o==e.asyncId&&(e.asyncId=s,e.asyncTo=s?i:void 0,e.promise=s?l:void 0)}return Q.is.fun(u)&&Q.raf.batchedUpdates(()=>{u(f,r,r.item)}),f})()}function Te(t,n){(0,Q.flush)(t.timeouts,e=>e.cancel()),t.pauseQueue.clear(),t.resumeQueue.clear(),t.asyncId=t.asyncTo=t.promise=void 0,n&&(t.cancelId=n)}var Ce=class extends Error{constructor(){super("An async animation has been interrupted. You see this error because you forgot to use `await` or `.catch(...)` on its returned promise.")}},Je=class extends Error{constructor(){super("SkipAnimationSignal")}};var X=require("@react-spring/shared"),At=require("@react-spring/animated"),Ee=t=>t instanceof te,Zt=1,te=class extends X.FluidValue{constructor(){super(...arguments);this.id=Zt++;this._priority=0}get priority(){return this._priority}set priority(e){this._priority!=e&&(this._priority=e,this._onPriorityChange(e))}get(){let e=(0,At.getAnimated)(this);return e&&e.getValue()}to(...e){return X.Globals.to(this,e)}interpolate(...e){return(0,X.deprecateInterpolate)(),X.Globals.to(this,e)}toJSON(){return this.get()}observerAdded(e){e==1&&this._attach()}observerRemoved(e){e==0&&this._detach()}_attach(){}_detach(){}_onChange(e,r=!1){(0,X.callFluidObservers)(this,{type:"change",parent:this,value:e,idle:r})}_onPriorityChange(e){this.idle||X.frameLoop.sort(this),(0,X.callFluidObservers)(this,{type:"priority",parent:this,priority:e})}};var xe=Symbol.for("SpringPhase"),Rt=1,pt=2,lt=4,$e=t=>(t[xe]&Rt)>0,ne=t=>(t[xe]&pt)>0,Ie=t=>(t[xe]&lt)>0,ct=(t,n)=>n?t[xe]|=pt|Rt:t[xe]&=~pt,ft=(t,n)=>n?t[xe]|=lt:t[xe]&=~lt;var pe=class extends te{constructor(e,r){super();this.animation=new Ye;this.defaultProps={};this._state={paused:!1,delayed:!1,pauseQueue:new Set,resumeQueue:new Set,timeouts:new Set};this._pendingCalls=new Set;this._lastCallId=0;this._lastToId=0;this._memoizedDuration=0;if(!a.is.und(e)||!a.is.und(r)){let o=a.is.obj(e)?{...e}:{...r,from:e};a.is.und(o.default)&&(o.default=!0),this.start(o)}}get idle(){return!(ne(this)||this._state.asyncTo)||Ie(this)}get goal(){return(0,a.getFluidValue)(this.animation.to)}get velocity(){let e=(0,O.getAnimated)(this);return e instanceof O.AnimatedValue?e.lastVelocity||0:e.getPayload().map(r=>r.lastVelocity||0)}get hasAnimated(){return $e(this)}get isAnimating(){return ne(this)}get isPaused(){return Ie(this)}get isDelayed(){return this._state.delayed}advance(e){let r=!0,o=!1,s=this.animation,{toValues:u}=s,{config:i}=s,l=(0,O.getPayload)(s.to);!l&&(0,a.hasFluidValue)(s.to)&&(u=(0,a.toArray)((0,a.getFluidValue)(s.to))),s.values.forEach((c,S)=>{if(c.done)return;let y=c.constructor==O.AnimatedString?1:l?l[S].lastPosition:u[S],g=s.immediate,f=y;if(!g){if(f=c.lastPosition,i.tension<=0){c.done=!0;return}let m=c.elapsedTime+=e,d=s.fromValues[S],b=c.v0!=null?c.v0:c.v0=a.is.arr(i.velocity)?i.velocity[S]:i.velocity,T,I=i.precision||(d==y?.005:Math.min(1,Math.abs(y-d)*.001));if(a.is.und(i.duration))if(i.decay){let A=i.decay===!0?.998:i.decay,F=Math.exp(-(1-A)*m);f=d+b/(1-A)*(1-F),g=Math.abs(c.lastPosition-f)<=I,T=b*F}else{T=c.lastVelocity==null?b:c.lastVelocity;let A=i.restVelocity||I/10,F=i.clamp?0:i.bounce,V=!a.is.und(F),z=d==y?c.v0>0:d<y,w,Y=!1,q=1,ae=Math.ceil(e/q);for(let K=0;K<ae&&(w=Math.abs(T)>A,!(!w&&(g=Math.abs(y-f)<=I,g)));++K){V&&(Y=f==y||f>y==z,Y&&(T=-T*F,f=y));let h=-i.tension*1e-6*(f-y),x=-i.friction*.001*T,L=(h+x)/i.mass;T=T+L*q,f=f+T*q}}else{let A=1;i.duration>0&&(this._memoizedDuration!==i.duration&&(this._memoizedDuration=i.duration,c.durationProgress>0&&(c.elapsedTime=i.duration*c.durationProgress,m=c.elapsedTime+=e)),A=(i.progress||0)+m/this._memoizedDuration,A=A>1?1:A<0?0:A,c.durationProgress=A),f=d+i.easing(A)*(y-d),T=(f-c.lastPosition)/e,g=A==1}c.lastVelocity=T,Number.isNaN(f)&&(console.warn("Got NaN while animating:",this),g=!0)}l&&!l[S].done&&(g=!1),g?c.done=!0:r=!1,c.setValue(f,i.round)&&(o=!0)});let p=(0,O.getAnimated)(this),R=p.getValue();if(r){let c=(0,a.getFluidValue)(s.to);(R!==c||o)&&!i.decay?(p.setValue(c),this._onChange(c)):o&&i.decay&&this._onChange(R),this._stop()}else o&&this._onChange(R)}set(e){return a.raf.batchedUpdates(()=>{this._stop(),this._focus(e),this._set(e)}),this}pause(){this._update({pause:!0})}resume(){this._update({pause:!1})}finish(){if(ne(this)){let{to:e,config:r}=this.animation;a.raf.batchedUpdates(()=>{this._onStart(),r.decay||this._set(e,!1),this._stop()})}return this}update(e){return(this.queue||(this.queue=[])).push(e),this}start(e,r){let o;return a.is.und(e)?(o=this.queue||[],this.queue=[]):o=[a.is.obj(e)?e:{...r,to:e}],Promise.all(o.map(s=>this._update(s))).then(s=>Ue(this,s))}stop(e){let{to:r}=this.animation;return this._focus(this.get()),Te(this._state,e&&this._lastCallId),a.raf.batchedUpdates(()=>this._stop(r,e)),this}reset(){this._update({reset:!0})}eventObserved(e){e.type=="change"?this._start():e.type=="priority"&&(this.priority=e.priority+1)}_prepareNode(e){let r=this.key||"",{to:o,from:s}=e;o=a.is.obj(o)?o[r]:o,(o==null||Ke(o))&&(o=void 0),s=a.is.obj(s)?s[r]:s,s==null&&(s=void 0);let u={to:o,from:s};return $e(this)||(e.reverse&&([o,s]=[s,o]),s=(0,a.getFluidValue)(s),a.is.und(s)?(0,O.getAnimated)(this)||this._set(o):this._set(s)),u}_update({...e},r){let{key:o,defaultProps:s}=this;e.default&&Object.assign(s,ye(e,(l,p)=>/^on/.test(p)?st(l,o):l)),Ct(this,e,"onProps"),Le(this,"onProps",e,this);let u=this._prepareNode(e);if(Object.isFrozen(this))throw Error("Cannot animate a `SpringValue` object that is frozen. Did you forget to pass your component to `animated(...)` before animating its props?");let i=this._state;return He(++this._lastCallId,{key:o,props:e,defaultProps:s,state:i,actions:{pause:()=>{Ie(this)||(ft(this,!0),(0,a.flushCalls)(i.pauseQueue),Le(this,"onPause",B(this,we(this,this.animation.to)),this))},resume:()=>{Ie(this)&&(ft(this,!1),ne(this)&&this._resume(),(0,a.flushCalls)(i.resumeQueue),Le(this,"onResume",B(this,we(this,this.animation.to)),this))},start:this._merge.bind(this,u)}}).then(l=>{if(e.loop&&l.finished&&!(r&&l.noop)){let p=dt(e);if(p)return this._update(p,!0)}return l})}_merge(e,r,o){if(r.cancel)return this.stop(!0),o(ee(this));let s=!a.is.und(e.to),u=!a.is.und(e.from);if(s||u)if(r.callId>this._lastToId)this._lastToId=r.callId;else return o(ee(this));let{key:i,defaultProps:l,animation:p}=this,{to:R,from:c}=p,{to:S=R,from:y=c}=e;u&&!s&&(!r.default||a.is.und(S))&&(S=y),r.reverse&&([S,y]=[y,S]);let g=!(0,a.isEqual)(y,c);g&&(p.from=y),y=(0,a.getFluidValue)(y);let f=!(0,a.isEqual)(S,R);f&&this._focus(S);let m=Ke(r.to),{config:d}=p,{decay:b,velocity:T}=d;(s||u)&&(d.velocity=0),r.config&&!m&&xt(d,k(r.config,i),r.config!==l.config?k(l.config,i):void 0);let I=(0,O.getAnimated)(this);if(!I||a.is.und(S))return o(B(this,!0));let A=a.is.und(r.reset)?u&&!r.default:!a.is.und(y)&&ge(r.reset,i),F=A?y:this.get(),V=Re(S),z=a.is.num(V)||a.is.arr(V)||(0,a.isAnimatedString)(V),w=!m&&(!z||ge(l.immediate||r.immediate,i));if(f){let K=(0,O.getAnimatedType)(S);if(K!==I.constructor)if(w)I=this._set(V);else throw Error(`Cannot animate between ${I.constructor.name} and ${K.name}, as the "to" prop suggests`)}let Y=I.constructor,q=(0,a.hasFluidValue)(S),ae=!1;if(!q){let K=A||!$e(this)&&g;(f||K)&&(ae=(0,a.isEqual)(Re(F),V),q=!ae),(!(0,a.isEqual)(p.immediate,w)&&!w||!(0,a.isEqual)(d.decay,b)||!(0,a.isEqual)(d.velocity,T))&&(q=!0)}if(ae&&ne(this)&&(p.changed&&!A?q=!0:q||this._stop(R)),!m&&((q||(0,a.hasFluidValue)(R))&&(p.values=I.getPayload(),p.toValues=(0,a.hasFluidValue)(S)?null:Y==O.AnimatedString?[1]:(0,a.toArray)(V)),p.immediate!=w&&(p.immediate=w,!w&&!A&&this._set(R)),q)){let{onRest:K}=p;(0,a.each)(en,x=>Ct(this,r,x));let h=B(this,we(this,R));(0,a.flushCalls)(this._pendingCalls,h),this._pendingCalls.add(o),p.changed&&a.raf.batchedUpdates(()=>{p.changed=!A,K?.(h,this),A?k(l.onRest,h):p.onStart?.(h,this)})}A&&this._set(F),m?o(We(r.to,r,this._state,this)):q?this._start():ne(this)&&!f?this._pendingCalls.add(o):o(ut(F))}_focus(e){let r=this.animation;e!==r.to&&((0,a.getFluidObservers)(this)&&this._detach(),r.to=e,(0,a.getFluidObservers)(this)&&this._attach())}_attach(){let e=0,{to:r}=this.animation;(0,a.hasFluidValue)(r)&&((0,a.addFluidObserver)(r,this),Ee(r)&&(e=r.priority+1)),this.priority=e}_detach(){let{to:e}=this.animation;(0,a.hasFluidValue)(e)&&(0,a.removeFluidObserver)(e,this)}_set(e,r=!0){let o=(0,a.getFluidValue)(e);if(!a.is.und(o)){let s=(0,O.getAnimated)(this);if(!s||!(0,a.isEqual)(o,s.getValue())){let u=(0,O.getAnimatedType)(o);!s||s.constructor!=u?(0,O.setAnimated)(this,u.create(o)):s.setValue(o),s&&a.raf.batchedUpdates(()=>{this._onChange(o,r)})}}return(0,O.getAnimated)(this)}_onStart(){let e=this.animation;e.changed||(e.changed=!0,Le(this,"onStart",B(this,we(this,e.to)),this))}_onChange(e,r){r||(this._onStart(),k(this.animation.onChange,e,this)),k(this.defaultProps.onChange,e,this),super._onChange(e,r)}_start(){let e=this.animation;(0,O.getAnimated)(this).reset((0,a.getFluidValue)(e.to)),e.immediate||(e.fromValues=e.values.map(r=>r.lastPosition)),ne(this)||(ct(this,!0),Ie(this)||this._resume())}_resume(){a.Globals.skipAnimation?this.finish():a.frameLoop.start(this)}_stop(e,r){if(ne(this)){ct(this,!1);let o=this.animation;(0,a.each)(o.values,u=>{u.done=!0}),o.toValues&&(o.onChange=o.onPause=o.onResume=void 0),(0,a.callFluidObservers)(this,{type:"idle",parent:this});let s=r?ee(this.get()):B(this.get(),we(this,e??o.to));(0,a.flushCalls)(this._pendingCalls,s),o.changed&&(o.changed=!1,Le(this,"onRest",s,this))}}};function we(t,n){let e=Re(n),r=Re(t.get());return(0,a.isEqual)(r,e)}function dt(t,n=t.loop,e=t.to){let r=k(n);if(r){let o=r!==!0&&Se(r),s=(o||t).reverse,u=!o||o.reset;return Ve({...t,loop:n,default:!1,pause:void 0,to:!s||Ke(e)?e:void 0,from:u?t.from:void 0,reset:u,...o})}}function Ve(t){let{to:n,from:e}=t=Se(t),r=new Set;return a.is.obj(n)&&vt(n,r),a.is.obj(e)&&vt(e,r),t.keys=r.size?Array.from(r):null,t}function It(t){let n=Ve(t);return a.is.und(n.default)&&(n.default=ye(n)),n}function vt(t,n){(0,a.eachProp)(t,(e,r)=>e!=null&&n.add(r))}var en=["onStart","onRest","onChange","onPause","onResume"];function Ct(t,n,e){t.animation[e]=n[e]!==Ge(n,e)?st(n[e],t.key):void 0}function Le(t,n,...e){t.animation[n]?.(...e),t.defaultProps[n]?.(...e)}var P=require("@react-spring/shared");var tn=["onStart","onChange","onRest"],nn=1,le=class{constructor(n,e){this.id=nn++;this.springs={};this.queue=[];this._lastAsyncId=0;this._active=new Set;this._changed=new Set;this._started=!1;this._state={paused:!1,pauseQueue:new Set,resumeQueue:new Set,timeouts:new Set};this._events={onStart:new Map,onChange:new Map,onRest:new Map};this._onFrame=this._onFrame.bind(this),e&&(this._flush=e),n&&this.start({default:!0,...n})}get idle(){return!this._state.asyncTo&&Object.values(this.springs).every(n=>n.idle&&!n.isDelayed&&!n.isPaused)}get item(){return this._item}set item(n){this._item=n}get(){let n={};return this.each((e,r)=>n[r]=e.get()),n}set(n){for(let e in n){let r=n[e];P.is.und(r)||this.springs[e].set(r)}}update(n){return n&&this.queue.push(Ve(n)),this}start(n){let{queue:e}=this;return n?e=(0,P.toArray)(n).map(Ve):this.queue=[],this._flush?this._flush(this,e):(Ft(this,e),Ze(this,e))}stop(n,e){if(n!==!!n&&(e=n),e){let r=this.springs;(0,P.each)((0,P.toArray)(e),o=>r[o].stop(!!n))}else Te(this._state,this._lastAsyncId),this.each(r=>r.stop(!!n));return this}pause(n){if(P.is.und(n))this.start({pause:!0});else{let e=this.springs;(0,P.each)((0,P.toArray)(n),r=>e[r].pause())}return this}resume(n){if(P.is.und(n))this.start({pause:!1});else{let e=this.springs;(0,P.each)((0,P.toArray)(n),r=>e[r].resume())}return this}each(n){(0,P.eachProp)(this.springs,n)}_onFrame(){let{onStart:n,onChange:e,onRest:r}=this._events,o=this._active.size>0,s=this._changed.size>0;(o&&!this._started||s&&!this._started)&&(this._started=!0,(0,P.flush)(n,([l,p])=>{p.value=this.get(),l(p,this,this._item)}));let u=!o&&this._started,i=s||u&&r.size?this.get():null;s&&e.size&&(0,P.flush)(e,([l,p])=>{p.value=i,l(p,this,this._item)}),u&&(this._started=!1,(0,P.flush)(r,([l,p])=>{p.value=i,l(p,this,this._item)}))}eventObserved(n){if(n.type=="change")this._changed.add(n.parent),n.idle||this._active.add(n.parent);else if(n.type=="idle")this._active.delete(n.parent);else return;P.raf.onFrame(this._onFrame)}};function Ze(t,n){return Promise.all(n.map(e=>Vt(t,e))).then(e=>Ue(t,e))}async function Vt(t,n,e){let{keys:r,to:o,from:s,loop:u,onRest:i,onResolve:l}=n,p=P.is.obj(n.default)&&n.default;u&&(n.loop=!1),o===!1&&(n.to=null),s===!1&&(n.from=null);let R=P.is.arr(o)||P.is.fun(o)?o:void 0;R?(n.to=void 0,n.onRest=void 0,p&&(p.onRest=void 0)):(0,P.each)(tn,f=>{let m=n[f];if(P.is.fun(m)){let d=t._events[f];n[f]=({finished:b,cancelled:T})=>{let I=d.get(m);I?(b||(I.finished=!1),T&&(I.cancelled=!0)):d.set(m,{value:null,finished:b||!1,cancelled:T||!1})},p&&(p[f]=n[f])}});let c=t._state;n.pause===!c.paused?(c.paused=n.pause,(0,P.flushCalls)(n.pause?c.pauseQueue:c.resumeQueue)):c.paused&&(n.pause=!0);let S=(r||Object.keys(t.springs)).map(f=>t.springs[f].start(n)),y=n.cancel===!0||Ge(n,"cancel")===!0;(R||y&&c.asyncId)&&S.push(He(++t._lastAsyncId,{props:n,state:c,actions:{pause:P.noop,resume:P.noop,start(f,m){y?(Te(c,t._lastAsyncId),m(ee(t))):(f.onRest=i,m(We(R,f,c,t)))}}})),c.paused&&await new Promise(f=>{c.resumeQueue.add(f)});let g=Ue(t,await Promise.all(S));if(u&&g.finished&&!(e&&g.noop)){let f=dt(n,u,o);if(f)return Ft(t,[f]),Vt(t,f,!0)}return l&&P.raf.batchedUpdates(()=>l(g,t,t.item)),g}function Me(t,n){let e={...t.springs};return n&&(0,P.each)((0,P.toArray)(n),r=>{P.is.und(r.keys)&&(r=Ve(r)),P.is.obj(r.to)||(r={...r,to:void 0}),Ot(e,r,o=>_t(o))}),mt(t,e),e}function mt(t,n){(0,P.eachProp)(n,(e,r)=>{t.springs[r]||(t.springs[r]=e,(0,P.addFluidObserver)(e,t))})}function _t(t,n){let e=new pe;return e.key=t,n&&(0,P.addFluidObserver)(e,n),e}function Ot(t,n,e){n.keys&&(0,P.each)(n.keys,r=>{(t[r]||(t[r]=e(r)))._prepareNode(n)})}function Ft(t,n){(0,P.each)(n,e=>{Ot(t.springs,e,r=>_t(r,t))})}var tt=Pt(require("react")),kt=require("react"),Ut=require("@react-spring/shared"),re=({children:t,...n})=>{let e=(0,kt.useContext)(et),r=n.pause||!!e.pause,o=n.immediate||!!e.immediate;n=(0,Ut.useMemoOne)(()=>({pause:r,immediate:o}),[r,o]);let{Provider:s}=et;return tt.createElement(s,{value:n},t)},et=rn(re,{});re.Provider=et.Provider;re.Consumer=et.Consumer;function rn(t,n){return Object.assign(t,tt.createContext(n)),t.Provider._context=t,t.Consumer._context=t,t}var D=require("@react-spring/shared"),ce=()=>{let t=[],n=function(r){(0,D.deprecateDirectCall)();let o=[];return(0,D.each)(t,(s,u)=>{if(D.is.und(r))o.push(s.start());else{let i=e(r,s,u);i&&o.push(s.start(i))}}),o};n.current=t,n.add=function(r){t.includes(r)||t.push(r)},n.delete=function(r){let o=t.indexOf(r);~o&&t.splice(o,1)},n.pause=function(){return(0,D.each)(t,r=>r.pause(...arguments)),this},n.resume=function(){return(0,D.each)(t,r=>r.resume(...arguments)),this},n.set=function(r){(0,D.each)(t,(o,s)=>{let u=D.is.fun(r)?r(s,o):r;u&&o.set(u)})},n.start=function(r){let o=[];return(0,D.each)(t,(s,u)=>{if(D.is.und(r))o.push(s.start());else{let i=this._getProps(r,s,u);i&&o.push(s.start(i))}}),o},n.stop=function(){return(0,D.each)(t,r=>r.stop(...arguments)),this},n.update=function(r){return(0,D.each)(t,(o,s)=>o.update(this._getProps(r,o,s))),this};let e=function(r,o,s){return D.is.fun(r)?r(s,o):r};return n._getProps=e,n};function je(t,n,e){let r=j.is.fun(n)&&n;r&&!e&&(e=[]);let o=(0,W.useMemo)(()=>r||arguments.length==3?ce():void 0,[]),s=(0,W.useRef)(0),u=(0,j.useForceUpdate)(),i=(0,W.useMemo)(()=>({ctrls:[],queue:[],flush(d,b){let T=Me(d,b);return s.current>0&&!i.queue.length&&!Object.keys(T).some(A=>!d.springs[A])?Ze(d,b):new Promise(A=>{mt(d,T),i.queue.push(()=>{A(Ze(d,b))}),u()})}}),[]),l=(0,W.useRef)([...i.ctrls]),p=[],R=(0,j.usePrev)(t)||0;(0,W.useMemo)(()=>{(0,j.each)(l.current.slice(t,R),d=>{ke(d,o),d.stop(!0)}),l.current.length=t,c(R,t)},[t]),(0,W.useMemo)(()=>{c(0,Math.min(R,t))},e);function c(d,b){for(let T=d;T<b;T++){let I=l.current[T]||(l.current[T]=new le(null,i.flush)),A=r?r(T,I):n[T];A&&(p[T]=It(A))}}let S=l.current.map((d,b)=>Me(d,p[b])),y=(0,W.useContext)(re),g=(0,j.usePrev)(y),f=y!==g&&Be(y);(0,j.useIsomorphicLayoutEffect)(()=>{s.current++,i.ctrls=l.current;let{queue:d}=i;d.length&&(i.queue=[],(0,j.each)(d,b=>b())),(0,j.each)(l.current,(b,T)=>{o?.add(b),f&&b.start({default:y});let I=p[T];I&&(ve(b,I.ref),b.ref?b.queue.push(I):b.start(I))})}),(0,j.useOnce)(()=>()=>{(0,j.each)(i.ctrls,d=>d.stop(!0))});let m=S.map(d=>({...d}));return o?[m,o]:m}function oe(t,n){let e=Et.is.fun(t),[[r],o]=je(1,e?t:[t],e?n||[]:n);return e||arguments.length==2?[r,o]:r}var wt=require("react");var on=()=>ce(),sn=()=>(0,wt.useState)(on)[0];var nt=require("@react-spring/shared");var an=(t,n)=>{let e=(0,nt.useConstant)(()=>new pe(t,n));return(0,nt.useOnce)(()=>()=>{e.stop()}),e};var be=require("@react-spring/shared");function ht(t,n,e){let r=be.is.fun(n)&&n;r&&!e&&(e=[]);let o=!0,s,u=je(t,(i,l)=>{let p=r?r(i,l):n;return s=p.ref,o=o&&p.reverse,p},e||[{}]);if((0,be.useIsomorphicLayoutEffect)(()=>{(0,be.each)(u[1].current,(i,l)=>{let p=u[1].current[l+(o?1:-1)];if(ve(i,s),i.ref){p&&i.update({to:p.springs});return}p?i.start({to:p.springs}):i.start()})},e),r||arguments.length==3){let i=s??u[1];return i._getProps=(l,p,R)=>{let c=be.is.fun(l)?l(R,p):l;if(c){let S=i.current[R+(c.reverse?1:-1)];return S&&(c.to=S.springs),c}},u}return u[0]}var Ne=Pt(require("react")),fe=require("react"),v=require("@react-spring/shared");function gt(t,n,e){let r=v.is.fun(n)&&n,{reset:o,sort:s,trail:u=0,expires:i=!0,exitBeforeEnter:l=!1,onDestroyed:p,ref:R,config:c}=r?r():n,S=(0,fe.useMemo)(()=>r||arguments.length==3?ce():void 0,[]),y=(0,v.toArray)(t),g=[],f=(0,fe.useRef)(null),m=o?null:f.current;(0,v.useIsomorphicLayoutEffect)(()=>{f.current=g}),(0,v.useOnce)(()=>((0,v.each)(g,h=>{S?.add(h.ctrl),h.ctrl.ref=S}),()=>{(0,v.each)(f.current,h=>{h.expired&&clearTimeout(h.expirationId),ke(h.ctrl,S),h.ctrl.stop(!0)})}));let d=pn(y,r?r():n,m),b=o&&f.current||[];(0,v.useIsomorphicLayoutEffect)(()=>(0,v.each)(b,({ctrl:h,item:x,key:L})=>{ke(h,S),k(p,x,L)}));let T=[];if(m&&(0,v.each)(m,(h,x)=>{h.expired?(clearTimeout(h.expirationId),b.push(h)):(x=T[x]=d.indexOf(h.key),~x&&(g[x]=h))}),(0,v.each)(y,(h,x)=>{g[x]||(g[x]={key:d[x],item:h,phase:"mount",ctrl:new le},g[x].ctrl.item=h)}),T.length){let h=-1,{leave:x}=r?r():n;(0,v.each)(T,(L,N)=>{let M=m[N];~L?(h=g.indexOf(M),g[h]={...M,item:y[L]}):x&&g.splice(++h,0,M)})}v.is.fun(s)&&g.sort((h,x)=>s(h.item,x.item));let I=-u,A=(0,v.useForceUpdate)(),F=ye(n),V=new Map,z=(0,fe.useRef)(new Map),w=(0,fe.useRef)(!1);(0,v.each)(g,(h,x)=>{let L=h.key,N=h.phase,M=r?r():n,G,$,Nt=k(M.delay||0,L);if(N=="mount")G=M.enter,$="enter";else{let H=d.indexOf(L)<0;if(N!="leave")if(H)G=M.leave,$="leave";else if(G=M.update)$="update";else return;else if(!H)G=M.enter,$="enter";else return}if(G=k(G,h.item,x),G=v.is.obj(G)?Se(G):{to:G},!G.config){let H=c||F.config;G.config=k(H,h.item,x,$)}I+=u;let me={...F,delay:Nt+I,ref:R,immediate:M.immediate,reset:!1,...G};if($=="enter"&&v.is.und(me.from)){let H=r?r():n,Fe=v.is.und(H.initial)||m?H.from:H.initial;me.from=k(Fe,h.item,x)}let{onResolve:Dt}=me;me.onResolve=H=>{k(Dt,H);let Fe=f.current,ue=Fe.find(qe=>qe.key===L);if(ue&&!(H.cancelled&&ue.phase!="update")&&ue.ctrl.idle){let qe=Fe.every(he=>he.ctrl.idle);if(ue.phase=="leave"){let he=k(i,ue.item);if(he!==!1){let ot=he===!0?0:he;if(ue.expired=!0,!qe&&ot>0){ot<=2147483647&&(ue.expirationId=setTimeout(A,ot));return}}}qe&&Fe.some(he=>he.expired)&&(z.current.delete(ue),l&&(w.current=!0),A())}};let St=Me(h.ctrl,me);$==="leave"&&l?z.current.set(h,{phase:$,springs:St,payload:me}):V.set(h,{phase:$,springs:St,payload:me})});let Y=(0,fe.useContext)(re),q=(0,v.usePrev)(Y),ae=Y!==q&&Be(Y);(0,v.useIsomorphicLayoutEffect)(()=>{ae&&(0,v.each)(g,h=>{h.ctrl.start({default:Y})})},[Y]),(0,v.each)(V,(h,x)=>{if(z.current.size){let L=g.findIndex(N=>N.key===x.key);g.splice(L,1)}}),(0,v.useIsomorphicLayoutEffect)(()=>{(0,v.each)(z.current.size?z.current:V,({phase:h,payload:x},L)=>{let{ctrl:N}=L;L.phase=h,S?.add(N),ae&&h=="enter"&&N.start({default:Y}),x&&(ve(N,x.ref),(N.ref||S)&&!w.current?N.update(x):(N.start(x),w.current&&(w.current=!1)))})},o?void 0:e);let K=h=>Ne.createElement(Ne.Fragment,null,g.map((x,L)=>{let{springs:N}=V.get(x)||x.ctrl,M=h({...N},x.item,x,L);return M&&M.type?Ne.createElement(M.type,{...M.props,key:v.is.str(x.key)||v.is.num(x.key)?x.key:x.ctrl.id,ref:M.ref}):M}));return S?[K,S]:K}var un=1;function pn(t,{key:n,keys:e=n},r){if(e===null){let o=new Set;return t.map(s=>{let u=r&&r.find(i=>i.item===s&&i.phase!=="leave"&&!o.has(i));return u?(o.add(u),u.key):un++})}return v.is.und(e)?t:v.is.fun(e)?t.map(e):(0,v.toArray)(e)}var _e=require("@react-spring/shared");var ln=({container:t,...n}={})=>{let[e,r]=oe(()=>({scrollX:0,scrollY:0,scrollXProgress:0,scrollYProgress:0,...n}),[]);return(0,_e.useIsomorphicLayoutEffect)(()=>{let o=(0,_e.onScroll)(({x:s,y:u})=>{r.start({scrollX:s.current,scrollXProgress:s.progress,scrollY:u.current,scrollYProgress:u.progress})},{container:t?.current||void 0});return()=>{(0,_e.each)(Object.values(e),s=>s.stop()),o()}},[]),e};var Oe=require("@react-spring/shared");var cn=({container:t,...n})=>{let[e,r]=oe(()=>({width:0,height:0,...n}),[]);return(0,Oe.useIsomorphicLayoutEffect)(()=>{let o=(0,Oe.onResize)(({width:s,height:u})=>{r.start({width:s,height:u,immediate:e.width.get()===0||e.height.get()===0})},{container:t?.current||void 0});return()=>{(0,Oe.each)(Object.values(e),s=>s.stop()),o()}},[]),e};var rt=require("react"),De=require("@react-spring/shared");var fn={any:0,all:1};function dn(t,n){let[e,r]=(0,rt.useState)(!1),o=(0,rt.useRef)(),s=De.is.fun(t)&&t,u=s?s():{},{to:i={},from:l={},...p}=u,R=s?n:t,[c,S]=oe(()=>({from:l,...p}),[]);return(0,De.useIsomorphicLayoutEffect)(()=>{let y=o.current,{root:g,once:f,amount:m="any",...d}=R??{};if(!y||f&&e||typeof IntersectionObserver>"u")return;let b=new WeakMap,T=()=>(i&&S.start(i),r(!0),f?void 0:()=>{l&&S.start(l),r(!1)}),I=F=>{F.forEach(V=>{let z=b.get(V.target);if(V.isIntersecting!==!!z)if(V.isIntersecting){let w=T();De.is.fun(w)?b.set(V.target,w):A.unobserve(V.target)}else z&&(z(),b.delete(V.target))})},A=new IntersectionObserver(I,{root:g&&g.current||void 0,threshold:typeof m=="number"||Array.isArray(m)?m:fn[m],...d});return A.observe(y),()=>A.unobserve(y)},[R]),s?[o,c]:[o,e]}function mn({children:t,...n}){return t(oe(n))}var Lt=require("@react-spring/shared");function hn({items:t,children:n,...e}){let r=ht(t.length,e);return t.map((o,s)=>{let u=n(o,s);return Lt.is.fun(u)?u(r[s]):u})}function gn({items:t,children:n,...e}){return gt(t,e)(n)}var jt=require("@react-spring/shared");var C=require("@react-spring/shared");var ie=require("@react-spring/animated"),se=class extends te{constructor(e,r){super();this.source=e;this.idle=!0;this._active=new Set;this.calc=(0,C.createInterpolator)(...r);let o=this._get(),s=(0,ie.getAnimatedType)(o);(0,ie.setAnimated)(this,s.create(o))}advance(e){let r=this._get(),o=this.get();(0,C.isEqual)(r,o)||((0,ie.getAnimated)(this).setValue(r),this._onChange(r,this.idle)),!this.idle&&Mt(this._active)&&yt(this)}_get(){let e=C.is.arr(this.source)?this.source.map(C.getFluidValue):(0,C.toArray)((0,C.getFluidValue)(this.source));return this.calc(...e)}_start(){this.idle&&!Mt(this._active)&&(this.idle=!1,(0,C.each)((0,ie.getPayload)(this),e=>{e.done=!1}),C.Globals.skipAnimation?(C.raf.batchedUpdates(()=>this.advance()),yt(this)):C.frameLoop.start(this))}_attach(){let e=1;(0,C.each)((0,C.toArray)(this.source),r=>{(0,C.hasFluidValue)(r)&&(0,C.addFluidObserver)(r,this),Ee(r)&&(r.idle||this._active.add(r),e=Math.max(e,r.priority+1))}),this.priority=e,this._start()}_detach(){(0,C.each)((0,C.toArray)(this.source),e=>{(0,C.hasFluidValue)(e)&&(0,C.removeFluidObserver)(e,this)}),this._active.clear(),yt(this)}eventObserved(e){e.type=="change"?e.idle?this.advance():(this._active.add(e.parent),this._start()):e.type=="idle"?this._active.delete(e.parent):e.type=="priority"&&(this.priority=(0,C.toArray)(this.source).reduce((r,o)=>Math.max(r,(Ee(o)?o.priority:0)+1),0))}};function yn(t){return t.idle!==!1}function Mt(t){return!t.size||Array.from(t).every(yn)}function yt(t){t.idle||(t.idle=!0,(0,C.each)((0,ie.getPayload)(t),n=>{n.done=!0}),(0,C.callFluidObservers)(t,{type:"idle",parent:t}))}var Sn=(t,...n)=>new se(t,n),Pn=(t,...n)=>((0,jt.deprecateInterpolate)(),new se(t,n));var Ae=require("@react-spring/shared");Ae.Globals.assign({createStringInterpolator:Ae.createStringInterpolator,to:(t,n)=>new se(t,n)});var Tn=Ae.frameLoop.advance;var de=require("@react-spring/shared");E(U,require("@react-spring/types"),module.exports);
//# sourceMappingURL=react-spring_core.production.min.cjs.map