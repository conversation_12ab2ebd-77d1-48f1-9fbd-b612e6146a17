import{each as ge,useIsomorphicLayoutEffect as an}from"@react-spring/shared";import{is as X,toArray as $t,eachProp as dt,getFluidValue as Zt,isAnimatedString as en,Globals as tn}from"@react-spring/shared";function I(t,...n){return X.fun(t)?t(...n):t}var te=(t,n)=>t===!0||!!(n&&t&&(X.fun(t)?t(n):$t(t).includes(n))),et=(t,n)=>X.obj(t)?n&&t[n]:t;var ke=(t,n)=>t.default===!0?t[n]:t.default?t.default[n]:void 0,nn=t=>t,ne=(t,n=nn)=>{let e=rn;t.default&&t.default!==!0&&(t=t.default,e=Object.keys(t));let r={};for(let o of e){let s=n(t[o],o);X.und(s)||(r[o]=s)}return r},rn=["config","onProps","onStart","onChange","onPause","onResume","onRest"],on={config:1,from:1,to:1,ref:1,loop:1,reset:1,pause:1,cancel:1,reverse:1,immediate:1,default:1,delay:1,onProps:1,onStart:1,onChange:1,onPause:1,onResume:1,onRest:1,onResolve:1,items:1,trail:1,sort:1,expires:1,initial:1,enter:1,update:1,leave:1,children:1,onDestroyed:1,keys:1,callId:1,parentId:1};function sn(t){let n={},e=0;if(dt(t,(r,o)=>{on[o]||(n[o]=r,e++)}),e)return n}function de(t){let n=sn(t);if(n){let e={to:n};return dt(t,(r,o)=>o in n||(e[o]=r)),e}return{...t}}function me(t){return t=Zt(t),X.arr(t)?t.map(me):en(t)?tn.createStringInterpolator({range:[0,1],output:[t,t]})(1):t}function Ue(t){for(let n in t)return!0;return!1}function Ee(t){return X.fun(t)||X.arr(t)&&X.obj(t[0])}function xe(t,n){t.ref?.delete(t),n?.delete(t)}function he(t,n){n&&t.ref!==n&&(t.ref?.delete(t),n.add(t),t.ref=n)}function wr(t,n,e=1e3){an(()=>{if(n){let r=0;ge(t,(o,s)=>{let a=o.current;if(a.length){let i=e*n[s];isNaN(i)?i=r:r=i,ge(a,p=>{ge(p.queue,u=>{let b=u.delay;u.delay=l=>i+I(b||0,l)})}),o.start()}})}else{let r=Promise.resolve();ge(t,o=>{let s=o.current;if(s.length){let a=s.map(i=>{let p=i.queue;return i.queue=[],p});r=r.then(()=>(ge(s,(i,p)=>ge(a[p]||[],u=>i.queue.push(u))),Promise.all(o.start())))}})}})}import{is as Qn}from"@react-spring/shared";import{useContext as Mn,useMemo as Xe,useRef as Nt}from"react";import{is as jn,each as Ye,usePrev as Dt,useOnce as Nn,useForceUpdate as Dn,useIsomorphicLayoutEffect as qn}from"@react-spring/shared";import{is as R,raf as ve,each as At,isEqual as H,toArray as Rt,eachProp as Pn,frameLoop as Tn,flushCalls as Qe,getFluidValue as ie,isAnimatedString as xn,Globals as bn,callFluidObservers as An,hasFluidValue as Se,addFluidObserver as Rn,removeFluidObserver as vn,getFluidObservers as vt}from"@react-spring/shared";import{AnimatedValue as Cn,AnimatedString as Ct,getPayload as In,getAnimated as ae,setAnimated as Vn,getAnimatedType as It}from"@react-spring/animated";import{is as re,easings as un}from"@react-spring/shared";var mt={default:{tension:170,friction:26},gentle:{tension:120,friction:14},wobbly:{tension:180,friction:12},stiff:{tension:210,friction:20},slow:{tension:280,friction:60},molasses:{tension:280,friction:120}};var tt={...mt.default,mass:1,damping:1,easing:un.linear,clamp:!1},we=class{constructor(){this.velocity=0;Object.assign(this,tt)}};function gt(t,n,e){e&&(e={...e},ht(e,n),n={...e,...n}),ht(t,n),Object.assign(t,n);for(let a in tt)t[a]==null&&(t[a]=tt[a]);let{frequency:r,damping:o}=t,{mass:s}=t;return re.und(r)||(r<.01&&(r=.01),o<0&&(o=0),t.tension=Math.pow(2*Math.PI/r,2)*s,t.friction=4*Math.PI*o*s/r),t}function ht(t,n){if(!re.und(n.decay))t.duration=void 0;else{let e=!re.und(n.tension)||!re.und(n.friction);(e||!re.und(n.frequency)||!re.und(n.damping)||!re.und(n.mass))&&(t.duration=void 0,t.decay=void 0),e&&(t.frequency=void 0)}}var yt=[],Le=class{constructor(){this.changed=!1;this.values=yt;this.toValues=null;this.fromValues=yt;this.config=new we;this.immediate=!1}};import{is as pn,raf as St,Globals as ln}from"@react-spring/shared";function Me(t,{key:n,props:e,defaultProps:r,state:o,actions:s}){return new Promise((a,i)=>{let p,u,b=te(e.cancel??r?.cancel,n);if(b)g();else{pn.und(e.pause)||(o.paused=te(e.pause,n));let h=r?.pause;h!==!0&&(h=o.paused||te(h,n)),p=I(e.delay||0,n),h?(o.resumeQueue.add(y),s.pause()):(s.resume(),y())}function l(){o.resumeQueue.add(y),o.timeouts.delete(u),u.cancel(),p=u.time-St.now()}function y(){p>0&&!ln.skipAnimation?(o.delayed=!0,u=St.setTimeout(g,p),o.pauseQueue.add(l),o.timeouts.add(u)):g()}function g(){o.delayed&&(o.delayed=!1),o.pauseQueue.delete(l),o.timeouts.delete(u),t<=(o.cancelId||0)&&(b=!0);try{s.start({...e,callId:t,cancel:b},a)}catch(h){i(h)}}})}import{is as je,raf as cn,flush as fn,eachProp as dn,Globals as Pt}from"@react-spring/shared";var be=(t,n)=>n.length==1?n[0]:n.some(e=>e.cancelled)?q(t.get()):n.every(e=>e.noop)?nt(t.get()):w(t.get(),n.every(e=>e.finished)),nt=t=>({value:t,noop:!0,finished:!0,cancelled:!1}),w=(t,n,e=!1)=>({value:t,finished:n,cancelled:e}),q=t=>({value:t,cancelled:!0,finished:!1});function De(t,n,e,r){let{callId:o,parentId:s,onRest:a}=n,{asyncTo:i,promise:p}=e;return!s&&t===i&&!n.reset?p:e.promise=(async()=>{e.asyncId=o,e.asyncTo=t;let u=ne(n,(d,f)=>f==="onRest"?void 0:d),b,l,y=new Promise((d,f)=>(b=d,l=f)),g=d=>{let f=o<=(e.cancelId||0)&&q(r)||o!==e.asyncId&&w(r,!1);if(f)throw d.result=f,l(d),d},h=(d,f)=>{let T=new Ae,S=new Ne;return(async()=>{if(Pt.skipAnimation)throw oe(e),S.result=w(r,!1),l(S),S;g(T);let A=je.obj(d)?{...d}:{...f,to:d};A.parentId=o,dn(u,(C,v)=>{je.und(A[v])&&(A[v]=C)});let x=await r.start(A);return g(T),e.paused&&await new Promise(C=>{e.resumeQueue.add(C)}),x})()},c;if(Pt.skipAnimation)return oe(e),w(r,!1);try{let d;je.arr(t)?d=(async f=>{for(let T of f)await h(T)})(t):d=Promise.resolve(t(h,r.stop.bind(r))),await Promise.all([d.then(b),y]),c=w(r.get(),!0,!1)}catch(d){if(d instanceof Ae)c=d.result;else if(d instanceof Ne)c=d.result;else throw d}finally{o==e.asyncId&&(e.asyncId=s,e.asyncTo=s?i:void 0,e.promise=s?p:void 0)}return je.fun(a)&&cn.batchedUpdates(()=>{a(c,r,r.item)}),c})()}function oe(t,n){fn(t.timeouts,e=>e.cancel()),t.pauseQueue.clear(),t.resumeQueue.clear(),t.asyncId=t.asyncTo=t.promise=void 0,n&&(t.cancelId=n)}var Ae=class extends Error{constructor(){super("An async animation has been interrupted. You see this error because you forgot to use `await` or `.catch(...)` on its returned promise.")}},Ne=class extends Error{constructor(){super("SkipAnimationSignal")}};import{deprecateInterpolate as mn,frameLoop as hn,FluidValue as gn,Globals as Tt,callFluidObservers as xt}from"@react-spring/shared";import{getAnimated as yn}from"@react-spring/animated";var Re=t=>t instanceof Y,Sn=1,Y=class extends gn{constructor(){super(...arguments);this.id=Sn++;this._priority=0}get priority(){return this._priority}set priority(e){this._priority!=e&&(this._priority=e,this._onPriorityChange(e))}get(){let e=yn(this);return e&&e.getValue()}to(...e){return Tt.to(this,e)}interpolate(...e){return mn(),Tt.to(this,e)}toJSON(){return this.get()}observerAdded(e){e==1&&this._attach()}observerRemoved(e){e==0&&this._detach()}_attach(){}_detach(){}_onChange(e,r=!1){xt(this,{type:"change",parent:this,value:e,idle:r})}_onPriorityChange(e){this.idle||hn.sort(this),xt(this,{type:"priority",parent:this,priority:e})}};var se=Symbol.for("SpringPhase"),bt=1,rt=2,ot=4,qe=t=>(t[se]&bt)>0,Q=t=>(t[se]&rt)>0,ye=t=>(t[se]&ot)>0,st=(t,n)=>n?t[se]|=rt|bt:t[se]&=~rt,it=(t,n)=>n?t[se]|=ot:t[se]&=~ot;var ue=class extends Y{constructor(e,r){super();this.animation=new Le;this.defaultProps={};this._state={paused:!1,delayed:!1,pauseQueue:new Set,resumeQueue:new Set,timeouts:new Set};this._pendingCalls=new Set;this._lastCallId=0;this._lastToId=0;this._memoizedDuration=0;if(!R.und(e)||!R.und(r)){let o=R.obj(e)?{...e}:{...r,from:e};R.und(o.default)&&(o.default=!0),this.start(o)}}get idle(){return!(Q(this)||this._state.asyncTo)||ye(this)}get goal(){return ie(this.animation.to)}get velocity(){let e=ae(this);return e instanceof Cn?e.lastVelocity||0:e.getPayload().map(r=>r.lastVelocity||0)}get hasAnimated(){return qe(this)}get isAnimating(){return Q(this)}get isPaused(){return ye(this)}get isDelayed(){return this._state.delayed}advance(e){let r=!0,o=!1,s=this.animation,{toValues:a}=s,{config:i}=s,p=In(s.to);!p&&Se(s.to)&&(a=Rt(ie(s.to))),s.values.forEach((l,y)=>{if(l.done)return;let g=l.constructor==Ct?1:p?p[y].lastPosition:a[y],h=s.immediate,c=g;if(!h){if(c=l.lastPosition,i.tension<=0){l.done=!0;return}let d=l.elapsedTime+=e,f=s.fromValues[y],T=l.v0!=null?l.v0:l.v0=R.arr(i.velocity)?i.velocity[y]:i.velocity,S,A=i.precision||(f==g?.005:Math.min(1,Math.abs(g-f)*.001));if(R.und(i.duration))if(i.decay){let x=i.decay===!0?.998:i.decay,C=Math.exp(-(1-x)*d);c=f+T/(1-x)*(1-C),h=Math.abs(l.lastPosition-c)<=A,S=T*C}else{S=l.lastVelocity==null?T:l.lastVelocity;let x=i.restVelocity||A/10,C=i.clamp?0:i.bounce,v=!R.und(C),U=f==g?l.v0>0:f<g,V,M=!1,k=1,B=Math.ceil(e/k);for(let L=0;L<B&&(V=Math.abs(S)>x,!(!V&&(h=Math.abs(g-c)<=A,h)));++L){v&&(M=c==g||c>g==U,M&&(S=-S*C,c=g));let m=-i.tension*1e-6*(c-g),P=-i.friction*.001*S,_=(m+P)/i.mass;S=S+_*k,c=c+S*k}}else{let x=1;i.duration>0&&(this._memoizedDuration!==i.duration&&(this._memoizedDuration=i.duration,l.durationProgress>0&&(l.elapsedTime=i.duration*l.durationProgress,d=l.elapsedTime+=e)),x=(i.progress||0)+d/this._memoizedDuration,x=x>1?1:x<0?0:x,l.durationProgress=x),c=f+i.easing(x)*(g-f),S=(c-l.lastPosition)/e,h=x==1}l.lastVelocity=S,Number.isNaN(c)&&(console.warn("Got NaN while animating:",this),h=!0)}p&&!p[y].done&&(h=!1),h?l.done=!0:r=!1,l.setValue(c,i.round)&&(o=!0)});let u=ae(this),b=u.getValue();if(r){let l=ie(s.to);(b!==l||o)&&!i.decay?(u.setValue(l),this._onChange(l)):o&&i.decay&&this._onChange(b),this._stop()}else o&&this._onChange(b)}set(e){return ve.batchedUpdates(()=>{this._stop(),this._focus(e),this._set(e)}),this}pause(){this._update({pause:!0})}resume(){this._update({pause:!1})}finish(){if(Q(this)){let{to:e,config:r}=this.animation;ve.batchedUpdates(()=>{this._onStart(),r.decay||this._set(e,!1),this._stop()})}return this}update(e){return(this.queue||(this.queue=[])).push(e),this}start(e,r){let o;return R.und(e)?(o=this.queue||[],this.queue=[]):o=[R.obj(e)?e:{...r,to:e}],Promise.all(o.map(s=>this._update(s))).then(s=>be(this,s))}stop(e){let{to:r}=this.animation;return this._focus(this.get()),oe(this._state,e&&this._lastCallId),ve.batchedUpdates(()=>this._stop(r,e)),this}reset(){this._update({reset:!0})}eventObserved(e){e.type=="change"?this._start():e.type=="priority"&&(this.priority=e.priority+1)}_prepareNode(e){let r=this.key||"",{to:o,from:s}=e;o=R.obj(o)?o[r]:o,(o==null||Ee(o))&&(o=void 0),s=R.obj(s)?s[r]:s,s==null&&(s=void 0);let a={to:o,from:s};return qe(this)||(e.reverse&&([o,s]=[s,o]),s=ie(s),R.und(s)?ae(this)||this._set(o):this._set(s)),a}_update({...e},r){let{key:o,defaultProps:s}=this;e.default&&Object.assign(s,ne(e,(p,u)=>/^on/.test(u)?et(p,o):p)),_t(this,e,"onProps"),Ie(this,"onProps",e,this);let a=this._prepareNode(e);if(Object.isFrozen(this))throw Error("Cannot animate a `SpringValue` object that is frozen. Did you forget to pass your component to `animated(...)` before animating its props?");let i=this._state;return Me(++this._lastCallId,{key:o,props:e,defaultProps:s,state:i,actions:{pause:()=>{ye(this)||(it(this,!0),Qe(i.pauseQueue),Ie(this,"onPause",w(this,Ce(this,this.animation.to)),this))},resume:()=>{ye(this)&&(it(this,!1),Q(this)&&this._resume(),Qe(i.resumeQueue),Ie(this,"onResume",w(this,Ce(this,this.animation.to)),this))},start:this._merge.bind(this,a)}}).then(p=>{if(e.loop&&p.finished&&!(r&&p.noop)){let u=at(e);if(u)return this._update(u,!0)}return p})}_merge(e,r,o){if(r.cancel)return this.stop(!0),o(q(this));let s=!R.und(e.to),a=!R.und(e.from);if(s||a)if(r.callId>this._lastToId)this._lastToId=r.callId;else return o(q(this));let{key:i,defaultProps:p,animation:u}=this,{to:b,from:l}=u,{to:y=b,from:g=l}=e;a&&!s&&(!r.default||R.und(y))&&(y=g),r.reverse&&([y,g]=[g,y]);let h=!H(g,l);h&&(u.from=g),g=ie(g);let c=!H(y,b);c&&this._focus(y);let d=Ee(r.to),{config:f}=u,{decay:T,velocity:S}=f;(s||a)&&(f.velocity=0),r.config&&!d&&gt(f,I(r.config,i),r.config!==p.config?I(p.config,i):void 0);let A=ae(this);if(!A||R.und(y))return o(w(this,!0));let x=R.und(r.reset)?a&&!r.default:!R.und(g)&&te(r.reset,i),C=x?g:this.get(),v=me(y),U=R.num(v)||R.arr(v)||xn(v),V=!d&&(!U||te(p.immediate||r.immediate,i));if(c){let L=It(y);if(L!==A.constructor)if(V)A=this._set(v);else throw Error(`Cannot animate between ${A.constructor.name} and ${L.name}, as the "to" prop suggests`)}let M=A.constructor,k=Se(y),B=!1;if(!k){let L=x||!qe(this)&&h;(c||L)&&(B=H(me(C),v),k=!B),(!H(u.immediate,V)&&!V||!H(f.decay,T)||!H(f.velocity,S))&&(k=!0)}if(B&&Q(this)&&(u.changed&&!x?k=!0:k||this._stop(b)),!d&&((k||Se(b))&&(u.values=A.getPayload(),u.toValues=Se(y)?null:M==Ct?[1]:Rt(v)),u.immediate!=V&&(u.immediate=V,!V&&!x&&this._set(b)),k)){let{onRest:L}=u;At(_n,P=>_t(this,r,P));let m=w(this,Ce(this,b));Qe(this._pendingCalls,m),this._pendingCalls.add(o),u.changed&&ve.batchedUpdates(()=>{u.changed=!x,L?.(m,this),x?I(p.onRest,m):u.onStart?.(m,this)})}x&&this._set(C),d?o(De(r.to,r,this._state,this)):k?this._start():Q(this)&&!c?this._pendingCalls.add(o):o(nt(C))}_focus(e){let r=this.animation;e!==r.to&&(vt(this)&&this._detach(),r.to=e,vt(this)&&this._attach())}_attach(){let e=0,{to:r}=this.animation;Se(r)&&(Rn(r,this),Re(r)&&(e=r.priority+1)),this.priority=e}_detach(){let{to:e}=this.animation;Se(e)&&vn(e,this)}_set(e,r=!0){let o=ie(e);if(!R.und(o)){let s=ae(this);if(!s||!H(o,s.getValue())){let a=It(o);!s||s.constructor!=a?Vn(this,a.create(o)):s.setValue(o),s&&ve.batchedUpdates(()=>{this._onChange(o,r)})}}return ae(this)}_onStart(){let e=this.animation;e.changed||(e.changed=!0,Ie(this,"onStart",w(this,Ce(this,e.to)),this))}_onChange(e,r){r||(this._onStart(),I(this.animation.onChange,e,this)),I(this.defaultProps.onChange,e,this),super._onChange(e,r)}_start(){let e=this.animation;ae(this).reset(ie(e.to)),e.immediate||(e.fromValues=e.values.map(r=>r.lastPosition)),Q(this)||(st(this,!0),ye(this)||this._resume())}_resume(){bn.skipAnimation?this.finish():Tn.start(this)}_stop(e,r){if(Q(this)){st(this,!1);let o=this.animation;At(o.values,a=>{a.done=!0}),o.toValues&&(o.onChange=o.onPause=o.onResume=void 0),An(this,{type:"idle",parent:this});let s=r?q(this.get()):w(this.get(),Ce(this,e??o.to));Qe(this._pendingCalls,s),o.changed&&(o.changed=!1,Ie(this,"onRest",s,this))}}};function Ce(t,n){let e=me(n),r=me(t.get());return H(r,e)}function at(t,n=t.loop,e=t.to){let r=I(n);if(r){let o=r!==!0&&de(r),s=(o||t).reverse,a=!o||o.reset;return Pe({...t,loop:n,default:!1,pause:void 0,to:!s||Ee(e)?e:void 0,from:a?t.from:void 0,reset:a,...o})}}function Pe(t){let{to:n,from:e}=t=de(t),r=new Set;return R.obj(n)&&Vt(n,r),R.obj(e)&&Vt(e,r),t.keys=r.size?Array.from(r):null,t}function Ot(t){let n=Pe(t);return R.und(n.default)&&(n.default=ne(n)),n}function Vt(t,n){Pn(t,(e,r)=>e!=null&&n.add(r))}var _n=["onStart","onRest","onChange","onPause","onResume"];function _t(t,n,e){t.animation[e]=n[e]!==ke(n,e)?et(n[e],t.key):void 0}function Ie(t,n,...e){t.animation[n]?.(...e),t.defaultProps[n]?.(...e)}import{is as z,raf as kt,each as pe,noop as Ft,flush as ut,toArray as Ve,eachProp as Ut,flushCalls as On,addFluidObserver as Et}from"@react-spring/shared";var Fn=["onStart","onChange","onRest"],kn=1,le=class{constructor(n,e){this.id=kn++;this.springs={};this.queue=[];this._lastAsyncId=0;this._active=new Set;this._changed=new Set;this._started=!1;this._state={paused:!1,pauseQueue:new Set,resumeQueue:new Set,timeouts:new Set};this._events={onStart:new Map,onChange:new Map,onRest:new Map};this._onFrame=this._onFrame.bind(this),e&&(this._flush=e),n&&this.start({default:!0,...n})}get idle(){return!this._state.asyncTo&&Object.values(this.springs).every(n=>n.idle&&!n.isDelayed&&!n.isPaused)}get item(){return this._item}set item(n){this._item=n}get(){let n={};return this.each((e,r)=>n[r]=e.get()),n}set(n){for(let e in n){let r=n[e];z.und(r)||this.springs[e].set(r)}}update(n){return n&&this.queue.push(Pe(n)),this}start(n){let{queue:e}=this;return n?e=Ve(n).map(Pe):this.queue=[],this._flush?this._flush(this,e):(jt(this,e),ze(this,e))}stop(n,e){if(n!==!!n&&(e=n),e){let r=this.springs;pe(Ve(e),o=>r[o].stop(!!n))}else oe(this._state,this._lastAsyncId),this.each(r=>r.stop(!!n));return this}pause(n){if(z.und(n))this.start({pause:!0});else{let e=this.springs;pe(Ve(n),r=>e[r].pause())}return this}resume(n){if(z.und(n))this.start({pause:!1});else{let e=this.springs;pe(Ve(n),r=>e[r].resume())}return this}each(n){Ut(this.springs,n)}_onFrame(){let{onStart:n,onChange:e,onRest:r}=this._events,o=this._active.size>0,s=this._changed.size>0;(o&&!this._started||s&&!this._started)&&(this._started=!0,ut(n,([p,u])=>{u.value=this.get(),p(u,this,this._item)}));let a=!o&&this._started,i=s||a&&r.size?this.get():null;s&&e.size&&ut(e,([p,u])=>{u.value=i,p(u,this,this._item)}),a&&(this._started=!1,ut(r,([p,u])=>{u.value=i,p(u,this,this._item)}))}eventObserved(n){if(n.type=="change")this._changed.add(n.parent),n.idle||this._active.add(n.parent);else if(n.type=="idle")this._active.delete(n.parent);else return;kt.onFrame(this._onFrame)}};function ze(t,n){return Promise.all(n.map(e=>wt(t,e))).then(e=>be(t,e))}async function wt(t,n,e){let{keys:r,to:o,from:s,loop:a,onRest:i,onResolve:p}=n,u=z.obj(n.default)&&n.default;a&&(n.loop=!1),o===!1&&(n.to=null),s===!1&&(n.from=null);let b=z.arr(o)||z.fun(o)?o:void 0;b?(n.to=void 0,n.onRest=void 0,u&&(u.onRest=void 0)):pe(Fn,c=>{let d=n[c];if(z.fun(d)){let f=t._events[c];n[c]=({finished:T,cancelled:S})=>{let A=f.get(d);A?(T||(A.finished=!1),S&&(A.cancelled=!0)):f.set(d,{value:null,finished:T||!1,cancelled:S||!1})},u&&(u[c]=n[c])}});let l=t._state;n.pause===!l.paused?(l.paused=n.pause,On(n.pause?l.pauseQueue:l.resumeQueue)):l.paused&&(n.pause=!0);let y=(r||Object.keys(t.springs)).map(c=>t.springs[c].start(n)),g=n.cancel===!0||ke(n,"cancel")===!0;(b||g&&l.asyncId)&&y.push(Me(++t._lastAsyncId,{props:n,state:l,actions:{pause:Ft,resume:Ft,start(c,d){g?(oe(l,t._lastAsyncId),d(q(t))):(c.onRest=i,d(De(b,c,l,t)))}}})),l.paused&&await new Promise(c=>{l.resumeQueue.add(c)});let h=be(t,await Promise.all(y));if(a&&h.finished&&!(e&&h.noop)){let c=at(n,a,o);if(c)return jt(t,[c]),wt(t,c,!0)}return p&&kt.batchedUpdates(()=>p(h,t,t.item)),h}function _e(t,n){let e={...t.springs};return n&&pe(Ve(n),r=>{z.und(r.keys)&&(r=Pe(r)),z.obj(r.to)||(r={...r,to:void 0}),Mt(e,r,o=>Lt(o))}),pt(t,e),e}function pt(t,n){Ut(n,(e,r)=>{t.springs[r]||(t.springs[r]=e,Et(e,t))})}function Lt(t,n){let e=new ue;return e.key=t,n&&Et(e,n),e}function Mt(t,n,e){n.keys&&pe(n.keys,r=>{(t[r]||(t[r]=e(r)))._prepareNode(n)})}function jt(t,n){pe(n,e=>{Mt(t.springs,e,r=>Lt(r,t))})}import*as Be from"react";import{useContext as Un}from"react";import{useMemoOne as En}from"@react-spring/shared";var J=({children:t,...n})=>{let e=Un(Ge),r=n.pause||!!e.pause,o=n.immediate||!!e.immediate;n=En(()=>({pause:r,immediate:o}),[r,o]);let{Provider:s}=Ge;return Be.createElement(s,{value:n},t)},Ge=wn(J,{});J.Provider=Ge.Provider;J.Consumer=Ge.Consumer;function wn(t,n){return Object.assign(t,Be.createContext(n)),t.Provider._context=t,t.Consumer._context=t,t}import{each as ce,is as Ke,deprecateDirectCall as Ln}from"@react-spring/shared";var fe=()=>{let t=[],n=function(r){Ln();let o=[];return ce(t,(s,a)=>{if(Ke.und(r))o.push(s.start());else{let i=e(r,s,a);i&&o.push(s.start(i))}}),o};n.current=t,n.add=function(r){t.includes(r)||t.push(r)},n.delete=function(r){let o=t.indexOf(r);~o&&t.splice(o,1)},n.pause=function(){return ce(t,r=>r.pause(...arguments)),this},n.resume=function(){return ce(t,r=>r.resume(...arguments)),this},n.set=function(r){ce(t,(o,s)=>{let a=Ke.fun(r)?r(s,o):r;a&&o.set(a)})},n.start=function(r){let o=[];return ce(t,(s,a)=>{if(Ke.und(r))o.push(s.start());else{let i=this._getProps(r,s,a);i&&o.push(s.start(i))}}),o},n.stop=function(){return ce(t,r=>r.stop(...arguments)),this},n.update=function(r){return ce(t,(o,s)=>o.update(this._getProps(r,o,s))),this};let e=function(r,o,s){return Ke.fun(r)?r(s,o):r};return n._getProps=e,n};function He(t,n,e){let r=jn.fun(n)&&n;r&&!e&&(e=[]);let o=Xe(()=>r||arguments.length==3?fe():void 0,[]),s=Nt(0),a=Dn(),i=Xe(()=>({ctrls:[],queue:[],flush(f,T){let S=_e(f,T);return s.current>0&&!i.queue.length&&!Object.keys(S).some(x=>!f.springs[x])?ze(f,T):new Promise(x=>{pt(f,S),i.queue.push(()=>{x(ze(f,T))}),a()})}}),[]),p=Nt([...i.ctrls]),u=[],b=Dt(t)||0;Xe(()=>{Ye(p.current.slice(t,b),f=>{xe(f,o),f.stop(!0)}),p.current.length=t,l(b,t)},[t]),Xe(()=>{l(0,Math.min(b,t))},e);function l(f,T){for(let S=f;S<T;S++){let A=p.current[S]||(p.current[S]=new le(null,i.flush)),x=r?r(S,A):n[S];x&&(u[S]=Ot(x))}}let y=p.current.map((f,T)=>_e(f,u[T])),g=Mn(J),h=Dt(g),c=g!==h&&Ue(g);qn(()=>{s.current++,i.ctrls=p.current;let{queue:f}=i;f.length&&(i.queue=[],Ye(f,T=>T())),Ye(p.current,(T,S)=>{o?.add(T),c&&T.start({default:g});let A=u[S];A&&(he(T,A.ref),T.ref?T.queue.push(A):T.start(A))})}),Nn(()=>()=>{Ye(i.ctrls,f=>f.stop(!0))});let d=y.map(f=>({...f}));return o?[d,o]:d}function W(t,n){let e=Qn.fun(t),[[r],o]=He(1,e?t:[t],e?n||[]:n);return e||arguments.length==2?[r,o]:r}import{useState as zn}from"react";var Gn=()=>fe(),Xo=()=>zn(Gn)[0];import{useConstant as Bn,useOnce as Kn}from"@react-spring/shared";var Wo=(t,n)=>{let e=Bn(()=>new ue(t,n));return Kn(()=>()=>{e.stop()}),e};import{each as Xn,is as qt,useIsomorphicLayoutEffect as Yn}from"@react-spring/shared";function Qt(t,n,e){let r=qt.fun(n)&&n;r&&!e&&(e=[]);let o=!0,s,a=He(t,(i,p)=>{let u=r?r(i,p):n;return s=u.ref,o=o&&u.reverse,u},e||[{}]);if(Yn(()=>{Xn(a[1].current,(i,p)=>{let u=a[1].current[p+(o?1:-1)];if(he(i,s),i.ref){u&&i.update({to:u.springs});return}u?i.start({to:u.springs}):i.start()})},e),r||arguments.length==3){let i=s??a[1];return i._getProps=(p,u,b)=>{let l=qt.fun(p)?p(b,u):p;if(l){let y=i.current[b+(l.reverse?1:-1)];return y&&(l.to=y.springs),l}},a}return a[0]}import*as Oe from"react";import{useContext as Hn,useRef as lt,useMemo as Jn}from"react";import{is as G,toArray as zt,useForceUpdate as Wn,useOnce as $n,usePrev as Zn,each as N,useIsomorphicLayoutEffect as Je}from"@react-spring/shared";function Gt(t,n,e){let r=G.fun(n)&&n,{reset:o,sort:s,trail:a=0,expires:i=!0,exitBeforeEnter:p=!1,onDestroyed:u,ref:b,config:l}=r?r():n,y=Jn(()=>r||arguments.length==3?fe():void 0,[]),g=zt(t),h=[],c=lt(null),d=o?null:c.current;Je(()=>{c.current=h}),$n(()=>(N(h,m=>{y?.add(m.ctrl),m.ctrl.ref=y}),()=>{N(c.current,m=>{m.expired&&clearTimeout(m.expirationId),xe(m.ctrl,y),m.ctrl.stop(!0)})}));let f=tr(g,r?r():n,d),T=o&&c.current||[];Je(()=>N(T,({ctrl:m,item:P,key:_})=>{xe(m,y),I(u,P,_)}));let S=[];if(d&&N(d,(m,P)=>{m.expired?(clearTimeout(m.expirationId),T.push(m)):(P=S[P]=f.indexOf(m.key),~P&&(h[P]=m))}),N(g,(m,P)=>{h[P]||(h[P]={key:f[P],item:m,phase:"mount",ctrl:new le},h[P].ctrl.item=m)}),S.length){let m=-1,{leave:P}=r?r():n;N(S,(_,F)=>{let O=d[F];~_?(m=h.indexOf(O),h[m]={...O,item:g[_]}):P&&h.splice(++m,0,O)})}G.fun(s)&&h.sort((m,P)=>s(m.item,P.item));let A=-a,x=Wn(),C=ne(n),v=new Map,U=lt(new Map),V=lt(!1);N(h,(m,P)=>{let _=m.key,F=m.phase,O=r?r():n,E,D,Jt=I(O.delay||0,_);if(F=="mount")E=O.enter,D="enter";else{let j=f.indexOf(_)<0;if(F!="leave")if(j)E=O.leave,D="leave";else if(E=O.update)D="update";else return;else if(!j)E=O.enter,D="enter";else return}if(E=I(E,m.item,P),E=G.obj(E)?de(E):{to:E},!E.config){let j=l||C.config;E.config=I(j,m.item,P,D)}A+=a;let Z={...C,delay:Jt+A,ref:b,immediate:O.immediate,reset:!1,...E};if(D=="enter"&&G.und(Z.from)){let j=r?r():n,Te=G.und(j.initial)||d?j.from:j.initial;Z.from=I(Te,m.item,P)}let{onResolve:Wt}=Z;Z.onResolve=j=>{I(Wt,j);let Te=c.current,K=Te.find(Fe=>Fe.key===_);if(K&&!(j.cancelled&&K.phase!="update")&&K.ctrl.idle){let Fe=Te.every(ee=>ee.ctrl.idle);if(K.phase=="leave"){let ee=I(i,K.item);if(ee!==!1){let Ze=ee===!0?0:ee;if(K.expired=!0,!Fe&&Ze>0){Ze<=2147483647&&(K.expirationId=setTimeout(x,Ze));return}}}Fe&&Te.some(ee=>ee.expired)&&(U.current.delete(K),p&&(V.current=!0),x())}};let ft=_e(m.ctrl,Z);D==="leave"&&p?U.current.set(m,{phase:D,springs:ft,payload:Z}):v.set(m,{phase:D,springs:ft,payload:Z})});let M=Hn(J),k=Zn(M),B=M!==k&&Ue(M);Je(()=>{B&&N(h,m=>{m.ctrl.start({default:M})})},[M]),N(v,(m,P)=>{if(U.current.size){let _=h.findIndex(F=>F.key===P.key);h.splice(_,1)}}),Je(()=>{N(U.current.size?U.current:v,({phase:m,payload:P},_)=>{let{ctrl:F}=_;_.phase=m,y?.add(F),B&&m=="enter"&&F.start({default:M}),P&&(he(F,P.ref),(F.ref||y)&&!V.current?F.update(P):(F.start(P),V.current&&(V.current=!1)))})},o?void 0:e);let L=m=>Oe.createElement(Oe.Fragment,null,h.map((P,_)=>{let{springs:F}=v.get(P)||P.ctrl,O=m({...F},P.item,P,_);return O&&O.type?Oe.createElement(O.type,{...O.props,key:G.str(P.key)||G.num(P.key)?P.key:P.ctrl.id,ref:O.ref}):O}));return y?[L,y]:L}var er=1;function tr(t,{key:n,keys:e=n},r){if(e===null){let o=new Set;return t.map(s=>{let a=r&&r.find(i=>i.item===s&&i.phase!=="leave"&&!o.has(i));return a?(o.add(a),a.key):er++})}return G.und(e)?t:G.fun(e)?t.map(e):zt(e)}import{each as nr,onScroll as rr,useIsomorphicLayoutEffect as or}from"@react-spring/shared";var hs=({container:t,...n}={})=>{let[e,r]=W(()=>({scrollX:0,scrollY:0,scrollXProgress:0,scrollYProgress:0,...n}),[]);return or(()=>{let o=rr(({x:s,y:a})=>{r.start({scrollX:s.current,scrollXProgress:s.progress,scrollY:a.current,scrollYProgress:a.progress})},{container:t?.current||void 0});return()=>{nr(Object.values(e),s=>s.stop()),o()}},[]),e};import{onResize as sr,each as ir,useIsomorphicLayoutEffect as ar}from"@react-spring/shared";var Ps=({container:t,...n})=>{let[e,r]=W(()=>({width:0,height:0,...n}),[]);return ar(()=>{let o=sr(({width:s,height:a})=>{r.start({width:s,height:a,immediate:e.width.get()===0||e.height.get()===0})},{container:t?.current||void 0});return()=>{ir(Object.values(e),s=>s.stop()),o()}},[]),e};import{useRef as ur,useState as pr}from"react";import{is as Bt,useIsomorphicLayoutEffect as lr}from"@react-spring/shared";var cr={any:0,all:1};function Cs(t,n){let[e,r]=pr(!1),o=ur(),s=Bt.fun(t)&&t,a=s?s():{},{to:i={},from:p={},...u}=a,b=s?n:t,[l,y]=W(()=>({from:p,...u}),[]);return lr(()=>{let g=o.current,{root:h,once:c,amount:d="any",...f}=b??{};if(!g||c&&e||typeof IntersectionObserver>"u")return;let T=new WeakMap,S=()=>(i&&y.start(i),r(!0),c?void 0:()=>{p&&y.start(p),r(!1)}),A=C=>{C.forEach(v=>{let U=T.get(v.target);if(v.isIntersecting!==!!U)if(v.isIntersecting){let V=S();Bt.fun(V)?T.set(v.target,V):x.unobserve(v.target)}else U&&(U(),T.delete(v.target))})},x=new IntersectionObserver(A,{root:h&&h.current||void 0,threshold:typeof d=="number"||Array.isArray(d)?d:cr[d],...f});return x.observe(g),()=>x.unobserve(g)},[b]),s?[o,l]:[o,e]}function qs({children:t,...n}){return t(W(n))}import{is as fr}from"@react-spring/shared";function Bs({items:t,children:n,...e}){let r=Qt(t.length,e);return t.map((o,s)=>{let a=n(o,s);return fr.fun(a)?a(r[s]):a})}function Ys({items:t,children:n,...e}){return Gt(t,e)(n)}import{deprecateInterpolate as Cr}from"@react-spring/shared";import{is as dr,raf as mr,each as $e,isEqual as hr,toArray as We,frameLoop as gr,getFluidValue as Kt,createInterpolator as yr,Globals as Sr,callFluidObservers as Pr,addFluidObserver as Tr,removeFluidObserver as xr,hasFluidValue as Xt}from"@react-spring/shared";import{getAnimated as br,setAnimated as Ar,getAnimatedType as Rr,getPayload as Ht}from"@react-spring/animated";var $=class extends Y{constructor(e,r){super();this.source=e;this.idle=!0;this._active=new Set;this.calc=yr(...r);let o=this._get(),s=Rr(o);Ar(this,s.create(o))}advance(e){let r=this._get(),o=this.get();hr(r,o)||(br(this).setValue(r),this._onChange(r,this.idle)),!this.idle&&Yt(this._active)&&ct(this)}_get(){let e=dr.arr(this.source)?this.source.map(Kt):We(Kt(this.source));return this.calc(...e)}_start(){this.idle&&!Yt(this._active)&&(this.idle=!1,$e(Ht(this),e=>{e.done=!1}),Sr.skipAnimation?(mr.batchedUpdates(()=>this.advance()),ct(this)):gr.start(this))}_attach(){let e=1;$e(We(this.source),r=>{Xt(r)&&Tr(r,this),Re(r)&&(r.idle||this._active.add(r),e=Math.max(e,r.priority+1))}),this.priority=e,this._start()}_detach(){$e(We(this.source),e=>{Xt(e)&&xr(e,this)}),this._active.clear(),ct(this)}eventObserved(e){e.type=="change"?e.idle?this.advance():(this._active.add(e.parent),this._start()):e.type=="idle"?this._active.delete(e.parent):e.type=="priority"&&(this.priority=We(this.source).reduce((r,o)=>Math.max(r,(Re(o)?o.priority:0)+1),0))}};function vr(t){return t.idle!==!1}function Yt(t){return!t.size||Array.from(t).every(vr)}function ct(t){t.idle||(t.idle=!0,$e(Ht(t),n=>{n.done=!0}),Pr(t,{type:"idle",parent:t}))}var ui=(t,...n)=>new $(t,n),pi=(t,...n)=>(Cr(),new $(t,n));import{Globals as Ir,frameLoop as Vr,createStringInterpolator as _r}from"@react-spring/shared";Ir.assign({createStringInterpolator:_r,to:(t,n)=>new $(t,n)});var di=Vr.advance;import{createInterpolator as Ui,useIsomorphicLayoutEffect as Ei,useReducedMotion as wi,easings as Li}from"@react-spring/shared";export*from"@react-spring/types";export{Ae as BailSignal,le as Controller,Y as FrameValue,Ir as Globals,$ as Interpolation,qs as Spring,J as SpringContext,fe as SpringRef,ue as SpringValue,Bs as Trail,Ys as Transition,mt as config,Ui as createInterpolator,Li as easings,de as inferTo,pi as interpolate,ui as to,di as update,wr as useChain,Cs as useInView,Ei as useIsomorphicLayoutEffect,wi as useReducedMotion,Ps as useResize,hs as useScroll,W as useSpring,Xo as useSpringRef,Wo as useSpringValue,He as useSprings,Qt as useTrail,Gt as useTransition};
//# sourceMappingURL=react-spring_core.modern.production.min.mjs.map